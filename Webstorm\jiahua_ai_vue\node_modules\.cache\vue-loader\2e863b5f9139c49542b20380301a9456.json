{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue?vue&type=template&id=f28d94aa&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "mtime": 1753772362285}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n<div class=\"project-detail-app\">\n  <!-- 项目信息头部 -->\n  <div class=\"project-header\">\n    <div class=\"header-content\">\n      <div class=\"project-info\">\n        <button class=\"back-btn\" @click=\"goBack\">\n          ← 返回项目列表\n        </button>\n        <h1 class=\"project-title\">{{ projectInfo.projectName }}</h1>\n        <div class=\"project-meta\">\n          <span class=\"project-mode\">{{ getModeText(projectInfo.mode) }}</span>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <button class=\"btn-secondary\" v-if=\"projectInfo.mode==='hours'\" @click=\"viewProjectStats\">工时统计</button>\n        <button class=\"btn-primary\" v-if=\"userRole==='admin'\" @click=\"handleAddTask\">新建工单</button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 搜索筛选 -->\n  <div class=\"filter-section\">\n    <div class=\"filter-content\">\n      <div class=\"filter-row\">\n        <div class=\"filter-group\" v-if=\"userRole === 'admin' && activeTab === 'all'\">\n          <label>处理人</label>\n          <a-select v-model=\"queryParam.handling\" placeholder=\"请选择处理人\">\n            <a-select-option v-for=\"d in projectMemberList\" :key=\"d.id\" :value=\"d.user_id\">\n              {{ d.realname }}\n            </a-select-option>\n          </a-select>\n        </div>\n\n        <div class=\"filter-group\">\n          <label>状态</label>\n          <a-select \n            v-model=\"queryParam.workStatus\" \n            placeholder=\"请选择状态\" \n            mode=\"multiple\"\n            class=\"multi-select-wrap\">\n            <a-select-option value=\"pending\">待处理</a-select-option>\n            <a-select-option value=\"working\">进行中</a-select-option>\n            <a-select-option value=\"completed\">待审核</a-select-option>\n            <a-select-option value=\"approved\">已审核</a-select-option>\n            <a-select-option value=\"rejected\">已驳回</a-select-option>\n          </a-select>\n        </div>\n\n        <div class=\"filter-actions\">\n          <button class=\"btn-primary\" @click=\"searchQuery\">搜索</button>\n          <button class=\"btn-ghost\" @click=\"searchReset\">重置</button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 标签页切换 -->\n  <div class=\"tabs-container\">\n    <div class=\"tabs-header\">\n      <div class=\"tabs-left\">\n        <button\n          v-for=\"tab in tabs[this.userRole]\"\n          :key=\"tab.value\"\n          :class=\"['tab-btn', { active: activeTab === tab.value }]\"\n          @click=\"switchTab(tab.value)\"\n        >\n          {{ tab.label }}\n          <span class=\"tab-count\" v-if=\"tab.value === 'pool'\">{{ poolTotal }}</span>\n          <span class=\"tab-count\" v-if=\"tab.value === 'my'\">{{ myTotal }}</span>\n          <span class=\"tab-count\" v-if=\"tab.value === 'all'\">{{ allTotal }}</span>\n          <span class=\"tab-count\" v-if=\"tab.value === 'audit'\">{{ auditTotal }}</span>\n        </button>\n      </div>\n\n      <!-- 视图切换按钮 - 仅在排期模式和全部工单标签页显示 -->\n      <div class=\"view-switcher\" v-if=\"projectInfo.mode === 'timespan' && (activeTab === 'all' || activeTab === 'my')\">\n        <button\n          :class=\"['view-btn', { active: currentView === 'table' }]\"\n          @click=\"switchView('table')\"\n          title=\"表格视图\"\n        >\n          <i class=\"icon-table\">表格</i>\n        </button>\n        <button\n          :class=\"['view-btn', { active: currentView === 'calendar' }]\"\n          @click=\"switchView('calendar')\"\n          title=\"月历视图\"\n        >\n          <i class=\"icon-calendar\">月历</i>\n        </button>\n        <button\n          :class=\"['view-btn', { active: currentView === 'daily' }]\"\n          @click=\"switchView('daily')\"\n          title=\"按天视图\"\n        >\n          <i class=\"icon-daily\">按天</i>\n        </button>\n        <button\n          :class=\"['view-btn', { active: currentView === 'gantt' }]\"\n          @click=\"switchView('gantt')\"\n          title=\"甘特图\"\n        >\n          <i class=\"icon-gantt\">甘特图</i>\n        </button>\n      </div>\n    </div>\n\n    <!-- 表格视图 -->\n    <div class=\"table-container\" v-show=\"currentView === 'table'\">\n      <a-spin :spinning=\"loading\" class=\"table-loading\">\n        <a-table\n          ref=\"table\"\n          size=\"middle\"\n          bordered\n          :rowKey=\"record => record.id\"\n          :columns=\"dataColumns[projectInfo.mode]\"\n          :dataSource=\"dataSource\"\n          :pagination=\"ipagination\"\n          class=\"enhanced-table\"\n          @change=\"handleTableChange\"\n        >\n          <template slot=\"prioritySlot\" slot-scope=\"text\">\n            <span class=\"priority-tag\" :class=\"'priority-' + text\">{{ getPriorityText(text) }}</span>\n          </template>\n\n          <template slot=\"workHourSlot\" slot-scope=\"text\">\n            <span class=\"work-hour\">{{ text }}h</span>\n          </template>\n\n          <template slot=\"workStatus\" slot-scope=\"text\">\n            <span class=\"status-tag\" :class=\"'status-' + text\">{{ getWorkStatusText(text) }}</span>\n          </template>\n\n          <template slot=\"plannedTimeSlot\" slot-scope=\"text, record\">\n            <div class=\"time-range\">\n              <div class=\"time-start\">{{ record.startTime }}</div>\n              <div class=\"time-divider\">至</div>\n              <div class=\"time-end\">{{ record.endTime }}</div>\n            </div>\n          </template>\n\n          <template slot=\"reviewTime\" slot-scope=\"text, record\">\n            <span v-if=\"record.workStatus === 'approved'\" class=\"status-tag\" :class=\"'status-' + text\">{{ text }}</span>\n          </template>\n\n          <!-- 操作列 -->\n          <template slot=\"action\" slot-scope=\"text, record\">\n            <div class=\"action-buttons\">\n              <a v-if=\"activeTab === 'pool'\" @click=\"claimTask(record)\" class=\"action-link claim\">领取</a>\n\n              <a v-if=\"activeTab === 'my' && record.workStatus === 'rejected'\"\n                 @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link restart\">重新开始</a>\n              <a v-if=\"activeTab === 'my' && record.workStatus === 'pending'\"\n                 @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link start\">开始</a>\n              <a v-if=\"activeTab === 'my' && record.workStatus === 'working'\"\n                 @click=\"showFinishModal(record)\" class=\"action-link complete\">结束</a>\n\n              <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '1')\" class=\"action-link approve\">通过</a>\n              <a-divider v-if=\"activeTab === 'audit'\" type=\"vertical\" class=\"action-divider\"/>\n              <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '2')\" class=\"action-link reject\">拒绝</a>\n\n              <a-divider v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" type=\"vertical\" class=\"action-divider\"/>\n              <a v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" @click=\"taskEdit(record)\" class=\"action-link edit\">编辑</a>\n\n              <a v-if=\"userRole === 'admin' && (activeTab === 'all' && record.workStatus === 'pending')\" @click=\"taskEdit2(record)\" class=\"action-link edit\">转交负责人</a>\n\n              <a-divider v-if=\"(activeTab !== 'all' && record.workStatus !== 'approved' && (record.workStatus !== 'completed' || activeTab === 'audit'))\" type=\"vertical\" class=\"action-divider\"/>\n              <a @click=\"viewTaskDetail(record)\" class=\"action-link detail\">详情</a>\n            </div>\n          </template>\n        </a-table>\n      </a-spin>\n    </div>\n\n    <!-- 月历视图 -->\n    <div class=\"calendar-container\" v-show=\"currentView === 'calendar'\">\n      <div class=\"calendar-header\">\n        <button class=\"calendar-nav-btn\" @click=\"previousMonth\">\n          <i class=\"icon-prev\">‹</i>\n        </button>\n        <h3 class=\"calendar-title\">{{ currentMonth.format('YYYY年 MM月') }}</h3>\n        <button class=\"calendar-nav-btn\" @click=\"nextMonth\">\n          <i class=\"icon-next\">›</i>\n        </button>\n      </div>\n\n      <div class=\"calendar-grid\">\n        <div class=\"calendar-weekdays\">\n          <div class=\"weekday\" v-for=\"day in weekdays\" :key=\"day\">{{ day }}</div>\n        </div>\n\n        <div class=\"calendar-days\">\n          <div\n            v-for=\"day in calendarDays\"\n            :key=\"day.date\"\n            :class=\"['calendar-day', {\n              'other-month': !day.isCurrentMonth,\n              'today': day.isToday,\n              'has-tasks': day.tasks.length > 0\n            }]\"\n          >\n            <div class=\"day-number\">{{ day.dayNumber }}</div>\n            <div class=\"day-tasks\">\n              <div\n                v-for=\"task in day.tasks.slice(0, 3)\"\n                :key=\"task.id\"\n                :class=\"['task-item', 'status-' + task.workStatus]\"\n                @click=\"viewTaskDetail(task)\"\n                :title=\"task.ordername\"\n              >\n                <span class=\"task-name\">{{ task.ordername }}</span>\n                <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5) }}</span>\n              </div>\n              <div v-if=\"day.tasks.length > 3\" class=\"more-tasks\" @click=\"showDayTasks(day)\">\n                +{{ day.tasks.length - 3 }} 更多\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 甘特图视图 -->\n    <div class=\"gantt-container\" v-show=\"currentView === 'gantt'\">\n      <div class=\"gantt-header\">\n        <div class=\"gantt-controls\">\n          <button class=\"gantt-nav-btn\" @click=\"previousGanttPeriod\">\n            <i class=\"icon-prev\">‹</i>\n          </button>\n          <span class=\"gantt-period\">{{ ganttPeriod.start.format('MM/DD') }} - {{ ganttPeriod.end.format('MM/DD') }}</span>\n          <button class=\"gantt-nav-btn\" @click=\"nextGanttPeriod\">\n            <i class=\"icon-next\">›</i>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"gantt-content\">\n        <div class=\"gantt-timeline\">\n          <div class=\"timeline-header\">\n            <div class=\"task-header\">工单</div>\n            <div class=\"dates-header\">\n              <div\n                v-for=\"date in ganttDates\"\n                :key=\"date.format('YYYY-MM-DD')\"\n                :class=\"['date-cell', { 'today': date.isSame(moment(), 'day') }]\"\n              >\n                <div class=\"date-day\">{{ date.format('DD') }}</div>\n                <div class=\"date-weekday\">{{ date.format('ddd') }}</div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"timeline-body\">\n            <div\n              v-for=\"task in ganttTasks\"\n              :key=\"task.id\"\n              class=\"gantt-row\"\n            >\n              <div class=\"task-info\">\n                <div class=\"task-name\" :title=\"task.ordername\">{{ task.ordername }}</div>\n                <div class=\"task-meta\">\n                  <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5)}}</span>\n                  <span :class=\"['task-status', 'status-' + task.workStatus]\">\n                    {{ getWorkStatusText(task.workStatus) }}\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"gantt-bars\">\n                <div\n                  :class=\"['gantt-bar', 'status-' + task.workStatus]\"\n                  :style=\"getGanttBarStyle(task)\"\n                  @click=\"viewTaskDetail(task)\"\n                  :title=\"`${task.ordername} (${task.startTime} - ${task.endTime})`\"\n                >\n                  <div class=\"bar-content\">\n                    <span class=\"bar-text\">{{ task.ordername }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 按天视图 -->\n    <div class=\"daily-container\" v-show=\"currentView === 'daily'\">\n      <div class=\"daily-header\">\n        <div class=\"daily-controls\">\n          <button class=\"daily-nav-btn\" @click=\"previousDay\">\n            <i class=\"icon-prev\">‹</i>\n          </button>\n          <div class=\"date-picker-wrapper\">\n            <a-date-picker\n              v-model=\"currentDay\"\n              :format=\"'YYYY年MM月DD日'\"\n              :allowClear=\"false\"\n              @change=\"onDayChange\"\n              class=\"daily-date-picker\"\n            />\n          </div>\n          <button class=\"daily-nav-btn\" @click=\"nextDay\">\n            <i class=\"icon-next\">›</i>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"daily-content\">\n        <!-- 跨日期工单展示区域 -->\n        <div class=\"cross-date-section\" v-if=\"getCrossDayTasksForCurrentDay().length > 0\">\n          <div class=\"cross-date-header\">\n            <h3 class=\"cross-date-title\">\n              <span class=\"title-icon\">📅</span>\n              跨日期工单\n              <span class=\"task-count\">{{ getCrossDayTasksForCurrentDay().length }}</span>\n            </h3>\n          </div>\n          <div class=\"cross-date-timeline\">\n            <div\n              v-for=\"(task, index) in getCrossDayTasksForCurrentDay()\"\n              :key=\"task.id\"\n              :class=\"['cross-date-task', getTaskColorClass(task, index)]\"\n              :style=\"getCrossDayTaskStyle(task, index)\"\n              @click=\"viewTaskDetail(task)\"\n            >\n              <div class=\"cross-task-content\">\n                <div class=\"cross-task-header\">\n                  <div class=\"cross-task-title\">{{ task.ordername }}</div>\n                  <div :class=\"['cross-task-status', 'status-' + task.workStatus]\">\n                    {{ getWorkStatusText(task.workStatus) }}\n                  </div>\n                </div>\n                <div class=\"cross-task-details\">\n                  <div class=\"cross-task-handler\">\n                    <i class=\"icon-user\">👤</i>\n                    {{ task.handling }}\n                  </div>\n                  <div class=\"cross-task-time\">\n                    <i class=\"icon-time\">⏰</i>\n                    {{ formatCrossDayTaskTime(task) }}\n                  </div>\n                  <div class=\"cross-task-duration\">\n                    <i class=\"icon-duration\">⏱️</i>\n                    {{ getCrossDayTaskDuration(task) }}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 时间轴容器 -->\n        <div class=\"timeline-container\">\n          <div class=\"timeline-hours\">\n            <div\n              v-for=\"hour in timelineHours\"\n              :key=\"hour\"\n              class=\"hour-slot\"\n            >\n              <!-- 美观的小时标签 -->\n              <div class=\"hour-label\">\n                <div class=\"hour-text\">{{ hour }}:00</div>\n              </div>\n\n              <!-- 时间线区域 -->\n              <div class=\"hour-line\">\n                <!-- 半小时刻度线 -->\n                <div class=\"half-hour-line\"></div>\n\n                <!-- 15分钟刻度线 -->\n                <div class=\"quarter-hour-line\" style=\"top: 25%\"></div>\n                <div class=\"quarter-hour-line\" style=\"top: 75%\"></div>\n              </div>\n\n              <!-- 工单展示区域 -->\n              <div class=\"task-slots\">\n                <div\n                  v-for=\"task in getTasksForHour(hour)\"\n                  :key=\"task.id\"\n                  :class=\"['task-card', getTaskColorClass(task)]\"\n                  :style=\"getTaskCardStyle(task)\"\n                  @click=\"viewTaskDetail(task)\"\n                >\n                  <div class=\"task-card-content\">\n                    <div class=\"task-title\">{{ task.ordername }}</div>\n                    <div class=\"task-meta\">\n                      <div class=\"task-handler\">\n                        <i class=\"icon-user\">👤</i>\n                        {{ task.handling }}\n                      </div>\n                      <div class=\"task-time\">\n                        <i class=\"icon-time\">⏰</i>\n                        {{ formatTaskTime(task) }}\n                      </div>\n                    </div>\n                    <div :class=\"['task-status', 'status-' + task.workStatus]\">\n                      {{ getWorkStatusText(task.workStatus) }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 日期详情弹窗 -->\n  <a-modal\n    v-model=\"dayTasksVisible\"\n    :title=\"`${selectedDay ? selectedDay.date : ''} 的工单`\"\n    :footer=\"null\"\n    width=\"800px\"\n    class=\"day-tasks-modal\"\n  >\n    <div class=\"day-tasks-list\">\n      <div\n        v-for=\"task in selectedDayTasks\"\n        :key=\"task.id\"\n        class=\"day-task-item\"\n        @click=\"viewTaskDetail(task)\"\n      >\n        <div class=\"task-main\">\n          <h4 class=\"task-title\">{{ task.ordername }}</h4>\n          <div class=\"task-info-row\">\n            <span class=\"task-handler\">处理人: {{ task.handling }}</span>\n            <span :class=\"['task-status', 'status-' + task.workStatus]\">\n              {{ getWorkStatusText(task.workStatus) }}\n            </span>\n          </div>\n          <div class=\"task-time\">\n            {{ task.startTime }} - {{ task.endTime }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 其他弹窗 -->\n  <div>\n    <a-modal\n      title=\"工单审核\"\n      :visible=\"isShowAudit\"\n      @ok=\"audit(null,'3')\"\n      @cancel=\"isShowAudit = false\"\n      class=\"audit-modal\"\n    >\n      <a-form-model>\n        <a-form-model-item label=\"审核意见\" :labelCol=\"{ span: 5 }\" :wrapperCol=\"{ span: 19 }\">\n          <a-textarea :rows=\"3\" v-model=\"auditOpinion\" placeholder=\"请输入审核意见\"></a-textarea>\n        </a-form-model-item>\n      </a-form-model>\n    </a-modal>\n  </div>\n\n  <!-- 工单弹窗 -->\n  <work-order-task-modal\n    ref=\"taskModal\"\n    :project-info=\"projectInfo\"\n    @ok=\"taskModalOk\"\n  ></work-order-task-modal>\n\n        <!-- 工单详情弹窗 -->\n  <task-detail-modal ref=\"taskDetailModal\"></task-detail-modal>\n\n  <!-- 工时统计弹窗 -->\n  <project-stats-modal ref=\"projectStatsModal\"></project-stats-modal>\n  \n  <!-- 工单完成弹窗 -->\n  <a-modal\n    title=\"完成工单\"\n    :visible=\"finishModalVisible\"\n    :confirmLoading=\"finishConfirmLoading\"\n    @cancel=\"finishModalVisible = false\"\n    :footer=\"null\"\n    width=\"30%\"\n    class=\"finish-modal\"\n  >\n    <a-spin :spinning=\"finishConfirmLoading\">\n      <a-form-model :model=\"finishForm\" :label-col=\"{ span: 4 }\" :wrapper-col=\"{ span: 20 }\">\n        <a-form-model-item label=\"上传附件\">\n          <div class=\"upload-container\" @dragover.prevent @drop.prevent=\"handleDrop\">\n            <a-upload\n              name=\"file\"\n              :action=\"uploadAction\"\n              :headers=\"uploadHeaders\"\n              :file-list=\"finishForm.fileList\"\n              @change=\"handleFileChange\"\n              :multiple=\"true\"\n              :show-upload-list=\"true\"\n              :before-upload=\"beforeUpload\"\n            >\n              <a-button type=\"dashed\">\n                <a-icon type=\"upload\" /> 点击上传\n              </a-button>\n              <div class=\"upload-tips\">\n                <span>支持：点击上传、拖拽上传（附件非必选）</span>\n              </div>\n            </a-upload>\n          </div>\n        </a-form-model-item>\n        \n        <a-form-model-item label=\"完成说明\">\n          <a-textarea \n            :rows=\"4\" \n            v-model=\"finishForm.finishDescribe\" \n            placeholder=\"请输入完成说明（非必填）\"\n          />\n        </a-form-model-item>\n      </a-form-model>\n      \n      <div class=\"modal-footer\">\n        <a-button @click=\"finishModalVisible = false\">取消</a-button>\n        <a-button type=\"primary\" @click=\"handleFinishOk\" :loading=\"finishConfirmLoading\">立即结束</a-button>\n      </div>\n    </a-spin>\n  </a-modal>\n</div>\n", null]}