{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue?vue&type=template&id=f28d94aa&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "mtime": 1753772672737}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"project-detail-app\" },\n    [\n      _c(\"div\", { staticClass: \"project-header\" }, [\n        _c(\"div\", { staticClass: \"header-content\" }, [\n          _c(\"div\", { staticClass: \"project-info\" }, [\n            _c(\n              \"button\",\n              { staticClass: \"back-btn\", on: { click: _vm.goBack } },\n              [_vm._v(\"\\n          ← 返回项目列表\\n        \")]\n            ),\n            _c(\"h1\", { staticClass: \"project-title\" }, [\n              _vm._v(_vm._s(_vm.projectInfo.projectName))\n            ]),\n            _c(\"div\", { staticClass: \"project-meta\" }, [\n              _c(\"span\", { staticClass: \"project-mode\" }, [\n                _vm._v(_vm._s(_vm.getModeText(_vm.projectInfo.mode)))\n              ])\n            ])\n          ]),\n          _c(\"div\", { staticClass: \"header-actions\" }, [\n            _vm.projectInfo.mode === \"hours\"\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"btn-secondary\",\n                    on: { click: _vm.viewProjectStats }\n                  },\n                  [_vm._v(\"工时统计\")]\n                )\n              : _vm._e(),\n            _vm.userRole === \"admin\"\n              ? _c(\n                  \"button\",\n                  {\n                    staticClass: \"btn-primary\",\n                    on: { click: _vm.handleAddTask }\n                  },\n                  [_vm._v(\"新建工单\")]\n                )\n              : _vm._e()\n          ])\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"filter-section\" }, [\n        _c(\"div\", { staticClass: \"filter-content\" }, [\n          _c(\"div\", { staticClass: \"filter-row\" }, [\n            _vm.userRole === \"admin\" && _vm.activeTab === \"all\"\n              ? _c(\n                  \"div\",\n                  { staticClass: \"filter-group\" },\n                  [\n                    _c(\"label\", [_vm._v(\"处理人\")]),\n                    _c(\n                      \"a-select\",\n                      {\n                        attrs: { placeholder: \"请选择处理人\" },\n                        model: {\n                          value: _vm.queryParam.handling,\n                          callback: function($$v) {\n                            _vm.$set(_vm.queryParam, \"handling\", $$v)\n                          },\n                          expression: \"queryParam.handling\"\n                        }\n                      },\n                      _vm._l(_vm.projectMemberList, function(d) {\n                        return _c(\n                          \"a-select-option\",\n                          { key: d.id, attrs: { value: d.user_id } },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(d.realname) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      }),\n                      1\n                    )\n                  ],\n                  1\n                )\n              : _vm._e(),\n            _c(\n              \"div\",\n              { staticClass: \"filter-group\" },\n              [\n                _c(\"label\", [_vm._v(\"状态\")]),\n                _c(\n                  \"a-select\",\n                  {\n                    staticClass: \"multi-select-wrap\",\n                    attrs: { placeholder: \"请选择状态\", mode: \"multiple\" },\n                    model: {\n                      value: _vm.queryParam.workStatus,\n                      callback: function($$v) {\n                        _vm.$set(_vm.queryParam, \"workStatus\", $$v)\n                      },\n                      expression: \"queryParam.workStatus\"\n                    }\n                  },\n                  [\n                    _c(\"a-select-option\", { attrs: { value: \"pending\" } }, [\n                      _vm._v(\"待处理\")\n                    ]),\n                    _c(\"a-select-option\", { attrs: { value: \"working\" } }, [\n                      _vm._v(\"进行中\")\n                    ]),\n                    _c(\"a-select-option\", { attrs: { value: \"completed\" } }, [\n                      _vm._v(\"待审核\")\n                    ]),\n                    _c(\"a-select-option\", { attrs: { value: \"approved\" } }, [\n                      _vm._v(\"已审核\")\n                    ]),\n                    _c(\"a-select-option\", { attrs: { value: \"rejected\" } }, [\n                      _vm._v(\"已驳回\")\n                    ])\n                  ],\n                  1\n                )\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"filter-actions\" }, [\n              _c(\n                \"button\",\n                { staticClass: \"btn-primary\", on: { click: _vm.searchQuery } },\n                [_vm._v(\"搜索\")]\n              ),\n              _c(\n                \"button\",\n                { staticClass: \"btn-ghost\", on: { click: _vm.searchReset } },\n                [_vm._v(\"重置\")]\n              )\n            ])\n          ])\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"tabs-container\" }, [\n        _c(\"div\", { staticClass: \"tabs-header\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"tabs-left\" },\n            _vm._l(_vm.tabs[this.userRole], function(tab) {\n              return _c(\n                \"button\",\n                {\n                  key: tab.value,\n                  class: [\"tab-btn\", { active: _vm.activeTab === tab.value }],\n                  on: {\n                    click: function($event) {\n                      return _vm.switchTab(tab.value)\n                    }\n                  }\n                },\n                [\n                  _vm._v(\"\\n          \" + _vm._s(tab.label) + \"\\n          \"),\n                  tab.value === \"pool\"\n                    ? _c(\"span\", { staticClass: \"tab-count\" }, [\n                        _vm._v(_vm._s(_vm.poolTotal))\n                      ])\n                    : _vm._e(),\n                  tab.value === \"my\"\n                    ? _c(\"span\", { staticClass: \"tab-count\" }, [\n                        _vm._v(_vm._s(_vm.myTotal))\n                      ])\n                    : _vm._e(),\n                  tab.value === \"all\"\n                    ? _c(\"span\", { staticClass: \"tab-count\" }, [\n                        _vm._v(_vm._s(_vm.allTotal))\n                      ])\n                    : _vm._e(),\n                  tab.value === \"audit\"\n                    ? _c(\"span\", { staticClass: \"tab-count\" }, [\n                        _vm._v(_vm._s(_vm.auditTotal))\n                      ])\n                    : _vm._e()\n                ]\n              )\n            }),\n            0\n          ),\n          _vm.projectInfo.mode === \"timespan\" &&\n          (_vm.activeTab === \"all\" || _vm.activeTab === \"my\")\n            ? _c(\"div\", { staticClass: \"view-switcher\" }, [\n                _c(\n                  \"button\",\n                  {\n                    class: [\n                      \"view-btn\",\n                      { active: _vm.currentView === \"table\" }\n                    ],\n                    attrs: { title: \"表格视图\" },\n                    on: {\n                      click: function($event) {\n                        return _vm.switchView(\"table\")\n                      }\n                    }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-table\" }, [_vm._v(\"表格\")])]\n                ),\n                _c(\n                  \"button\",\n                  {\n                    class: [\n                      \"view-btn\",\n                      { active: _vm.currentView === \"calendar\" }\n                    ],\n                    attrs: { title: \"月历视图\" },\n                    on: {\n                      click: function($event) {\n                        return _vm.switchView(\"calendar\")\n                      }\n                    }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-calendar\" }, [_vm._v(\"月历\")])]\n                ),\n                _c(\n                  \"button\",\n                  {\n                    class: [\n                      \"view-btn\",\n                      { active: _vm.currentView === \"daily\" }\n                    ],\n                    attrs: { title: \"按天视图\" },\n                    on: {\n                      click: function($event) {\n                        return _vm.switchView(\"daily\")\n                      }\n                    }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-daily\" }, [_vm._v(\"按天\")])]\n                ),\n                _c(\n                  \"button\",\n                  {\n                    class: [\n                      \"view-btn\",\n                      { active: _vm.currentView === \"gantt\" }\n                    ],\n                    attrs: { title: \"甘特图\" },\n                    on: {\n                      click: function($event) {\n                        return _vm.switchView(\"gantt\")\n                      }\n                    }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-gantt\" }, [_vm._v(\"甘特图\")])]\n                )\n              ])\n            : _vm._e()\n        ]),\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentView === \"table\",\n                expression: \"currentView === 'table'\"\n              }\n            ],\n            staticClass: \"table-container\"\n          },\n          [\n            _c(\n              \"a-spin\",\n              {\n                staticClass: \"table-loading\",\n                attrs: { spinning: _vm.loading }\n              },\n              [\n                _c(\"a-table\", {\n                  ref: \"table\",\n                  staticClass: \"enhanced-table\",\n                  attrs: {\n                    size: \"middle\",\n                    bordered: \"\",\n                    rowKey: function(record) {\n                      return record.id\n                    },\n                    columns: _vm.dataColumns[_vm.projectInfo.mode],\n                    dataSource: _vm.dataSource,\n                    pagination: _vm.ipagination\n                  },\n                  on: { change: _vm.handleTableChange },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"prioritySlot\",\n                      fn: function(text) {\n                        return [\n                          _c(\n                            \"span\",\n                            {\n                              staticClass: \"priority-tag\",\n                              class: \"priority-\" + text\n                            },\n                            [_vm._v(_vm._s(_vm.getPriorityText(text)))]\n                          )\n                        ]\n                      }\n                    },\n                    {\n                      key: \"workHourSlot\",\n                      fn: function(text) {\n                        return [\n                          _c(\"span\", { staticClass: \"work-hour\" }, [\n                            _vm._v(_vm._s(text) + \"h\")\n                          ])\n                        ]\n                      }\n                    },\n                    {\n                      key: \"workStatus\",\n                      fn: function(text) {\n                        return [\n                          _c(\n                            \"span\",\n                            {\n                              staticClass: \"status-tag\",\n                              class: \"status-\" + text\n                            },\n                            [_vm._v(_vm._s(_vm.getWorkStatusText(text)))]\n                          )\n                        ]\n                      }\n                    },\n                    {\n                      key: \"plannedTimeSlot\",\n                      fn: function(text, record) {\n                        return [\n                          _c(\"div\", { staticClass: \"time-range\" }, [\n                            _c(\"div\", { staticClass: \"time-start\" }, [\n                              _vm._v(_vm._s(record.startTime))\n                            ]),\n                            _c(\"div\", { staticClass: \"time-divider\" }, [\n                              _vm._v(\"至\")\n                            ]),\n                            _c(\"div\", { staticClass: \"time-end\" }, [\n                              _vm._v(_vm._s(record.endTime))\n                            ])\n                          ])\n                        ]\n                      }\n                    },\n                    {\n                      key: \"reviewTime\",\n                      fn: function(text, record) {\n                        return [\n                          record.workStatus === \"approved\"\n                            ? _c(\n                                \"span\",\n                                {\n                                  staticClass: \"status-tag\",\n                                  class: \"status-\" + text\n                                },\n                                [_vm._v(_vm._s(text))]\n                              )\n                            : _vm._e()\n                        ]\n                      }\n                    },\n                    {\n                      key: \"action\",\n                      fn: function(text, record) {\n                        return [\n                          _c(\n                            \"div\",\n                            { staticClass: \"action-buttons\" },\n                            [\n                              _vm.activeTab === \"pool\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link claim\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.claimTask(record)\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"领取\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab === \"my\" &&\n                              record.workStatus === \"rejected\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link restart\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.updateWorkStatus(\n                                            record.id,\n                                            \"working\"\n                                          )\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"重新开始\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab === \"my\" &&\n                              record.workStatus === \"pending\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link start\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.updateWorkStatus(\n                                            record.id,\n                                            \"working\"\n                                          )\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"开始\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab === \"my\" &&\n                              record.workStatus === \"working\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link complete\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.showFinishModal(record)\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"结束\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab === \"audit\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link approve\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.audit(record.id, \"1\")\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"通过\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab === \"audit\"\n                                ? _c(\"a-divider\", {\n                                    staticClass: \"action-divider\",\n                                    attrs: { type: \"vertical\" }\n                                  })\n                                : _vm._e(),\n                              _vm.activeTab === \"audit\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link reject\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.audit(record.id, \"2\")\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"拒绝\")]\n                                  )\n                                : _vm._e(),\n                              record.initiatorId === _vm.currentUserId &&\n                              (_vm.activeTab === \"my\" ||\n                                _vm.activeTab === \"pool\") &&\n                              (record.workStatus === \"pending\" ||\n                                record.workStatus === \"rejected\")\n                                ? _c(\"a-divider\", {\n                                    staticClass: \"action-divider\",\n                                    attrs: { type: \"vertical\" }\n                                  })\n                                : _vm._e(),\n                              record.initiatorId === _vm.currentUserId &&\n                              (_vm.activeTab === \"my\" ||\n                                _vm.activeTab === \"pool\") &&\n                              (record.workStatus === \"pending\" ||\n                                record.workStatus === \"rejected\")\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link edit\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.taskEdit(record)\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"编辑\")]\n                                  )\n                                : _vm._e(),\n                              _vm.userRole === \"admin\" &&\n                              _vm.activeTab === \"all\" &&\n                                record.workStatus === \"pending\"\n                                ? _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"action-link edit\",\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.taskEdit2(record)\n                                        }\n                                      }\n                                    },\n                                    [_vm._v(\"转交负责人\")]\n                                  )\n                                : _vm._e(),\n                              _vm.activeTab !== \"all\" &&\n                              record.workStatus !== \"approved\" &&\n                              (record.workStatus !== \"completed\" ||\n                                _vm.activeTab === \"audit\")\n                                ? _c(\"a-divider\", {\n                                    staticClass: \"action-divider\",\n                                    attrs: { type: \"vertical\" }\n                                  })\n                                : _vm._e(),\n                              _c(\n                                \"a\",\n                                {\n                                  staticClass: \"action-link detail\",\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.viewTaskDetail(record)\n                                    }\n                                  }\n                                },\n                                [_vm._v(\"详情\")]\n                              )\n                            ],\n                            1\n                          )\n                        ]\n                      }\n                    }\n                  ])\n                })\n              ],\n              1\n            )\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentView === \"calendar\",\n                expression: \"currentView === 'calendar'\"\n              }\n            ],\n            staticClass: \"calendar-container\"\n          },\n          [\n            _c(\"div\", { staticClass: \"calendar-header\" }, [\n              _c(\n                \"button\",\n                {\n                  staticClass: \"calendar-nav-btn\",\n                  on: { click: _vm.previousMonth }\n                },\n                [_c(\"i\", { staticClass: \"icon-prev\" }, [_vm._v(\"‹\")])]\n              ),\n              _c(\"h3\", { staticClass: \"calendar-title\" }, [\n                _vm._v(_vm._s(_vm.currentMonth.format(\"YYYY年 MM月\")))\n              ]),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"calendar-nav-btn\",\n                  on: { click: _vm.nextMonth }\n                },\n                [_c(\"i\", { staticClass: \"icon-next\" }, [_vm._v(\"›\")])]\n              )\n            ]),\n            _c(\"div\", { staticClass: \"calendar-grid\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"calendar-weekdays\" },\n                _vm._l(_vm.weekdays, function(day) {\n                  return _c(\"div\", { key: day, staticClass: \"weekday\" }, [\n                    _vm._v(_vm._s(day))\n                  ])\n                }),\n                0\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"calendar-days\" },\n                _vm._l(_vm.calendarDays, function(day) {\n                  return _c(\n                    \"div\",\n                    {\n                      key: day.date,\n                      class: [\n                        \"calendar-day\",\n                        {\n                          \"other-month\": !day.isCurrentMonth,\n                          today: day.isToday,\n                          \"has-tasks\": day.tasks.length > 0\n                        }\n                      ]\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"day-number\" }, [\n                        _vm._v(_vm._s(day.dayNumber))\n                      ]),\n                      _c(\n                        \"div\",\n                        { staticClass: \"day-tasks\" },\n                        [\n                          _vm._l(day.tasks.slice(0, 3), function(task) {\n                            return _c(\n                              \"div\",\n                              {\n                                key: task.id,\n                                class: [\n                                  \"task-item\",\n                                  \"status-\" + task.workStatus\n                                ],\n                                attrs: { title: task.ordername },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.viewTaskDetail(task)\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"task-name\" }, [\n                                  _vm._v(_vm._s(task.ordername))\n                                ]),\n                                _c(\"span\", { staticClass: \"task-handler\" }, [\n                                  _vm._v(\n                                    _vm._s(\n                                      task.handling +\n                                        \" \" +\n                                        task.startTime.substr(11, 5) +\n                                        \"-\" +\n                                        task.endTime.substr(11, 5)\n                                    )\n                                  )\n                                ])\n                              ]\n                            )\n                          }),\n                          day.tasks.length > 3\n                            ? _c(\n                                \"div\",\n                                {\n                                  staticClass: \"more-tasks\",\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.showDayTasks(day)\n                                    }\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                +\" +\n                                      _vm._s(day.tasks.length - 3) +\n                                      \" 更多\\n              \"\n                                  )\n                                ]\n                              )\n                            : _vm._e()\n                        ],\n                        2\n                      )\n                    ]\n                  )\n                }),\n                0\n              )\n            ])\n          ]\n        ),\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentView === \"gantt\",\n                expression: \"currentView === 'gantt'\"\n              }\n            ],\n            staticClass: \"gantt-container\"\n          },\n          [\n            _c(\"div\", { staticClass: \"gantt-header\" }, [\n              _c(\"div\", { staticClass: \"gantt-controls\" }, [\n                _c(\n                  \"button\",\n                  {\n                    staticClass: \"gantt-nav-btn\",\n                    on: { click: _vm.previousGanttPeriod }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-prev\" }, [_vm._v(\"‹\")])]\n                ),\n                _c(\"span\", { staticClass: \"gantt-period\" }, [\n                  _vm._v(\n                    _vm._s(_vm.ganttPeriod.start.format(\"MM/DD\")) +\n                      \" - \" +\n                      _vm._s(_vm.ganttPeriod.end.format(\"MM/DD\"))\n                  )\n                ]),\n                _c(\n                  \"button\",\n                  {\n                    staticClass: \"gantt-nav-btn\",\n                    on: { click: _vm.nextGanttPeriod }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-next\" }, [_vm._v(\"›\")])]\n                )\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"gantt-content\" }, [\n              _c(\"div\", { staticClass: \"gantt-timeline\" }, [\n                _c(\"div\", { staticClass: \"timeline-header\" }, [\n                  _c(\"div\", { staticClass: \"task-header\" }, [_vm._v(\"工单\")]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"dates-header\" },\n                    _vm._l(_vm.ganttDates, function(date) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: date.format(\"YYYY-MM-DD\"),\n                          class: [\n                            \"date-cell\",\n                            { today: date.isSame(_vm.moment(), \"day\") }\n                          ]\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"date-day\" }, [\n                            _vm._v(_vm._s(date.format(\"DD\")))\n                          ]),\n                          _c(\"div\", { staticClass: \"date-weekday\" }, [\n                            _vm._v(_vm._s(date.format(\"ddd\")))\n                          ])\n                        ]\n                      )\n                    }),\n                    0\n                  )\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"timeline-body\" },\n                  _vm._l(_vm.ganttTasks, function(task) {\n                    return _c(\n                      \"div\",\n                      { key: task.id, staticClass: \"gantt-row\" },\n                      [\n                        _c(\"div\", { staticClass: \"task-info\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"task-name\",\n                              attrs: { title: task.ordername }\n                            },\n                            [_vm._v(_vm._s(task.ordername))]\n                          ),\n                          _c(\"div\", { staticClass: \"task-meta\" }, [\n                            _c(\"span\", { staticClass: \"task-handler\" }, [\n                              _vm._v(\n                                _vm._s(\n                                  task.handling +\n                                    \" \" +\n                                    task.startTime.substr(11, 5) +\n                                    \"-\" +\n                                    task.endTime.substr(11, 5)\n                                )\n                              )\n                            ]),\n                            _c(\n                              \"span\",\n                              {\n                                class: [\n                                  \"task-status\",\n                                  \"status-\" + task.workStatus\n                                ]\n                              },\n                              [\n                                _vm._v(\n                                  \"\\n                    \" +\n                                    _vm._s(\n                                      _vm.getWorkStatusText(task.workStatus)\n                                    ) +\n                                    \"\\n                  \"\n                                )\n                              ]\n                            )\n                          ])\n                        ]),\n                        _c(\"div\", { staticClass: \"gantt-bars\" }, [\n                          _c(\n                            \"div\",\n                            {\n                              class: [\"gantt-bar\", \"status-\" + task.workStatus],\n                              style: _vm.getGanttBarStyle(task),\n                              attrs: {\n                                title:\n                                  task.ordername +\n                                  \" (\" +\n                                  task.startTime +\n                                  \" - \" +\n                                  task.endTime +\n                                  \")\"\n                              },\n                              on: {\n                                click: function($event) {\n                                  return _vm.viewTaskDetail(task)\n                                }\n                              }\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"bar-content\" }, [\n                                _c(\"span\", { staticClass: \"bar-text\" }, [\n                                  _vm._v(_vm._s(task.ordername))\n                                ])\n                              ])\n                            ]\n                          )\n                        ])\n                      ]\n                    )\n                  }),\n                  0\n                )\n              ])\n            ])\n          ]\n        ),\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentView === \"daily\",\n                expression: \"currentView === 'daily'\"\n              }\n            ],\n            staticClass: \"daily-container\"\n          },\n          [\n            _c(\"div\", { staticClass: \"daily-header\" }, [\n              _c(\"div\", { staticClass: \"daily-controls\" }, [\n                _c(\n                  \"button\",\n                  {\n                    staticClass: \"daily-nav-btn\",\n                    on: { click: _vm.previousDay }\n                  },\n                  [_c(\"i\", { staticClass: \"icon-prev\" }, [_vm._v(\"‹\")])]\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"date-picker-wrapper\" },\n                  [\n                    _c(\"a-date-picker\", {\n                      staticClass: \"daily-date-picker\",\n                      attrs: { format: \"YYYY年MM月DD日\", allowClear: false },\n                      on: { change: _vm.onDayChange },\n                      model: {\n                        value: _vm.currentDay,\n                        callback: function($$v) {\n                          _vm.currentDay = $$v\n                        },\n                        expression: \"currentDay\"\n                      }\n                    })\n                  ],\n                  1\n                ),\n                _c(\n                  \"button\",\n                  { staticClass: \"daily-nav-btn\", on: { click: _vm.nextDay } },\n                  [_c(\"i\", { staticClass: \"icon-next\" }, [_vm._v(\"›\")])]\n                )\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"daily-content\" }, [\n              _vm.getCrossDayTasksForCurrentDay().length > 0\n                ? _c(\"div\", { staticClass: \"cross-date-section\" }, [\n                    _c(\"div\", { staticClass: \"cross-date-header\" }, [\n                      _c(\"h3\", { staticClass: \"cross-date-title\" }, [\n                        _c(\"span\", { staticClass: \"title-icon\" }, [\n                          _vm._v(\"📅\")\n                        ]),\n                        _vm._v(\"\\n              跨日期工单\\n              \"),\n                        _c(\"span\", { staticClass: \"task-count\" }, [\n                          _vm._v(\n                            _vm._s(_vm.getCrossDayTasksForCurrentDay().length)\n                          )\n                        ])\n                      ])\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"cross-date-timeline\" },\n                      _vm._l(_vm.getCrossDayTasksForCurrentDay(), function(\n                        task,\n                        index\n                      ) {\n                        return _c(\n                          \"div\",\n                          {\n                            key: task.id,\n                            class: [\n                              \"cross-date-task\",\n                              _vm.getTaskColorClass(task, index)\n                            ],\n                            style: _vm.getCrossDayTaskStyle(task, index),\n                            on: {\n                              click: function($event) {\n                                return _vm.viewTaskDetail(task)\n                              }\n                            }\n                          },\n                          [\n                            _c(\"div\", { staticClass: \"cross-task-content\" }, [\n                              _c(\"div\", { staticClass: \"cross-task-header\" }, [\n                                _c(\"div\", { staticClass: \"cross-task-title\" }, [\n                                  _vm._v(_vm._s(task.ordername))\n                                ]),\n                                _c(\n                                  \"div\",\n                                  {\n                                    class: [\n                                      \"cross-task-status\",\n                                      \"status-\" + task.workStatus\n                                    ]\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                    \" +\n                                        _vm._s(\n                                          _vm.getWorkStatusText(task.workStatus)\n                                        ) +\n                                        \"\\n                  \"\n                                    )\n                                  ]\n                                )\n                              ]),\n                              _c(\"div\", { staticClass: \"cross-task-details\" }, [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"cross-task-handler\" },\n                                  [\n                                    _c(\"i\", { staticClass: \"icon-user\" }, [\n                                      _vm._v(\"👤\")\n                                    ]),\n                                    _vm._v(\n                                      \"\\n                    \" +\n                                        _vm._s(task.handling) +\n                                        \"\\n                  \"\n                                    )\n                                  ]\n                                ),\n                                _c(\"div\", { staticClass: \"cross-task-time\" }, [\n                                  _c(\"i\", { staticClass: \"icon-time\" }, [\n                                    _vm._v(\"⏰\")\n                                  ]),\n                                  _vm._v(\n                                    \"\\n                    \" +\n                                      _vm._s(_vm.formatCrossDayTaskTime(task)) +\n                                      \"\\n                  \"\n                                  )\n                                ]),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"cross-task-duration\" },\n                                  [\n                                    _c(\"i\", { staticClass: \"icon-duration\" }, [\n                                      _vm._v(\"⏱️\")\n                                    ]),\n                                    _vm._v(\n                                      \"\\n                    \" +\n                                        _vm._s(\n                                          _vm.getCrossDayTaskDuration(task)\n                                        ) +\n                                        \"\\n                  \"\n                                    )\n                                  ]\n                                )\n                              ])\n                            ])\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  ])\n                : _vm._e(),\n              _c(\"div\", { staticClass: \"timeline-container\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"timeline-hours\" },\n                  _vm._l(_vm.timelineHours, function(hour) {\n                    return _c(\"div\", { key: hour, staticClass: \"hour-slot\" }, [\n                      _c(\"div\", { staticClass: \"hour-label\" }, [\n                        _c(\"div\", { staticClass: \"hour-text\" }, [\n                          _vm._v(_vm._s(hour) + \":00\")\n                        ])\n                      ]),\n                      _vm._m(0, true),\n                      _c(\n                        \"div\",\n                        { staticClass: \"task-slots\" },\n                        _vm._l(_vm.getTasksForHour(hour), function(task) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: task.id,\n                              class: [\"task-card\", _vm.getTaskColorClass(task)],\n                              style: _vm.getTaskCardStyle(task),\n                              on: {\n                                click: function($event) {\n                                  return _vm.viewTaskDetail(task)\n                                }\n                              }\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  class: [\n                                    \"task-status-corner\",\n                                    \"status-\" + task.workStatus\n                                  ]\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                    \" +\n                                      _vm._s(\n                                        _vm.getWorkStatusText(task.workStatus)\n                                      ) +\n                                      \"\\n                  \"\n                                  )\n                                ]\n                              ),\n                              _c(\"div\", { staticClass: \"task-card-content\" }, [\n                                _c(\"div\", { staticClass: \"task-title\" }, [\n                                  _vm._v(_vm._s(task.ordername))\n                                ]),\n                                _c(\"div\", { staticClass: \"task-meta\" }, [\n                                  _c(\"div\", { staticClass: \"task-handler\" }, [\n                                    _c(\"i\", { staticClass: \"icon-user\" }, [\n                                      _vm._v(\"👤\")\n                                    ]),\n                                    _vm._v(\n                                      \"\\n                        \" +\n                                        _vm._s(task.handling) +\n                                        \"\\n                      \"\n                                    )\n                                  ]),\n                                  _c(\"div\", { staticClass: \"task-time\" }, [\n                                    _c(\"i\", { staticClass: \"icon-time\" }, [\n                                      _vm._v(\"⏰\")\n                                    ]),\n                                    _vm._v(\n                                      \"\\n                        \" +\n                                        _vm._s(_vm.formatTaskTime(task)) +\n                                        \"\\n                      \"\n                                    )\n                                  ])\n                                ])\n                              ])\n                            ]\n                          )\n                        }),\n                        0\n                      )\n                    ])\n                  }),\n                  0\n                )\n              ])\n            ])\n          ]\n        )\n      ]),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"day-tasks-modal\",\n          attrs: {\n            title: (_vm.selectedDay ? _vm.selectedDay.date : \"\") + \" 的工单\",\n            footer: null,\n            width: \"800px\"\n          },\n          model: {\n            value: _vm.dayTasksVisible,\n            callback: function($$v) {\n              _vm.dayTasksVisible = $$v\n            },\n            expression: \"dayTasksVisible\"\n          }\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"day-tasks-list\" },\n            _vm._l(_vm.selectedDayTasks, function(task) {\n              return _c(\n                \"div\",\n                {\n                  key: task.id,\n                  staticClass: \"day-task-item\",\n                  on: {\n                    click: function($event) {\n                      return _vm.viewTaskDetail(task)\n                    }\n                  }\n                },\n                [\n                  _c(\"div\", { staticClass: \"task-main\" }, [\n                    _c(\"h4\", { staticClass: \"task-title\" }, [\n                      _vm._v(_vm._s(task.ordername))\n                    ]),\n                    _c(\"div\", { staticClass: \"task-info-row\" }, [\n                      _c(\"span\", { staticClass: \"task-handler\" }, [\n                        _vm._v(\"处理人: \" + _vm._s(task.handling))\n                      ]),\n                      _c(\n                        \"span\",\n                        { class: [\"task-status\", \"status-\" + task.workStatus] },\n                        [\n                          _vm._v(\n                            \"\\n              \" +\n                              _vm._s(_vm.getWorkStatusText(task.workStatus)) +\n                              \"\\n            \"\n                          )\n                        ]\n                      )\n                    ]),\n                    _c(\"div\", { staticClass: \"task-time\" }, [\n                      _vm._v(\n                        \"\\n            \" +\n                          _vm._s(task.startTime) +\n                          \" - \" +\n                          _vm._s(task.endTime) +\n                          \"\\n          \"\n                      )\n                    ])\n                  ])\n                ]\n              )\n            }),\n            0\n          )\n        ]\n      ),\n      _c(\n        \"div\",\n        [\n          _c(\n            \"a-modal\",\n            {\n              staticClass: \"audit-modal\",\n              attrs: { title: \"工单审核\", visible: _vm.isShowAudit },\n              on: {\n                ok: function($event) {\n                  return _vm.audit(null, \"3\")\n                },\n                cancel: function($event) {\n                  _vm.isShowAudit = false\n                }\n              }\n            },\n            [\n              _c(\n                \"a-form-model\",\n                [\n                  _c(\n                    \"a-form-model-item\",\n                    {\n                      attrs: {\n                        label: \"审核意见\",\n                        labelCol: { span: 5 },\n                        wrapperCol: { span: 19 }\n                      }\n                    },\n                    [\n                      _c(\"a-textarea\", {\n                        attrs: { rows: 3, placeholder: \"请输入审核意见\" },\n                        model: {\n                          value: _vm.auditOpinion,\n                          callback: function($$v) {\n                            _vm.auditOpinion = $$v\n                          },\n                          expression: \"auditOpinion\"\n                        }\n                      })\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\"work-order-task-modal\", {\n        ref: \"taskModal\",\n        attrs: { \"project-info\": _vm.projectInfo },\n        on: { ok: _vm.taskModalOk }\n      }),\n      _c(\"task-detail-modal\", { ref: \"taskDetailModal\" }),\n      _c(\"project-stats-modal\", { ref: \"projectStatsModal\" }),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"finish-modal\",\n          attrs: {\n            title: \"完成工单\",\n            visible: _vm.finishModalVisible,\n            confirmLoading: _vm.finishConfirmLoading,\n            footer: null,\n            width: \"30%\"\n          },\n          on: {\n            cancel: function($event) {\n              _vm.finishModalVisible = false\n            }\n          }\n        },\n        [\n          _c(\n            \"a-spin\",\n            { attrs: { spinning: _vm.finishConfirmLoading } },\n            [\n              _c(\n                \"a-form-model\",\n                {\n                  attrs: {\n                    model: _vm.finishForm,\n                    \"label-col\": { span: 4 },\n                    \"wrapper-col\": { span: 20 }\n                  }\n                },\n                [\n                  _c(\"a-form-model-item\", { attrs: { label: \"上传附件\" } }, [\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"upload-container\",\n                        on: {\n                          dragover: function($event) {\n                            $event.preventDefault()\n                          },\n                          drop: function($event) {\n                            $event.preventDefault()\n                            return _vm.handleDrop.apply(null, arguments)\n                          }\n                        }\n                      },\n                      [\n                        _c(\n                          \"a-upload\",\n                          {\n                            attrs: {\n                              name: \"file\",\n                              action: _vm.uploadAction,\n                              headers: _vm.uploadHeaders,\n                              \"file-list\": _vm.finishForm.fileList,\n                              multiple: true,\n                              \"show-upload-list\": true,\n                              \"before-upload\": _vm.beforeUpload\n                            },\n                            on: { change: _vm.handleFileChange }\n                          },\n                          [\n                            _c(\n                              \"a-button\",\n                              { attrs: { type: \"dashed\" } },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"upload\" } }),\n                                _vm._v(\" 点击上传\\n              \")\n                              ],\n                              1\n                            ),\n                            _c(\"div\", { staticClass: \"upload-tips\" }, [\n                              _c(\"span\", [\n                                _vm._v(\"支持：点击上传、拖拽上传（附件非必选）\")\n                              ])\n                            ])\n                          ],\n                          1\n                        )\n                      ],\n                      1\n                    )\n                  ]),\n                  _c(\n                    \"a-form-model-item\",\n                    { attrs: { label: \"完成说明\" } },\n                    [\n                      _c(\"a-textarea\", {\n                        attrs: {\n                          rows: 4,\n                          placeholder: \"请输入完成说明（非必填）\"\n                        },\n                        model: {\n                          value: _vm.finishForm.finishDescribe,\n                          callback: function($$v) {\n                            _vm.$set(_vm.finishForm, \"finishDescribe\", $$v)\n                          },\n                          expression: \"finishForm.finishDescribe\"\n                        }\n                      })\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"modal-footer\" },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      on: {\n                        click: function($event) {\n                          _vm.finishModalVisible = false\n                        }\n                      }\n                    },\n                    [_vm._v(\"取消\")]\n                  ),\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        loading: _vm.finishConfirmLoading\n                      },\n                      on: { click: _vm.handleFinishOk }\n                    },\n                    [_vm._v(\"立即结束\")]\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"hour-line\" }, [\n      _c(\"div\", { staticClass: \"half-hour-line\" }),\n      _c(\"div\", {\n        staticClass: \"quarter-hour-line\",\n        staticStyle: { top: \"25%\" }\n      }),\n      _c(\"div\", {\n        staticClass: \"quarter-hour-line\",\n        staticStyle: { top: \"75%\" }\n      })\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}