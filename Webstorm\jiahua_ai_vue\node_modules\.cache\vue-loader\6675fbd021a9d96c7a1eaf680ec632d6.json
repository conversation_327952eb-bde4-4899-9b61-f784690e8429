{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "mtime": 1753774277478}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\eslint-loader\\index.js", "mtime": 1753423166782}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./ProjectDetail.vue?vue&type=template&id=f28d94aa&scoped=true&\"\nimport script from \"./ProjectDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./ProjectDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ProjectDetail.vue?vue&type=style&index=0&id=f28d94aa&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f28d94aa\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\developing\\\\java\\\\Code\\\\Webstorm\\\\jiahua_ai_vue\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('f28d94aa')) {\n      api.createRecord('f28d94aa', component.options)\n    } else {\n      api.reload('f28d94aa', component.options)\n    }\n    module.hot.accept(\"./ProjectDetail.vue?vue&type=template&id=f28d94aa&scoped=true&\", function () {\n      api.rerender('f28d94aa', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/admin/WorkOrderTask/modules/ProjectDetail.vue\"\nexport default component.exports"]}