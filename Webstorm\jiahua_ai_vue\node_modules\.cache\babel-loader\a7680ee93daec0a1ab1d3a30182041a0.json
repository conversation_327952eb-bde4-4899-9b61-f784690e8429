{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "mtime": 1753774277478}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753423167852}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["function _createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mixinDevice } from '@/utils/mixin';\nimport WorkOrderTaskModal from './WorkOrderTaskModal';\nimport TaskDetailModal from './TaskDetailModal';\nimport ProjectStatsModal from './ProjectStatsModal';\nimport _moment from 'moment';\nimport { getAction, putAction, httpAction } from '@api/manage';\nimport { Modal } from 'ant-design-vue';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'ProjectDetail',\n  mixins: [mixinDevice],\n  components: {\n    WorkOrderTaskModal: WorkOrderTaskModal,\n    TaskDetailModal: TaskDetailModal,\n    ProjectStatsModal: ProjectStatsModal\n  },\n  data: function data() {\n    return {\n      activeTab: 'pool',\n      currentView: 'table',\n      // 新增：当前视图类型\n      currentUserId: '',\n      projectInfo: {},\n      projectMemberList: [],\n      queryParam: {\n        workStatus: []\n      },\n      uploadAction: \"\".concat(window._CONFIG['domianURL'], \"/sys/common/upload\"),\n      uploadHeaders: {},\n      finishModalVisible: false,\n      finishConfirmLoading: false,\n      currentTask: null,\n      finishForm: {\n        fileList: [],\n        finishDescribe: '',\n        finishAnnex: ''\n      },\n      poolTasks: [],\n      myTasks: [],\n      workingTimers: new Map(),\n      // 月历相关\n      currentMonth: _moment(),\n      weekdays: ['一', '二', '三', '四', '五', '六', '日'],\n      dayTasksVisible: false,\n      selectedDay: null,\n      selectedDayTasks: [],\n      // 甘特图相关\n      ganttPeriod: {\n        start: _moment().startOf('week'),\n        end: _moment().endOf('week').add(6, 'days')\n      },\n      // 按天视图相关\n      currentDay: _moment(),\n      dailyColumnAssignment: null,\n      // 当天工单列分配缓存\n      timelineHours: ['00'].concat(_toConsumableArray(Array.from({\n        length: 23\n      }, function (_, i) {\n        return (i + 1).toString().padStart(2, '0');\n      }))),\n      // 0-23小时，完整24小时\n\n      tabs: {\n        'admin': [{\n          value: 'pool',\n          label: '公海池'\n        }, {\n          value: 'my',\n          label: '我的工单'\n        }, {\n          value: 'all',\n          label: '全部工单'\n        }, {\n          value: 'audit',\n          label: '待审核'\n        }],\n        'member': [{\n          value: 'pool',\n          label: '公海池'\n        }, {\n          value: 'my',\n          label: '我的工单'\n        }]\n      },\n      pageNo: 1,\n      pageSize: 3,\n      total: 0,\n      poolTotal: 0,\n      myTotal: 0,\n      auditTotal: 0,\n      allTotal: 0,\n      userRole: 'member',\n      dataSource: [],\n      dataColumns: {\n        'hours': [{\n          title: '工单名称',\n          align: 'center',\n          dataIndex: 'ordername',\n          width: 200\n        }, {\n          title: '优先级',\n          align: 'center',\n          dataIndex: 'priority',\n          width: 100,\n          scopedSlots: {\n            customRender: 'prioritySlot'\n          }\n        }, {\n          title: '处理人',\n          align: 'center',\n          dataIndex: 'handling',\n          width: 120\n        }, {\n          title: '工时',\n          align: 'center',\n          dataIndex: 'estimatedHours',\n          width: 120,\n          scopedSlots: {\n            customRender: 'workHourSlot'\n          }\n        }, {\n          title: '状态',\n          align: 'center',\n          dataIndex: 'workStatus',\n          width: 100,\n          scopedSlots: {\n            customRender: 'workStatus'\n          }\n        }, {\n          title: '创建时间',\n          align: 'center',\n          dataIndex: 'createTime',\n          width: 100,\n          scopedSlots: {\n            customRender: 'createTime'\n          }\n        }, {\n          title: '审核意见',\n          align: 'center',\n          dataIndex: 'reviewComment',\n          width: 100,\n          scopedSlots: {\n            customRender: 'reviewComment'\n          }\n        }, {\n          title: '完成时间',\n          align: 'center',\n          dataIndex: 'reviewTime',\n          width: 100,\n          scopedSlots: {\n            customRender: 'reviewTime'\n          }\n        }, {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          width: 180,\n          scopedSlots: {\n            customRender: 'action'\n          }\n        }],\n        'timespan': [{\n          title: '工单名称',\n          align: 'center',\n          dataIndex: 'ordername',\n          width: 200\n        }, {\n          title: '优先级',\n          align: 'center',\n          dataIndex: 'priority',\n          width: 100,\n          scopedSlots: {\n            customRender: 'prioritySlot'\n          }\n        }, {\n          title: '处理人',\n          align: 'center',\n          dataIndex: 'handling',\n          width: 120\n        }, {\n          title: '计划时间',\n          align: 'center',\n          width: 120,\n          scopedSlots: {\n            customRender: 'plannedTimeSlot'\n          }\n        }, {\n          title: '状态',\n          align: 'center',\n          dataIndex: 'workStatus',\n          width: 100,\n          scopedSlots: {\n            customRender: 'workStatus'\n          }\n        }, {\n          title: '审核意见',\n          align: 'center',\n          dataIndex: 'reviewComment',\n          width: 100,\n          scopedSlots: {\n            customRender: 'reviewComment'\n          }\n        }, {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          width: 180,\n          scopedSlots: {\n            customRender: 'action'\n          }\n        }]\n      },\n      loading: false,\n      selectedRowKeys: [],\n      selectionRows: [],\n      ipagination: {\n        current: 1,\n        pageSize: 15,\n        total: 0\n      },\n      statusMap: {\n        'pending': '待处理',\n        'working': '进行中',\n        'completed': '待审核',\n        'approved': '已审核',\n        'rejected': '已驳回'\n      },\n      progressStageMap: {\n        '0': '准备阶段',\n        '1': '实施阶段',\n        '2': '完成阶段'\n      },\n      isShowAudit: false,\n      auditOpinion: '',\n      currentId: '',\n      allTasksForView: []\n    };\n  },\n  computed: {\n    projectId: function projectId() {\n      return this.$route.query.id;\n    },\n    moment: function moment() {\n      return _moment;\n    },\n    // 月历计算属性\n    calendarDays: function calendarDays() {\n      var startOfMonth = this.currentMonth.clone().startOf('month');\n      var endOfMonth = this.currentMonth.clone().endOf('month');\n      var startDate = startOfMonth.clone().startOf('week');\n      var endDate = endOfMonth.clone().endOf('week');\n      var days = [];\n      var current = startDate.clone();\n      while (current.isSameOrBefore(endDate)) {\n        var dayTasks = this.getTasksForDate(current);\n        days.push({\n          date: current.format('YYYY-MM-DD'),\n          dayNumber: current.date(),\n          isCurrentMonth: current.isSame(this.currentMonth, 'month'),\n          isToday: current.isSame(_moment(), 'day'),\n          tasks: dayTasks\n        });\n        current.add(1, 'day');\n      }\n      return days;\n    },\n    // 甘特图计算属性\n    ganttDates: function ganttDates() {\n      var dates = [];\n      var current = this.ganttPeriod.start.clone();\n      while (current.isSameOrBefore(this.ganttPeriod.end)) {\n        dates.push(current.clone());\n        current.add(1, 'day');\n      }\n      return dates;\n    },\n    ganttTasks: function ganttTasks() {\n      var _this = this;\n      // 优先使用当前视图的数据，如果没有则使用表格数据\n      var tasksForView = this.allTasksForView || this.dataSource;\n      return tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n        return taskStart.isSameOrBefore(_this.ganttPeriod.end) && taskEnd.isSameOrAfter(_this.ganttPeriod.start);\n      });\n    }\n  },\n  created: function created(options) {\n    console.log(\"222333444\");\n    console.log(options);\n    console.log(\"进入人33333333\");\n  },\n  mounted: function mounted() {\n    console.log(\"进入人222222222\");\n    this.currentUserId = this.$store.getters.userInfo.id;\n    this.loadProjectInfo();\n    this.loadProjectMember();\n    this.loadTasks();\n    this.startWorkingTimers();\n\n    // 设置上传头部\n    var token = Vue.ls.get(ACCESS_TOKEN);\n    this.uploadHeaders = {\n      'X-Access-Token': token\n    };\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.clearWorkingTimers();\n  },\n  methods: {\n    // 视图切换\n    switchView: function switchView(view) {\n      this.currentView = view;\n\n      // 如果切换到日历或甘特图视图，重新加载当前标签页的数据\n      if (view !== 'table') {\n        this.loadAllTasksForView();\n      }\n    },\n    loadAllTasksForView: function loadAllTasksForView() {\n      var _this2 = this;\n      this.loading = true;\n      // 先清空旧数据\n      this.allTasksForView = [];\n      var params = _objectSpread({}, this.queryParam);\n\n      // 将状态数组转换为逗号分隔的字符串\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\n        params.workStatus = params.workStatus.join(',');\n      }\n      params.projectId = this.projectId;\n      params.backup1 = this.activeTab === 'pool' ? '' : this.activeTab === 'my' ? '1' : this.activeTab === 'all' ? '3' : '2';\n      params.pageNo = 1;\n      params.pageSize = 1000;\n      this.$http.get('/WorkOrderTask/list', {\n        params: params\n      }).then(function (res) {\n        if (res.success) {\n          _this2.allTasksForView = res.result.data;\n        }\n        _this2.loading = false;\n      });\n    },\n    // 月历相关方法\n    previousMonth: function previousMonth() {\n      this.currentMonth = this.currentMonth.clone().subtract(1, 'month');\n    },\n    nextMonth: function nextMonth() {\n      this.currentMonth = this.currentMonth.clone().add(1, 'month');\n    },\n    getTasksForDate: function getTasksForDate(date) {\n      var dateStr = date.format('YYYY-MM-DD');\n      // 优先使用当前视图的数据，如果没有则使用表格数据\n      var tasksForView = this.allTasksForView || this.dataSource;\n      return tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var startDate = _moment(task.startTime).format('YYYY-MM-DD');\n        var endDate = _moment(task.endTime).format('YYYY-MM-DD');\n        return dateStr >= startDate && dateStr <= endDate;\n      });\n    },\n    showDayTasks: function showDayTasks(day) {\n      this.selectedDay = day;\n      this.selectedDayTasks = day.tasks;\n      this.dayTasksVisible = true;\n    },\n    // 甘特图相关方法\n    previousGanttPeriod: function previousGanttPeriod() {\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().subtract(7, 'days');\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().subtract(7, 'days');\n    },\n    nextGanttPeriod: function nextGanttPeriod() {\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().add(7, 'days');\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().add(7, 'days');\n    },\n    getGanttBarStyle: function getGanttBarStyle(task) {\n      if (!task.startTime || !task.endTime) return {\n        display: 'none'\n      };\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var periodStart = this.ganttPeriod.start;\n      var periodEnd = this.ganttPeriod.end;\n\n      // 计算任务在甘特图中的位置\n      var totalDays = periodEnd.diff(periodStart, 'days') + 1;\n      var dayWidth = 100 / totalDays;\n\n      // 计算开始位置\n      var startOffset = 0;\n      if (taskStart.isSameOrAfter(periodStart)) {\n        startOffset = taskStart.diff(periodStart, 'days') * dayWidth;\n      }\n\n      // 计算宽度\n      var width = dayWidth;\n      if (taskEnd.isAfter(taskStart)) {\n        var visibleStart = _moment.max(taskStart, periodStart);\n        var visibleEnd = _moment.min(taskEnd, periodEnd);\n        var visibleDays = visibleEnd.diff(visibleStart, 'days') + 1;\n        width = visibleDays * dayWidth;\n      }\n      return {\n        left: \"\".concat(startOffset, \"%\"),\n        width: \"\".concat(width, \"%\")\n      };\n    },\n    // 按天视图相关方法\n    previousDay: function previousDay() {\n      this.currentDay = this.currentDay.clone().subtract(1, 'day');\n      this.clearDailyColumnAssignment();\n    },\n    nextDay: function nextDay() {\n      this.currentDay = this.currentDay.clone().add(1, 'day');\n      this.clearDailyColumnAssignment();\n    },\n    onDayChange: function onDayChange(date) {\n      if (date) {\n        this.currentDay = _moment(date);\n        this.clearDailyColumnAssignment();\n      }\n    },\n    clearDailyColumnAssignment: function clearDailyColumnAssignment() {\n      // 清除当天的列分配缓存，强制重新计算\n      this.dailyColumnAssignment = null;\n    },\n    getTasksForHour: function getTasksForHour(hour) {\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var tasksForView = this.allTasksForView || this.dataSource;\n      var tasks = tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n        var taskStartDate = taskStart.format('YYYY-MM-DD');\n        var taskEndDate = taskEnd.format('YYYY-MM-DD');\n\n        // 关键修改：排除跨日期工单，只显示当天的工单\n        if (taskStartDate !== taskEndDate) {\n          return false; // 跨日期工单不在时间轴内显示\n        }\n\n        // 只处理当天的工单\n        if (currentDateStr !== taskStartDate) {\n          return false;\n        }\n        var startHour = taskStart.hour();\n        var hourNum = parseInt(hour);\n\n        // 只在开始小时显示工单\n        return hourNum === startHour;\n      });\n\n      // 为重叠的工单分配列位置，从最左侧开始\n      return this.assignTaskColumnsFromLeft(tasks, hour);\n    },\n    // 新的列分配方法：从最左侧开始排列\n    assignTaskColumnsFromLeft: function assignTaskColumnsFromLeft(tasks, hour) {\n      if (tasks.length <= 1) {\n        return tasks.map(function (task) {\n          return _objectSpread(_objectSpread({}, task), {}, {\n            columnIndex: 0,\n            totalColumns: 1\n          });\n        });\n      }\n\n      // 简化的列分配：按开始时间排序，从左到右分配\n      var sortedTasks = tasks.sort(function (a, b) {\n        var timeA = _moment(a.startTime);\n        var timeB = _moment(b.startTime);\n        return timeA.valueOf() - timeB.valueOf();\n      });\n\n      // 检查时间重叠，分配到不同列\n      var columns = [];\n      sortedTasks.forEach(function (task) {\n        var assignedColumn = -1;\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n\n        // 寻找不重叠的列\n        for (var colIndex = 0; colIndex < columns.length; colIndex++) {\n          var canUseColumn = true;\n          var _iterator = _createForOfIteratorHelper(columns[colIndex]),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var existingTask = _step.value;\n              var existingStart = _moment(existingTask.startTime);\n              var existingEnd = _moment(existingTask.endTime);\n\n              // 检查时间重叠\n              if (taskStart.isBefore(existingEnd) && taskEnd.isAfter(existingStart)) {\n                canUseColumn = false;\n                break;\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n          if (canUseColumn) {\n            assignedColumn = colIndex;\n            break;\n          }\n        }\n\n        // 如果没有找到合适的列，创建新列\n        if (assignedColumn === -1) {\n          assignedColumn = columns.length;\n          columns.push([]);\n        }\n        columns[assignedColumn].push(task);\n        task.columnIndex = assignedColumn;\n        task.totalColumns = Math.min(4, columns.length); // 最多4列\n      });\n\n      // 确保所有工单都有相同的总列数\n      var totalColumns = Math.min(4, columns.length);\n      sortedTasks.forEach(function (task) {\n        task.totalColumns = totalColumns;\n      });\n      return sortedTasks;\n    },\n    assignTaskColumns: function assignTaskColumns(tasks, hour) {\n      // 保留原方法以兼容其他调用\n      return this.assignTaskColumnsFromLeft(tasks, hour);\n    },\n    performDailyColumnAssignment: function performDailyColumnAssignment(allTasks) {\n      var _this3 = this;\n      console.log('开始全局列分配，工单数量:', allTasks.length);\n\n      // 按开始时间排序所有工单\n      var sortedTasks = allTasks.sort(function (a, b) {\n        var startA = _moment(a.startTime);\n        var startB = _moment(b.startTime);\n        return startA.valueOf() - startB.valueOf();\n      });\n      var columns = [];\n\n      // 为每个工单分配列，使用更严格的重叠检测\n      sortedTasks.forEach(function (task, index) {\n        var assignedColumn = -1;\n        console.log(\"\\u5904\\u7406\\u5DE5\\u5355 \".concat(index + 1, \": \").concat(task.ordername));\n        console.log(\"\\u5DE5\\u5355\\u65F6\\u95F4: \".concat(task.startTime, \" - \").concat(task.endTime));\n\n        // 尝试找到一个不重叠的列\n        for (var colIndex = 0; colIndex < columns.length; colIndex++) {\n          var canUseColumn = true;\n          var _iterator2 = _createForOfIteratorHelper(columns[colIndex]),\n            _step2;\n          try {\n            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n              var existingTask = _step2.value;\n              var overlaps = _this3.tasksOverlapInTime(task, existingTask);\n              console.log(\"\\u68C0\\u67E5\\u4E0E\\u5DE5\\u5355 \".concat(existingTask.ordername, \" \\u7684\\u91CD\\u53E0: \").concat(overlaps));\n              if (overlaps) {\n                canUseColumn = false;\n                break;\n              }\n            }\n          } catch (err) {\n            _iterator2.e(err);\n          } finally {\n            _iterator2.f();\n          }\n          if (canUseColumn) {\n            assignedColumn = colIndex;\n            console.log(\"\\u5206\\u914D\\u5230\\u5217 \".concat(colIndex));\n            break;\n          }\n        }\n\n        // 如果没有找到合适的列，创建新列\n        if (assignedColumn === -1) {\n          assignedColumn = columns.length;\n          columns.push([]);\n          console.log(\"\\u521B\\u5EFA\\u65B0\\u5217 \".concat(assignedColumn));\n        }\n        columns[assignedColumn].push(task);\n        task.columnIndex = assignedColumn;\n        task.totalColumns = Math.min(4, columns.length); // 最多4列\n      });\n\n      // 确保所有工单都有相同的总列数\n      var totalColumns = Math.min(4, columns.length);\n      sortedTasks.forEach(function (task) {\n        task.totalColumns = totalColumns;\n      });\n      console.log(\"\\u5217\\u5206\\u914D\\u5B8C\\u6210\\uFF0C\\u603B\\u5217\\u6570: \".concat(totalColumns));\n      console.log('各列工单分布:', columns.map(function (col, index) {\n        return {\n          column: index,\n          tasks: col.map(function (t) {\n            return t.ordername;\n          })\n        };\n      }));\n\n      // 缓存当天的列分配结果\n      this.dailyColumnAssignment = {\n        date: this.currentDay.format('YYYY-MM-DD'),\n        tasks: sortedTasks\n      };\n    },\n    tasksOverlapInTime: function tasksOverlapInTime(task1, task2) {\n      // 获取两个工单在当前日期的可见时间范围\n      var currentDate = this.currentDay.format('YYYY-MM-DD');\n      var range1 = this.getTaskVisibleRangeForDate(task1, currentDate);\n      var range2 = this.getTaskVisibleRangeForDate(task2, currentDate);\n      if (!range1 || !range2) {\n        console.log(\"\\u91CD\\u53E0\\u68C0\\u6D4B\\u5931\\u8D25: range1=\".concat(!!range1, \", range2=\").concat(!!range2));\n        return false;\n      }\n\n      // 改进的重叠检测：增加时间缓冲区，使相近的工单也被认为重叠\n      // 这样可以避免视觉上相近的工单在同一列显示\n      var bufferMinutes = 30; // 30分钟缓冲区\n      var range1StartWithBuffer = range1.start.clone().subtract(bufferMinutes, 'minutes');\n      var range1EndWithBuffer = range1.end.clone().add(bufferMinutes, 'minutes');\n      var range2StartWithBuffer = range2.start.clone().subtract(bufferMinutes, 'minutes');\n      var range2EndWithBuffer = range2.end.clone().add(bufferMinutes, 'minutes');\n      var overlaps = range1StartWithBuffer.isBefore(range2EndWithBuffer) && range2StartWithBuffer.isBefore(range1EndWithBuffer);\n      console.log(\"\\u91CD\\u53E0\\u68C0\\u6D4B\\u8BE6\\u60C5:\");\n      console.log(\"  \\u5DE5\\u53551 \".concat(task1.ordername, \": \").concat(range1.start.format('HH:mm'), \" - \").concat(range1.end.format('HH:mm')));\n      console.log(\"  \\u5DE5\\u53552 \".concat(task2.ordername, \": \").concat(range2.start.format('HH:mm'), \" - \").concat(range2.end.format('HH:mm')));\n      console.log(\"  \\u91CD\\u53E0\\u7ED3\\u679C: \".concat(overlaps));\n      return overlaps;\n    },\n    getTaskVisibleRangeForDate: function getTaskVisibleRangeForDate(task, date) {\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var taskStartDate = taskStart.format('YYYY-MM-DD');\n      var taskEndDate = taskEnd.format('YYYY-MM-DD');\n\n      // 如果工单不在当前日期范围内，返回null\n      if (date < taskStartDate || date > taskEndDate) {\n        return null;\n      }\n\n      // 计算在当前日期的可见时间范围\n      var dayStart = _moment(\"\".concat(date, \" 00:00:00\"));\n      var dayEnd = _moment(\"\".concat(date, \" 23:59:59\"));\n      var visibleStart, visibleEnd;\n      if (date === taskStartDate && date === taskEndDate) {\n        // 工单在同一天内\n        visibleStart = taskStart;\n        visibleEnd = taskEnd;\n      } else if (date === taskStartDate) {\n        // 当前日期是工单开始日期，延伸到当天结束\n        visibleStart = taskStart;\n        visibleEnd = dayEnd;\n      } else if (date === taskEndDate) {\n        // 修复：当前日期是工单结束日期，从当天开始到工单结束时间\n        // 但是对于按天视图，我们需要考虑工单在结束日期的实际显示需求\n        visibleStart = dayStart;\n        visibleEnd = taskEnd;\n      } else {\n        // 当前日期在工单开始和结束之间，显示全天\n        visibleStart = dayStart;\n        visibleEnd = dayEnd;\n      }\n\n      // 调试输出\n      console.log(\"\\u5DE5\\u5355 \".concat(task.ordername, \" \\u5728 \").concat(date, \" \\u7684\\u53EF\\u89C1\\u8303\\u56F4:\"));\n      console.log(\"  \\u539F\\u59CB\\u65F6\\u95F4: \".concat(taskStart.format('YYYY-MM-DD HH:mm'), \" - \").concat(taskEnd.format('YYYY-MM-DD HH:mm')));\n      console.log(\"  \\u65E5\\u671F\\u5206\\u7C7B: \\u5F00\\u59CB=\".concat(taskStartDate, \", \\u5F53\\u524D=\").concat(date, \", \\u7ED3\\u675F=\").concat(taskEndDate));\n      console.log(\"  \\u53EF\\u89C1\\u8303\\u56F4: \".concat(visibleStart.format('HH:mm'), \" - \").concat(visibleEnd.format('HH:mm')));\n      return {\n        start: visibleStart,\n        end: visibleEnd\n      };\n    },\n    getAllTasksForCurrentDay: function getAllTasksForCurrentDay() {\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var tasksForView = this.allTasksForView || this.dataSource;\n      return tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var taskStartDate = _moment(task.startTime).format('YYYY-MM-DD');\n        var taskEndDate = _moment(task.endTime).format('YYYY-MM-DD');\n\n        // 检查任务是否在当前日期\n        return currentDateStr >= taskStartDate && currentDateStr <= taskEndDate;\n      });\n    },\n    findOverlappingTasks: function findOverlappingTasks(currentTask, allTasks) {\n      var _this4 = this;\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var overlapping = [];\n      allTasks.forEach(function (task) {\n        if (_this4.tasksOverlapOnDate(currentTask, task, currentDateStr)) {\n          overlapping.push(task.id);\n        }\n      });\n      return overlapping.sort(); // 排序确保一致的列分配\n    },\n    getTaskVisibleRange: function getTaskVisibleRange(task) {\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var taskStartDate = taskStart.format('YYYY-MM-DD');\n      var taskEndDate = taskEnd.format('YYYY-MM-DD');\n      var dayStart = _moment(\"\".concat(currentDateStr, \" 01:00:00\")); // 从1:00开始\n      var dayEnd = _moment(\"\".concat(currentDateStr, \" 23:59:59\")); // 到23:59结束\n\n      var visibleStart, visibleEnd;\n      if (currentDateStr === taskStartDate && currentDateStr === taskEndDate) {\n        visibleStart = _moment.max(taskStart, dayStart);\n        visibleEnd = _moment.min(taskEnd, dayEnd);\n      } else if (currentDateStr === taskStartDate) {\n        visibleStart = _moment.max(taskStart, dayStart);\n        visibleEnd = dayEnd;\n      } else if (currentDateStr === taskEndDate) {\n        visibleStart = dayStart;\n        visibleEnd = _moment.min(taskEnd, dayEnd);\n      } else if (currentDateStr > taskStartDate && currentDateStr < taskEndDate) {\n        visibleStart = dayStart;\n        visibleEnd = dayEnd;\n      } else {\n        return null;\n      }\n\n      // 如果可见时间范围无效，返回null\n      if (visibleStart.isAfter(visibleEnd)) {\n        return null;\n      }\n      return {\n        start: visibleStart,\n        end: visibleEnd\n      };\n    },\n    timeRangesOverlap: function timeRangesOverlap(range1, range2) {\n      // 检查两个时间范围是否重叠\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end);\n    },\n    tasksOverlapOnDate: function tasksOverlapOnDate(task1, task2, dateStr) {\n      // 计算两个工单在指定日期的时间范围\n      var getVisibleRange = function getVisibleRange(task) {\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n        var taskStartDate = taskStart.format('YYYY-MM-DD');\n        var taskEndDate = taskEnd.format('YYYY-MM-DD');\n        if (dateStr === taskStartDate && dateStr === taskEndDate) {\n          return {\n            start: taskStart,\n            end: taskEnd\n          };\n        } else if (dateStr === taskStartDate) {\n          return {\n            start: taskStart,\n            end: _moment(\"\".concat(dateStr, \" 23:59:59\"))\n          };\n        } else if (dateStr === taskEndDate) {\n          return {\n            start: _moment(\"\".concat(dateStr, \" 00:00:00\")),\n            end: taskEnd\n          };\n        } else if (dateStr > taskStartDate && dateStr < taskEndDate) {\n          return {\n            start: _moment(\"\".concat(dateStr, \" 00:00:00\")),\n            end: _moment(\"\".concat(dateStr, \" 23:59:59\"))\n          };\n        }\n        return null;\n      };\n      var range1 = getVisibleRange(task1);\n      var range2 = getVisibleRange(task2);\n      if (!range1 || !range2) return false;\n\n      // 检查时间范围是否重叠\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end);\n    },\n    getTaskCardStyle: function getTaskCardStyle(task) {\n      if (!task.startTime || !task.endTime) return {};\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var currentDate = this.currentDay.format('YYYY-MM-DD');\n      var taskStartDate = taskStart.format('YYYY-MM-DD');\n      var taskEndDate = taskEnd.format('YYYY-MM-DD');\n\n      // 只处理当天的工单（跨日期工单已在上层过滤）\n      if (taskStartDate !== taskEndDate || currentDate !== taskStartDate) {\n        return {\n          display: 'none'\n        };\n      }\n      var startHour = taskStart.hour();\n      var startMinute = taskStart.minute();\n      var endHour = taskEnd.hour();\n      var endMinute = taskEnd.minute();\n\n      // 计算在120px高度时间槽中的精确位置\n      var topOffsetPx = startMinute / 60 * 120; // 使用120px高度\n\n      // 注释：不再需要durationMinutes变量，直接计算精确高度\n\n      // 精确计算高度，确保底部对齐结束时间刻度线\n      var heightPx;\n      if (startHour === endHour) {\n        // 同一小时内的工单 - 精确计算到分钟\n        var endOffsetPx = endMinute / 60 * 120;\n        heightPx = endOffsetPx - topOffsetPx;\n\n        // 确保最小高度能显示内容（一行布局后内容更紧凑）\n        var minContentHeight = this.calculateContentHeight();\n        if (heightPx < minContentHeight) {\n          // 如果时间范围太短，优先保证内容显示，但尽量接近结束时间\n          heightPx = minContentHeight;\n        }\n      } else {\n        // 跨小时的工单 - 精确计算到结束时间位置\n        var totalHours = endHour - startHour;\n        var endMinuteOffset = endMinute / 60 * 120;\n        heightPx = totalHours * 120 + endMinuteOffset - topOffsetPx;\n\n        // 跨小时工单通常有足够空间，但仍需检查最小内容高度\n        var _minContentHeight = this.calculateContentHeight();\n        heightPx = Math.max(heightPx, _minContentHeight);\n      }\n\n      // 确保高度为正数且不小于最小值\n      heightPx = Math.max(heightPx, 60); // 绝对最小高度60px\n\n      // 计算多列布局，从最左侧开始\n      var totalColumns = task.totalColumns || 1;\n      var columnIndex = task.columnIndex || 0;\n\n      // 优化列宽计算，确保内容完整显示\n      var containerWidth = 100; // 容器总宽度百分比\n      var columnGap = 2; // 列间距百分比\n      var totalGapWidth = (totalColumns - 1) * columnGap;\n      var availableWidth = containerWidth - totalGapWidth;\n      var columnWidth = availableWidth / totalColumns;\n\n      // 从最左侧开始排列（0%开始）\n      var leftOffset = columnIndex * (columnWidth + columnGap);\n      var finalStyle = {\n        top: \"\".concat(topOffsetPx, \"px\"),\n        height: \"\".concat(heightPx, \"px\"),\n        position: 'absolute',\n        width: \"\".concat(Math.max(columnWidth, 20), \"%\"),\n        // 确保最小宽度\n        left: \"\".concat(leftOffset, \"%\"),\n        // 从最左侧开始\n        zIndex: 10001 + columnIndex,\n        overflow: 'visible',\n        // 改为visible，允许内容自适应\n        minHeight: \"\".concat(heightPx, \"px\") // 使用计算出的高度作为最小高度\n      };\n      return finalStyle;\n    },\n    // 新增：计算内容所需高度（一行布局，状态在右上角）\n    calculateContentHeight: function calculateContentHeight() {\n      // 基础内容高度计算（根据实际CSS样式）\n      var titleHeight = 22; // 标题行高度（14px字体 * 1.3行高）\n      var metaRowHeight = 28; // 处理人和时间在一行的高度\n      var cardPadding = 32; // 卡片上下内边距总和（16px * 2）\n      var gaps = 12; // 标题和meta行之间的间距\n\n      // 总内容高度（处理人和时间在一行，更紧凑）\n      var totalContentHeight = titleHeight + metaRowHeight + cardPadding + gaps;\n\n      // 确保最小高度能显示完整内容，一行布局后高度更小\n      return Math.max(totalContentHeight, 80);\n    },\n    // 获取当前日期的跨日期工单\n    getCrossDayTasksForCurrentDay: function getCrossDayTasksForCurrentDay() {\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var tasksForView = this.allTasksForView || this.dataSource;\n      return tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n        var taskStartDate = taskStart.format('YYYY-MM-DD');\n        var taskEndDate = taskEnd.format('YYYY-MM-DD');\n\n        // 检查是否为跨日期工单且在当前日期范围内\n        var isCrossDay = taskStartDate !== taskEndDate;\n        var isInCurrentDate = currentDateStr >= taskStartDate && currentDateStr <= taskEndDate;\n        return isCrossDay && isInCurrentDate;\n      });\n    },\n    // 获取当前日期的当天工单（非跨日期）\n    getCurrentDayTasksForHour: function getCurrentDayTasksForHour(hour) {\n      var currentDateStr = this.currentDay.format('YYYY-MM-DD');\n      var tasksForView = this.allTasksForView || this.dataSource;\n      var tasks = tasksForView.filter(function (task) {\n        if (!task.startTime || !task.endTime) return false;\n        var taskStart = _moment(task.startTime);\n        var taskEnd = _moment(task.endTime);\n        var taskStartDate = taskStart.format('YYYY-MM-DD');\n        var taskEndDate = taskEnd.format('YYYY-MM-DD');\n\n        // 只显示当天工单（非跨日期）\n        var isSameDay = taskStartDate === taskEndDate && taskStartDate === currentDateStr;\n        if (!isSameDay) return false;\n\n        // 修复：只在工单开始的小时槽显示，避免重复\n        var startHour = taskStart.hour();\n        var hourNum = parseInt(hour);\n        return hourNum === startHour;\n      });\n      return this.assignTaskColumns(tasks, hour);\n    },\n    // 格式化跨日期工单的时间显示\n    formatCrossDayTaskTime: function formatCrossDayTaskTime(task) {\n      var currentDate = this.currentDay.format('YYYY-MM-DD');\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var taskStartDate = taskStart.format('YYYY-MM-DD');\n      var taskEndDate = taskEnd.format('YYYY-MM-DD');\n      if (currentDate === taskStartDate && currentDate === taskEndDate) {\n        // 同一天（不应该出现在这里）\n        return \"\".concat(taskStart.format('HH:mm'), \" - \").concat(taskEnd.format('HH:mm'));\n      } else if (currentDate === taskStartDate) {\n        // 开始日期\n        return \"\".concat(taskStart.format('HH:mm'), \" - \\u6B21\\u65E5\");\n      } else if (currentDate === taskEndDate) {\n        // 结束日期\n        return \"\\u524D\\u65E5 - \".concat(taskEnd.format('HH:mm'));\n      } else {\n        // 中间日期\n        return \"\\u5168\\u5929\";\n      }\n    },\n    // 计算跨日期工单的持续时间\n    getCrossDayTaskDuration: function getCrossDayTaskDuration(task) {\n      var taskStart = _moment(task.startTime);\n      var taskEnd = _moment(task.endTime);\n      var duration = _moment.duration(taskEnd.diff(taskStart));\n      var days = Math.floor(duration.asDays());\n      var hours = duration.hours();\n      var minutes = duration.minutes();\n      if (days > 0) {\n        return \"\".concat(days, \"\\u5929\").concat(hours, \"\\u5C0F\\u65F6\");\n      } else if (hours > 0) {\n        return \"\".concat(hours, \"\\u5C0F\\u65F6\").concat(minutes, \"\\u5206\\u949F\");\n      } else {\n        return \"\".concat(minutes, \"\\u5206\\u949F\");\n      }\n    },\n    // 计算跨日期工单在时间轴上的样式\n    getCrossDayTaskStyle: function getCrossDayTaskStyle(task, index) {\n      var totalTasks = this.getCrossDayTasksForCurrentDay().length;\n      var taskWidth = Math.min(95 / totalTasks, 25); // 最大宽度25%，根据工单数量调整\n      var leftOffset = index * (taskWidth + 1.5); // 1.5%间距，增加间距\n\n      return {\n        position: 'relative',\n        width: \"\".concat(taskWidth, \"%\"),\n        minWidth: '200px',\n        marginRight: '12px',\n        marginBottom: '8px',\n        display: 'inline-block',\n        verticalAlign: 'top',\n        zIndex: 100 + index\n      };\n    },\n    // 获取工单颜色类名\n    getTaskColorClass: function getTaskColorClass(task) {\n      var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      // 基于工单状态和索引生成颜色类\n      var colorClasses = ['task-color-blue', 'task-color-purple', 'task-color-green', 'task-color-orange', 'task-color-pink', 'task-color-cyan', 'task-color-red', 'task-color-indigo'];\n\n      // 优先根据状态分配颜色\n      var statusColors = {\n        'pending': 'task-color-orange',\n        'working': 'task-color-blue',\n        'completed': 'task-color-purple',\n        'approved': 'task-color-green',\n        'rejected': 'task-color-red'\n      };\n\n      // 如果有状态对应的颜色，使用状态颜色，否则使用循环颜色\n      return statusColors[task.workStatus] || colorClasses[index % colorClasses.length];\n    },\n    formatTaskTime: function formatTaskTime(task) {\n      if (!task.startTime || !task.endTime) return '';\n      var start = _moment(task.startTime);\n      var end = _moment(task.endTime);\n      var startDate = start.format('YYYY-MM-DD');\n      var endDate = end.format('YYYY-MM-DD');\n\n      // 如果是同一天，只显示时间范围（更紧凑）\n      if (startDate === endDate) {\n        return \"\".concat(start.format('HH:mm'), \"-\").concat(end.format('HH:mm'));\n      }\n\n      // 跨天显示紧凑格式\n      return \"\".concat(start.format('M/D HH:mm'), \"-\").concat(end.format('M/D HH:mm'));\n    },\n    // 原有方法保持不变\n    searchQuery: function searchQuery() {\n      this.loadTasks();\n    },\n    searchReset: function searchReset() {\n      this.queryParam = {\n        workStatus: []\n      };\n      this.ipagination.current = 1;\n    },\n    audit: function audit(id, type) {\n      var _this5 = this;\n      if (type === '1') {\n        Modal.confirm({\n          title: '审核确认',\n          icon: '',\n          content: '确定要通过此条工单吗？',\n          onOk: function onOk() {\n            _this5.updateWorkStatus(id, 'approved');\n          }\n        });\n      } else if (type === '2') {\n        this.isShowAudit = true;\n        this.currentId = id;\n      } else if (type === '3') {\n        if (this.auditOpinion.length === 0) {\n          this.$message.error(\"请输入审核意见！\");\n          return;\n        }\n        this.updateWorkStatus(this.currentId, 'rejected');\n      }\n    },\n    updateWorkStatus: function updateWorkStatus(id, status) {\n      var _this6 = this;\n      var params = {};\n      params.id = id;\n      params.workStatus = status;\n      if (status === 'rejected') {\n        params.reviewComment = this.auditOpinion;\n      }\n      putAction('/WorkOrderTask/edit', params).then(function (res) {\n        if (res.success) {\n          _this6.$message.success('操作成功！');\n          _this6.loadTasks();\n          // 如果当前不是表格视图，也要重新加载数据\n          if (_this6.currentView !== 'table') {\n            _this6.loadAllTasksForView();\n          }\n          _this6.isShowAudit = false;\n        } else {\n          _this6.$message.error('操作失败！');\n        }\n      });\n    },\n    handleTableChange: function handleTableChange(current, pageSize) {\n      this.ipagination = current;\n      this.loadTasks();\n    },\n    onSelectChange: function onSelectChange(selectedRowKeys, selectionRows) {\n      this.selectedRowKeys = selectedRowKeys;\n      this.selectionRows = selectionRows;\n    },\n    getWorkStatusText: function getWorkStatusText(status) {\n      return this.statusMap[status] || '待处理';\n    },\n    getProgressStageText: function getProgressStageText(stage) {\n      return this.progressStageMap[stage] || '未开始';\n    },\n    switchTab: function switchTab(e) {\n      var _this7 = this;\n      this.dataSource = [];\n      this.loading = true;\n      this.activeTab = e;\n\n      // 如果切换到不支持多视图的标签页，强制切换回表格视图\n      if (e === 'pool' || e === 'audit') {\n        this.currentView = 'table';\n      }\n      this.searchReset();\n      this.loadTasks();\n\n      // 如果当前不是表格视图，也要重新加载数据\n      if (this.currentView !== 'table') {\n        // 延迟加载，确保标签切换完成\n        this.$nextTick(function () {\n          _this7.loadAllTasksForView();\n        });\n      }\n    },\n    loadProjectInfo: function loadProjectInfo() {\n      var _this8 = this;\n      getAction(\"/project/queryById\", {\n        id: this.projectId\n      }).then(function (res) {\n        if (res.success) {\n          _this8.projectInfo = res.result;\n        }\n      });\n    },\n    loadProjectMember: function loadProjectMember() {\n      var _this9 = this;\n      getAction(\"/projectMember/list\", {\n        projectId: this.projectId\n      }).then(function (res) {\n        if (res.success) {\n          _this9.projectMemberList = res.result;\n          _this9.projectMemberList.forEach(function (item) {\n            if (item.user_id === _this9.currentUserId) {\n              _this9.userRole = item.role;\n              return;\n            }\n          });\n        }\n      });\n    },\n    loadTasks: function loadTasks() {\n      var _this10 = this;\n      this.loading = true;\n      var params = _objectSpread({}, this.queryParam);\n\n      // 将状态数组转换为逗号分隔的字符串\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\n        params.workStatus = params.workStatus.join(',');\n      }\n      params.projectId = this.projectId;\n      params.backup1 = this.activeTab === 'pool' ? '' : this.activeTab === 'my' ? '1' : this.activeTab === 'all' ? '3' : '2';\n      params.pageNo = this.ipagination.current;\n      params.pageSize = this.ipagination.pageSize;\n      this.$http.get('/WorkOrderTask/list', {\n        params: params\n      }).then(function (res) {\n        if (res.success) {\n          _this10.dataSource = res.result.data;\n          var numJSON = res.result.data2;\n          _this10.poolTotal = numJSON.poolTotal;\n          _this10.myTotal = numJSON.myTotal;\n          _this10.allTotal = numJSON.allTotal;\n          _this10.auditTotal = numJSON.auditTotal;\n          _this10.ipagination.total = res.result.total;\n        } else {\n          _this10.dataSource = [];\n        }\n        _this10.loading = false;\n      });\n    },\n    handleAddTask: function handleAddTask() {\n      this.$refs.taskModal.add();\n    },\n    taskModalOk: function taskModalOk() {\n      this.loadTasks();\n      // 如果当前不是表格视图，也要重新加载数据\n      if (this.currentView !== 'table') {\n        this.loadAllTasksForView();\n      }\n    },\n    claimTask: function claimTask(task) {\n      var _this11 = this;\n      this.$confirm({\n        title: '确认领取',\n        content: \"\\u786E\\u5B9A\\u8981\\u9886\\u53D6\\u5DE5\\u5355\\\"\".concat(task.ordername, \"\\\"\\u5417\\uFF1F\"),\n        onOk: function onOk() {\n          _this11.$http.post('/WorkOrderTask/claim', {\n            id: task.id\n          }).then(function (res) {\n            if (res.success) {\n              _this11.$message.success('领取成功');\n              _this11.loadTasks();\n              if (_this11.currentView !== 'table') {\n                _this11.loadAllTasksForView();\n              }\n            } else {\n              _this11.$message.error('领取失败');\n            }\n          });\n        }\n      });\n    },\n    startWork: function startWork(task) {\n      var _this12 = this;\n      this.$http.post('/WorkOrderTask/startWork', {\n        id: task.id,\n        startTime: new Date()\n      }).then(function (res) {\n        if (res.success) {\n          _this12.$message.success('工单已开始');\n          task.workStatus = 'working';\n          task.actualStartTime = new Date();\n          _this12.startWorkingTimer(task);\n        } else {\n          _this12.$message.error('开始失败');\n        }\n      });\n    },\n    showFinishModal: function showFinishModal(task) {\n      this.currentTask = task;\n      this.finishForm = {\n        fileList: [],\n        finishDescribe: '',\n        finishAnnex: ''\n      };\n      this.finishModalVisible = true;\n    },\n    // 处理拖拽上传\n    handleDrop: function handleDrop(e) {\n      var _this13 = this;\n      e.preventDefault();\n      var files = Array.from(e.dataTransfer.files);\n      files.forEach(function (file) {\n        _this13.uploadFile(file);\n      });\n    },\n    // 上传前验证\n    beforeUpload: function beforeUpload(file) {\n      var isLt100M = file.size / 1024 / 1024 < 100;\n      if (!isLt100M) {\n        this.$message.error('文件必须小于 100MB!');\n        return false;\n      }\n      return true;\n    },\n    // 处理上传变更\n    handleFileChange: function handleFileChange(info) {\n      var fileList = _toConsumableArray(info.fileList);\n\n      // 限制最多5个文件\n      fileList = fileList.slice(-5);\n\n      // 更新文件状态\n      fileList = fileList.map(function (file) {\n        if (file.response) {\n          file.url = file.response.message;\n        }\n        return file;\n      });\n      this.finishForm.fileList = fileList;\n\n      // 将文件URL拼接成字符串保存到finishAnnex\n      if (fileList.length > 0) {\n        this.finishForm.finishAnnex = fileList.filter(function (file) {\n          return file.status === 'done' && file.response;\n        }).map(function (file) {\n          return file.response.message;\n        }).join(',');\n      } else {\n        this.finishForm.finishAnnex = '';\n      }\n    },\n    // 完成工单提交\n    handleFinishOk: function handleFinishOk() {\n      var _this14 = this;\n      if (!this.currentTask) return;\n      this.finishConfirmLoading = true;\n      var params = {\n        id: this.currentTask.id,\n        workStatus: 'completed'\n      };\n\n      // 添加附件和说明字段\n      if (this.finishForm.finishAnnex) {\n        params.finishAnnex = this.finishForm.finishAnnex;\n      }\n      if (this.finishForm.finishDescribe) {\n        params.finishDescribe = this.finishForm.finishDescribe;\n      }\n      putAction('/WorkOrderTask/edit', params).then(function (res) {\n        if (res.success) {\n          _this14.$message.success('工单已结束，等待审核！');\n          _this14.finishModalVisible = false;\n          _this14.loadTasks();\n          // 如果当前不是表格视图，也要重新加载数据\n          if (_this14.currentView !== 'table') {\n            _this14.loadAllTasksForView();\n          }\n          _this14.isShowAudit = false;\n        } else {\n          _this14.$message.error(res.message || '结束失败');\n        }\n      }).finally(function () {\n        _this14.finishConfirmLoading = false;\n      });\n\n      // httpAction('/WorkOrderTask/endWork', params, 'post').then(res => {\n      //   if (res.success) {\n      //     this.$message.success('工单已结束，等待审核');\n      //     this.currentTask.workStatus = 'completed';\n      //     this.currentTask.actualEndTime = endTime;\n      //     this.currentTask.actualHours = workingHours;\n      //     this.clearWorkingTimer(this.currentTask.id);\n      //     this.finishModalVisible = false;\n      //     this.loadTasks();\n      //   } else {\n      //     this.$message.error(res.message || '结束失败');\n      //   }\n      // }).finally(() => {\n      //   this.finishConfirmLoading = false;\n      // });\n    },\n    viewTaskDetail: function viewTaskDetail(task) {\n      this.$refs.taskDetailModal.show(task);\n    },\n    taskEdit: function taskEdit(task) {\n      var data = JSON.parse(JSON.stringify(task));\n      data.handling = data.handlingId;\n      this.$refs.taskModal.show(data);\n    },\n    taskEdit2: function taskEdit2(task) {\n      var data = JSON.parse(JSON.stringify(task));\n      data.handling = data.handlingId;\n      this.$refs.taskModal.show2(data);\n    },\n    viewProjectStats: function viewProjectStats() {\n      this.$refs.projectStatsModal.show(this.projectInfo);\n    },\n    goBack: function goBack() {\n      this.$router.go(-1);\n    },\n    startWorkingTimers: function startWorkingTimers() {\n      var _this15 = this;\n      this.myTasks.forEach(function (task) {\n        if (task.workStatus === 'working' && task.actualStartTime) {\n          _this15.startWorkingTimer(task);\n        }\n      });\n    },\n    startWorkingTimer: function startWorkingTimer(task) {\n      var _this16 = this;\n      var timer = setInterval(function () {\n        _this16.$forceUpdate();\n      }, 1000);\n      this.workingTimers.set(task.id, timer);\n    },\n    clearWorkingTimer: function clearWorkingTimer(taskId) {\n      var timer = this.workingTimers.get(taskId);\n      if (timer) {\n        clearInterval(timer);\n        this.workingTimers.delete(taskId);\n      }\n    },\n    clearWorkingTimers: function clearWorkingTimers() {\n      this.workingTimers.forEach(function (timer) {\n        return clearInterval(timer);\n      });\n      this.workingTimers.clear();\n    },\n    calculateWorkingHours: function calculateWorkingHours(startTime, endTime) {\n      var start = _moment(startTime);\n      var end = _moment(endTime);\n      return Math.round(end.diff(start, 'hours', true) * 100) / 100;\n    },\n    getWorkingDuration: function getWorkingDuration(startTime) {\n      var start = _moment(startTime);\n      var now = _moment();\n      var duration = _moment.duration(now.diff(start));\n      var hours = Math.floor(duration.asHours());\n      var minutes = duration.minutes();\n      return \"\".concat(hours, \"h \").concat(minutes, \"m\");\n    },\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        '0': '未开始',\n        '1': '进行中',\n        '2': '已完成',\n        '3': '已暂停'\n      };\n      return statusMap[status] || '未知';\n    },\n    getStatusClass: function getStatusClass(status) {\n      var classMap = {\n        '0': 'status-pending',\n        '1': 'status-active',\n        '2': 'status-completed',\n        '3': 'status-paused'\n      };\n      return classMap[status] || 'status-pending';\n    },\n    getModeText: function getModeText(mode) {\n      return mode === 'hours' ? '工时模式' : '排期模式';\n    },\n    getPriorityText: function getPriorityText(priority) {\n      var priorityMap = {\n        '2': '高',\n        '1': '中',\n        '0': '低'\n      };\n      return priorityMap[priority] || '中';\n    },\n    getPriorityClass: function getPriorityClass(priority) {\n      var classMap = {\n        '2': 'priority-high',\n        '1': 'priority-medium',\n        '0': 'priority-low'\n      };\n      return classMap[priority] || 'priority-medium';\n    },\n    getTaskStatusText: function getTaskStatusText(task) {\n      var statusMap = {\n        'pending': '待开始',\n        'working': '进行中',\n        'completed': '待审核',\n        'approved': '已完成',\n        'rejected': '已驳回'\n      };\n      return statusMap[task.workStatus] || '未知';\n    },\n    getTaskStatusClass: function getTaskStatusClass(task) {\n      var classMap = {\n        'pending': 'task-pending',\n        'working': 'task-working',\n        'completed': 'task-completed',\n        'approved': 'task-approved',\n        'rejected': 'task-rejected'\n      };\n      return classMap[task.workStatus] || 'task-pending';\n    },\n    formatDate: function formatDate(date) {\n      if (!date) return '未设置';\n      return _moment(date).format('MM-DD HH:mm');\n    }\n  }\n};", {"version": 3, "sources": ["ProjectDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8gBA,SAAA,WAAA,QAAA,eAAA;AACA,OAAA,kBAAA,MAAA,sBAAA;AACA,OAAA,eAAA,MAAA,mBAAA;AACA,OAAA,iBAAA,MAAA,qBAAA;AACA,OAAA,OAAA,MAAA,QAAA;AACA,SAAA,SAAA,EAAA,SAAA,EAAA,UAAA,QAAA,aAAA;AACA,SAAA,KAAA,QAAA,gBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,OAAA,GAAA,MAAA,KAAA;AAEA,eAAA;EACA,IAAA,EAAA,eAAA;EACA,MAAA,EAAA,CAAA,WAAA,CAAA;EACA,UAAA,EAAA;IACA,kBAAA,EAAA,kBAAA;IACA,eAAA,EAAA,eAAA;IACA,iBAAA,EAAA;EACA,CAAA;EACA,IAAA,WAAA,KAAA,EAAA;IACA,OAAA;MACA,SAAA,EAAA,MAAA;MACA,WAAA,EAAA,OAAA;MAAA;MACA,aAAA,EAAA,EAAA;MACA,WAAA,EAAA,CAAA,CAAA;MACA,iBAAA,EAAA,EAAA;MACA,UAAA,EAAA;QACA,UAAA,EAAA;MACA,CAAA;MACA,YAAA,KAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,uBAAA;MACA,aAAA,EAAA,CAAA,CAAA;MACA,kBAAA,EAAA,KAAA;MACA,oBAAA,EAAA,KAAA;MACA,WAAA,EAAA,IAAA;MACA,UAAA,EAAA;QACA,QAAA,EAAA,EAAA;QACA,cAAA,EAAA,EAAA;QACA,WAAA,EAAA;MACA,CAAA;MACA,SAAA,EAAA,EAAA;MACA,OAAA,EAAA,EAAA;MACA,aAAA,EAAA,IAAA,GAAA,CAAA,CAAA;MAEA;MACA,YAAA,EAAA,OAAA,CAAA,CAAA;MACA,QAAA,EAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,CAAA;MACA,eAAA,EAAA,KAAA;MACA,WAAA,EAAA,IAAA;MACA,gBAAA,EAAA,EAAA;MAEA;MACA,WAAA,EAAA;QACA,KAAA,EAAA,OAAA,CAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA;QACA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA;MACA,CAAA;MAEA;MACA,UAAA,EAAA,OAAA,CAAA,CAAA;MACA,qBAAA,EAAA,IAAA;MAAA;MACA,aAAA,GAAA,IAAA,EAAA,MAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,IAAA,CAAA;QAAA,MAAA,EAAA;MAAA,CAAA,EAAA,UAAA,CAAA,EAAA,CAAA;QAAA,OAAA,CAAA,CAAA,GAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;MAAA,EAAA,EAAA;MAAA;;MAEA,IAAA,EAAA;QACA,OAAA,EAAA,CACA;UAAA,KAAA,EAAA,MAAA;UAAA,KAAA,EAAA;QAAA,CAAA,EACA;UAAA,KAAA,EAAA,IAAA;UAAA,KAAA,EAAA;QAAA,CAAA,EACA;UAAA,KAAA,EAAA,KAAA;UAAA,KAAA,EAAA;QAAA,CAAA,EACA;UAAA,KAAA,EAAA,OAAA;UAAA,KAAA,EAAA;QAAA,CAAA,CACA;QACA,QAAA,EAAA,CACA;UAAA,KAAA,EAAA,MAAA;UAAA,KAAA,EAAA;QAAA,CAAA,EACA;UAAA,KAAA,EAAA,IAAA;UAAA,KAAA,EAAA;QAAA,CAAA;MAEA,CAAA;MACA,MAAA,EAAA,CAAA;MACA,QAAA,EAAA,CAAA;MACA,KAAA,EAAA,CAAA;MACA,SAAA,EAAA,CAAA;MACA,OAAA,EAAA,CAAA;MACA,UAAA,EAAA,CAAA;MACA,QAAA,EAAA,CAAA;MACA,QAAA,EAAA,QAAA;MACA,UAAA,EAAA,EAAA;MACA,WAAA,EAAA;QACA,OAAA,EAAA,CACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,WAAA;UACA,KAAA,EAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,KAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,UAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,KAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,UAAA;UACA,KAAA,EAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,IAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,gBAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,IAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,YAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,YAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,eAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,YAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,IAAA;UACA,SAAA,EAAA,QAAA;UACA,KAAA,EAAA,QAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,CACA;QACA,UAAA,EAAA,CACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,WAAA;UACA,KAAA,EAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,KAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,UAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,KAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,UAAA;UACA,KAAA,EAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,IAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,YAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,MAAA;UACA,KAAA,EAAA,QAAA;UACA,SAAA,EAAA,eAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA,EACA;UACA,KAAA,EAAA,IAAA;UACA,SAAA,EAAA,QAAA;UACA,KAAA,EAAA,QAAA;UACA,KAAA,EAAA,GAAA;UACA,WAAA,EAAA;YAAA,YAAA,EAAA;UAAA;QACA,CAAA;MAEA,CAAA;MACA,OAAA,EAAA,KAAA;MACA,eAAA,EAAA,EAAA;MACA,aAAA,EAAA,EAAA;MACA,WAAA,EAAA;QACA,OAAA,EAAA,CAAA;QACA,QAAA,EAAA,EAAA;QACA,KAAA,EAAA;MACA,CAAA;MACA,SAAA,EAAA;QACA,SAAA,EAAA,KAAA;QACA,SAAA,EAAA,KAAA;QACA,WAAA,EAAA,KAAA;QACA,UAAA,EAAA,KAAA;QACA,UAAA,EAAA;MACA,CAAA;MACA,gBAAA,EAAA;QACA,GAAA,EAAA,MAAA;QACA,GAAA,EAAA,MAAA;QACA,GAAA,EAAA;MACA,CAAA;MACA,WAAA,EAAA,KAAA;MACA,YAAA,EAAA,EAAA;MACA,SAAA,EAAA,EAAA;MACA,eAAA,EAAA;IACA,CAAA;EACA,CAAA;EACA,QAAA,EAAA;IACA,SAAA,WAAA,UAAA,EAAA;MACA,OAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,EAAA;IACA,CAAA;IAEA,MAAA,WAAA,OAAA,EAAA;MACA,OAAA,OAAA;IACA,CAAA;IAEA;IACA,YAAA,WAAA,aAAA,EAAA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA;MACA,IAAA,UAAA,GAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA;MACA,IAAA,SAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA;MACA,IAAA,OAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA;MAEA,IAAA,IAAA,GAAA,EAAA;MACA,IAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;MAEA,OAAA,OAAA,CAAA,cAAA,CAAA,OAAA,CAAA,EAAA;QACA,IAAA,QAAA,GAAA,IAAA,CAAA,eAAA,CAAA,OAAA,CAAA;QACA,IAAA,CAAA,IAAA,CAAA;UACA,IAAA,EAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;UACA,SAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA;UACA,cAAA,EAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,OAAA,CAAA;UACA,OAAA,EAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA;UACA,KAAA,EAAA;QACA,CAAA,CAAA;QACA,OAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA;MACA;MAEA,OAAA,IAAA;IACA,CAAA;IAEA;IACA,UAAA,WAAA,WAAA,EAAA;MACA,IAAA,KAAA,GAAA,EAAA;MACA,IAAA,OAAA,GAAA,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;MAEA,OAAA,OAAA,CAAA,cAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,EAAA;QACA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;QACA,OAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA;MACA;MAEA,OAAA,KAAA;IACA,CAAA;IAEA,UAAA,WAAA,WAAA,EAAA;MAAA,IAAA,KAAA;MACA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MACA,OAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;QAEA,OAAA,SAAA,CAAA,cAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,CAAA,IACA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,WAAA,CAAA,KAAA,CAAA;MACA,CAAA,CAAA;IACA;EACA,CAAA;EACA,OAAA,WAAA,QAAA,OAAA,EAAA;IACA,OAAA,CAAA,GAAA,CAAA,WAAA,CAAA;IACA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA;IACA,OAAA,CAAA,GAAA,CAAA,aAAA,CAAA;EACA,CAAA;EAEA,OAAA,WAAA,QAAA,EAAA;IACA,OAAA,CAAA,GAAA,CAAA,cAAA,CAAA;IACA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;IACA,IAAA,CAAA,eAAA,CAAA,CAAA;IACA,IAAA,CAAA,iBAAA,CAAA,CAAA;IACA,IAAA,CAAA,SAAA,CAAA,CAAA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA;;IAEA;IACA,IAAA,KAAA,GAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CAAA;IACA,IAAA,CAAA,aAAA,GAAA;MAAA,gBAAA,EAAA;IAAA,CAAA;EACA,CAAA;EAEA,aAAA,WAAA,cAAA,EAAA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA;EACA,CAAA;EAEA,OAAA,EAAA;IACA;IACA,UAAA,WAAA,WAAA,IAAA,EAAA;MACA,IAAA,CAAA,WAAA,GAAA,IAAA;;MAEA;MACA,IAAA,IAAA,KAAA,OAAA,EAAA;QACA,IAAA,CAAA,mBAAA,CAAA,CAAA;MACA;IACA,CAAA;IAEA,mBAAA,WAAA,oBAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,CAAA,OAAA,GAAA,IAAA;MACA;MACA,IAAA,CAAA,eAAA,GAAA,EAAA;MAEA,IAAA,MAAA,GAAA,aAAA,KAAA,IAAA,CAAA,UAAA,CAAA;;MAEA;MACA,IAAA,MAAA,CAAA,UAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,UAAA,CAAA,IAAA,MAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,MAAA,CAAA,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,CAAA;MACA;MAEA,MAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;MACA,MAAA,CAAA,OAAA,GAAA,IAAA,CAAA,SAAA,KAAA,MAAA,GAAA,EAAA,GAAA,IAAA,CAAA,SAAA,KAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,SAAA,KAAA,KAAA,GAAA,GAAA,GAAA,GAAA;MACA,MAAA,CAAA,MAAA,GAAA,CAAA;MACA,MAAA,CAAA,QAAA,GAAA,IAAA;MAEA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,EAAA;QAAA,MAAA,EAAA;MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,eAAA,GAAA,GAAA,CAAA,MAAA,CAAA,IAAA;QACA;QACA,MAAA,CAAA,OAAA,GAAA,KAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA;IACA,aAAA,WAAA,cAAA,EAAA;MACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,OAAA,CAAA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,EAAA;MACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAA,CAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,IAAA,EAAA;MACA,IAAA,OAAA,GAAA,IAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MAEA,OAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QAEA,OAAA,OAAA,IAAA,SAAA,IAAA,OAAA,IAAA,OAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,YAAA,WAAA,aAAA,GAAA,EAAA;MACA,IAAA,CAAA,WAAA,GAAA,GAAA;MACA,IAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,KAAA;MACA,IAAA,CAAA,eAAA,GAAA,IAAA;IACA,CAAA;IAEA;IACA,mBAAA,WAAA,oBAAA,EAAA;MACA,IAAA,CAAA,WAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,MAAA,CAAA;MACA,IAAA,CAAA,WAAA,CAAA,GAAA,GAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,MAAA,CAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,EAAA;MACA,IAAA,CAAA,WAAA,CAAA,KAAA,GAAA,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA;MACA,IAAA,CAAA,WAAA,CAAA,GAAA,GAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA;IACA,CAAA;IAEA,gBAAA,WAAA,iBAAA,IAAA,EAAA;MACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA;QAAA,OAAA,EAAA;MAAA,CAAA;MAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,WAAA,GAAA,IAAA,CAAA,WAAA,CAAA,KAAA;MACA,IAAA,SAAA,GAAA,IAAA,CAAA,WAAA,CAAA,GAAA;;MAEA;MACA,IAAA,SAAA,GAAA,SAAA,CAAA,IAAA,CAAA,WAAA,EAAA,MAAA,CAAA,GAAA,CAAA;MACA,IAAA,QAAA,GAAA,GAAA,GAAA,SAAA;;MAEA;MACA,IAAA,WAAA,GAAA,CAAA;MACA,IAAA,SAAA,CAAA,aAAA,CAAA,WAAA,CAAA,EAAA;QACA,WAAA,GAAA,SAAA,CAAA,IAAA,CAAA,WAAA,EAAA,MAAA,CAAA,GAAA,QAAA;MACA;;MAEA;MACA,IAAA,KAAA,GAAA,QAAA;MACA,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,EAAA;QACA,IAAA,YAAA,GAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA,CAAA;QACA,IAAA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,SAAA,CAAA;QACA,IAAA,WAAA,GAAA,UAAA,CAAA,IAAA,CAAA,YAAA,EAAA,MAAA,CAAA,GAAA,CAAA;QACA,KAAA,GAAA,WAAA,GAAA,QAAA;MACA;MAEA,OAAA;QACA,IAAA,KAAA,MAAA,CAAA,WAAA,MAAA;QACA,KAAA,KAAA,MAAA,CAAA,KAAA;MACA,CAAA;IACA,CAAA;IAEA;IACA,WAAA,WAAA,YAAA,EAAA;MACA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,KAAA,CAAA;MACA,IAAA,CAAA,0BAAA,CAAA,CAAA;IACA,CAAA;IAEA,OAAA,WAAA,QAAA,EAAA;MACA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA;MACA,IAAA,CAAA,0BAAA,CAAA,CAAA;IACA,CAAA;IAEA,WAAA,WAAA,YAAA,IAAA,EAAA;MACA,IAAA,IAAA,EAAA;QACA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAA,0BAAA,CAAA,CAAA;MACA;IACA,CAAA;IAEA,0BAAA,WAAA,2BAAA,EAAA;MACA;MACA,IAAA,CAAA,qBAAA,GAAA,IAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,IAAA,EAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MAEA,IAAA,KAAA,GAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;QACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;QAEA;QACA,IAAA,aAAA,KAAA,WAAA,EAAA;UACA,OAAA,KAAA,EAAA;QACA;;QAEA;QACA,IAAA,cAAA,KAAA,aAAA,EAAA;UACA,OAAA,KAAA;QACA;QAEA,IAAA,SAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,OAAA,GAAA,QAAA,CAAA,IAAA,CAAA;;QAEA;QACA,OAAA,OAAA,KAAA,SAAA;MACA,CAAA,CAAA;;MAEA;MACA,OAAA,IAAA,CAAA,yBAAA,CAAA,KAAA,EAAA,IAAA,CAAA;IACA,CAAA;IAEA;IACA,yBAAA,WAAA,0BAAA,KAAA,EAAA,IAAA,EAAA;MACA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;QACA,OAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA;UAAA,OAAA,aAAA,CAAA,aAAA,KAAA,IAAA;YAAA,WAAA,EAAA,CAAA;YAAA,YAAA,EAAA;UAAA;QAAA,CAAA,CAAA;MACA;;MAEA;MACA,IAAA,WAAA,GAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;QACA,IAAA,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA;QACA,IAAA,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA;QACA,OAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA;MACA,CAAA,CAAA;;MAEA;MACA,IAAA,OAAA,GAAA,EAAA;MAEA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,cAAA,GAAA,CAAA,CAAA;QACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;;QAEA;QACA,KAAA,IAAA,QAAA,GAAA,CAAA,EAAA,QAAA,GAAA,OAAA,CAAA,MAAA,EAAA,QAAA,EAAA,EAAA;UACA,IAAA,YAAA,GAAA,IAAA;UAAA,IAAA,SAAA,GAAA,0BAAA,CAEA,OAAA,CAAA,QAAA,CAAA;YAAA,KAAA;UAAA;YAAA,KAAA,SAAA,CAAA,CAAA,MAAA,KAAA,GAAA,SAAA,CAAA,CAAA,IAAA,IAAA,GAAA;cAAA,IAAA,YAAA,GAAA,KAAA,CAAA,KAAA;cACA,IAAA,aAAA,GAAA,OAAA,CAAA,YAAA,CAAA,SAAA,CAAA;cACA,IAAA,WAAA,GAAA,OAAA,CAAA,YAAA,CAAA,OAAA,CAAA;;cAEA;cACA,IAAA,SAAA,CAAA,QAAA,CAAA,WAAA,CAAA,IAAA,OAAA,CAAA,OAAA,CAAA,aAAA,CAAA,EAAA;gBACA,YAAA,GAAA,KAAA;gBACA;cACA;YACA;UAAA,SAAA,GAAA;YAAA,SAAA,CAAA,CAAA,CAAA,GAAA;UAAA;YAAA,SAAA,CAAA,CAAA;UAAA;UAEA,IAAA,YAAA,EAAA;YACA,cAAA,GAAA,QAAA;YACA;UACA;QACA;;QAEA;QACA,IAAA,cAAA,KAAA,CAAA,CAAA,EAAA;UACA,cAAA,GAAA,OAAA,CAAA,MAAA;UACA,OAAA,CAAA,IAAA,CAAA,EAAA,CAAA;QACA;QAEA,OAAA,CAAA,cAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAA,WAAA,GAAA,cAAA;QACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAA,CAAA,MAAA,CAAA,EAAA;MACA,CAAA,CAAA;;MAEA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAA,CAAA,MAAA,CAAA;MACA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,YAAA,GAAA,YAAA;MACA,CAAA,CAAA;MAEA,OAAA,WAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,KAAA,EAAA,IAAA,EAAA;MACA;MACA,OAAA,IAAA,CAAA,yBAAA,CAAA,KAAA,EAAA,IAAA,CAAA;IACA,CAAA;IAEA,4BAAA,WAAA,6BAAA,QAAA,EAAA;MAAA,IAAA,MAAA;MACA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,QAAA,CAAA,MAAA,CAAA;;MAEA;MACA,IAAA,WAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;QACA,IAAA,MAAA,GAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA;QACA,IAAA,MAAA,GAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA;QACA,OAAA,MAAA,CAAA,OAAA,CAAA,CAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA;MACA,CAAA,CAAA;MAEA,IAAA,OAAA,GAAA,EAAA;;MAEA;MACA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;QACA,IAAA,cAAA,GAAA,CAAA,CAAA;QAEA,OAAA,CAAA,GAAA,6BAAA,MAAA,CAAA,KAAA,GAAA,CAAA,QAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;QACA,OAAA,CAAA,GAAA,8BAAA,MAAA,CAAA,IAAA,CAAA,SAAA,SAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;;QAEA;QACA,KAAA,IAAA,QAAA,GAAA,CAAA,EAAA,QAAA,GAAA,OAAA,CAAA,MAAA,EAAA,QAAA,EAAA,EAAA;UACA,IAAA,YAAA,GAAA,IAAA;UAAA,IAAA,UAAA,GAAA,0BAAA,CAEA,OAAA,CAAA,QAAA,CAAA;YAAA,MAAA;UAAA;YAAA,KAAA,UAAA,CAAA,CAAA,MAAA,MAAA,GAAA,UAAA,CAAA,CAAA,IAAA,IAAA,GAAA;cAAA,IAAA,YAAA,GAAA,MAAA,CAAA,KAAA;cACA,IAAA,QAAA,GAAA,MAAA,CAAA,kBAAA,CAAA,IAAA,EAAA,YAAA,CAAA;cACA,OAAA,CAAA,GAAA,mCAAA,MAAA,CAAA,YAAA,CAAA,SAAA,2BAAA,MAAA,CAAA,QAAA,CAAA,CAAA;cAEA,IAAA,QAAA,EAAA;gBACA,YAAA,GAAA,KAAA;gBACA;cACA;YACA;UAAA,SAAA,GAAA;YAAA,UAAA,CAAA,CAAA,CAAA,GAAA;UAAA;YAAA,UAAA,CAAA,CAAA;UAAA;UAEA,IAAA,YAAA,EAAA;YACA,cAAA,GAAA,QAAA;YACA,OAAA,CAAA,GAAA,6BAAA,MAAA,CAAA,QAAA,CAAA,CAAA;YACA;UACA;QACA;;QAEA;QACA,IAAA,cAAA,KAAA,CAAA,CAAA,EAAA;UACA,cAAA,GAAA,OAAA,CAAA,MAAA;UACA,OAAA,CAAA,IAAA,CAAA,EAAA,CAAA;UACA,OAAA,CAAA,GAAA,6BAAA,MAAA,CAAA,cAAA,CAAA,CAAA;QACA;QAEA,OAAA,CAAA,cAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA;QACA,IAAA,CAAA,WAAA,GAAA,cAAA;QACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAA,CAAA,MAAA,CAAA,EAAA;MACA,CAAA,CAAA;;MAEA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAA,CAAA,MAAA,CAAA;MACA,WAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,YAAA,GAAA,YAAA;MACA,CAAA,CAAA;MAEA,OAAA,CAAA,GAAA,4DAAA,MAAA,CAAA,YAAA,CAAA,CAAA;MACA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,GAAA,CAAA,UAAA,GAAA,EAAA,KAAA;QAAA,OAAA;UACA,MAAA,EAAA,KAAA;UACA,KAAA,EAAA,GAAA,CAAA,GAAA,CAAA,UAAA,CAAA;YAAA,OAAA,CAAA,CAAA,SAAA;UAAA;QACA,CAAA;MAAA,CAAA,CAAA,CAAA;;MAEA;MACA,IAAA,CAAA,qBAAA,GAAA;QACA,IAAA,EAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,KAAA,EAAA;MACA,CAAA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,KAAA,EAAA,KAAA,EAAA;MACA;MACA,IAAA,WAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,MAAA,GAAA,IAAA,CAAA,0BAAA,CAAA,KAAA,EAAA,WAAA,CAAA;MACA,IAAA,MAAA,GAAA,IAAA,CAAA,0BAAA,CAAA,KAAA,EAAA,WAAA,CAAA;MAEA,IAAA,CAAA,MAAA,IAAA,CAAA,MAAA,EAAA;QACA,OAAA,CAAA,GAAA,iDAAA,MAAA,CAAA,CAAA,CAAA,MAAA,eAAA,MAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA;QACA,OAAA,KAAA;MACA;;MAEA;MACA;MACA,IAAA,aAAA,GAAA,EAAA,EAAA;MACA,IAAA,qBAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA;MACA,IAAA,mBAAA,GAAA,MAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,aAAA,EAAA,SAAA,CAAA;MACA,IAAA,qBAAA,GAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA;MACA,IAAA,mBAAA,GAAA,MAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,aAAA,EAAA,SAAA,CAAA;MAEA,IAAA,QAAA,GAAA,qBAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,IAAA,qBAAA,CAAA,QAAA,CAAA,mBAAA,CAAA;MAEA,OAAA,CAAA,GAAA,wCAAA,CAAA;MACA,OAAA,CAAA,GAAA,oBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,QAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA;MACA,OAAA,CAAA,GAAA,oBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,QAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA;MACA,OAAA,CAAA,GAAA,gCAAA,MAAA,CAAA,QAAA,CAAA,CAAA;MAEA,OAAA,QAAA;IACA,CAAA;IAEA,0BAAA,WAAA,2BAAA,IAAA,EAAA,IAAA,EAAA;MACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;MAEA;MACA,IAAA,IAAA,GAAA,aAAA,IAAA,IAAA,GAAA,WAAA,EAAA;QACA,OAAA,IAAA;MACA;;MAEA;MACA,IAAA,QAAA,GAAA,OAAA,IAAA,MAAA,CAAA,IAAA,cAAA,CAAA;MACA,IAAA,MAAA,GAAA,OAAA,IAAA,MAAA,CAAA,IAAA,cAAA,CAAA;MAEA,IAAA,YAAA,EAAA,UAAA;MAEA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,KAAA,WAAA,EAAA;QACA;QACA,YAAA,GAAA,SAAA;QACA,UAAA,GAAA,OAAA;MACA,CAAA,MAAA,IAAA,IAAA,KAAA,aAAA,EAAA;QACA;QACA,YAAA,GAAA,SAAA;QACA,UAAA,GAAA,MAAA;MACA,CAAA,MAAA,IAAA,IAAA,KAAA,WAAA,EAAA;QACA;QACA;QACA,YAAA,GAAA,QAAA;QACA,UAAA,GAAA,OAAA;MACA,CAAA,MAAA;QACA;QACA,YAAA,GAAA,QAAA;QACA,UAAA,GAAA,MAAA;MACA;;MAEA;MACA,OAAA,CAAA,GAAA,iBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,cAAA,MAAA,CAAA,IAAA,qCAAA,CAAA;MACA,OAAA,CAAA,GAAA,gCAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,SAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,CAAA,CAAA;MACA,OAAA,CAAA,GAAA,6CAAA,MAAA,CAAA,aAAA,qBAAA,MAAA,CAAA,IAAA,qBAAA,MAAA,CAAA,WAAA,CAAA,CAAA;MACA,OAAA,CAAA,GAAA,gCAAA,MAAA,CAAA,YAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA;MAEA,OAAA;QACA,KAAA,EAAA,YAAA;QACA,GAAA,EAAA;MACA,CAAA;IACA,CAAA;IAEA,wBAAA,WAAA,yBAAA,EAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MAEA,OAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QAEA,IAAA,aAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;QAEA;QACA,OAAA,cAAA,IAAA,aAAA,IAAA,cAAA,IAAA,WAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,oBAAA,WAAA,qBAAA,WAAA,EAAA,QAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,WAAA,GAAA,EAAA;MAEA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,MAAA,CAAA,kBAAA,CAAA,WAAA,EAAA,IAAA,EAAA,cAAA,CAAA,EAAA;UACA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA;QACA;MACA,CAAA,CAAA;MAEA,OAAA,WAAA,CAAA,IAAA,CAAA,CAAA,EAAA;IACA,CAAA;IAEA,mBAAA,WAAA,oBAAA,IAAA,EAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,QAAA,GAAA,OAAA,IAAA,MAAA,CAAA,cAAA,cAAA,CAAA,EAAA;MACA,IAAA,MAAA,GAAA,OAAA,IAAA,MAAA,CAAA,cAAA,cAAA,CAAA,EAAA;;MAEA,IAAA,YAAA,EAAA,UAAA;MAEA,IAAA,cAAA,KAAA,aAAA,IAAA,cAAA,KAAA,WAAA,EAAA;QACA,YAAA,GAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA;QACA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,CAAA;MACA,CAAA,MAAA,IAAA,cAAA,KAAA,aAAA,EAAA;QACA,YAAA,GAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,QAAA,CAAA;QACA,UAAA,GAAA,MAAA;MACA,CAAA,MAAA,IAAA,cAAA,KAAA,WAAA,EAAA;QACA,YAAA,GAAA,QAAA;QACA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,CAAA;MACA,CAAA,MAAA,IAAA,cAAA,GAAA,aAAA,IAAA,cAAA,GAAA,WAAA,EAAA;QACA,YAAA,GAAA,QAAA;QACA,UAAA,GAAA,MAAA;MACA,CAAA,MAAA;QACA,OAAA,IAAA;MACA;;MAEA;MACA,IAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;QACA,OAAA,IAAA;MACA;MAEA,OAAA;QAAA,KAAA,EAAA,YAAA;QAAA,GAAA,EAAA;MAAA,CAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,MAAA,EAAA,MAAA,EAAA;MACA;MACA,OAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,KAAA,EAAA,KAAA,EAAA,OAAA,EAAA;MACA;MACA,IAAA,eAAA,GAAA,SAAA,eAAA,CAAA,IAAA,EAAA;QACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;QACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QAEA,IAAA,OAAA,KAAA,aAAA,IAAA,OAAA,KAAA,WAAA,EAAA;UACA,OAAA;YAAA,KAAA,EAAA,SAAA;YAAA,GAAA,EAAA;UAAA,CAAA;QACA,CAAA,MAAA,IAAA,OAAA,KAAA,aAAA,EAAA;UACA,OAAA;YAAA,KAAA,EAAA,SAAA;YAAA,GAAA,EAAA,OAAA,IAAA,MAAA,CAAA,OAAA,cAAA;UAAA,CAAA;QACA,CAAA,MAAA,IAAA,OAAA,KAAA,WAAA,EAAA;UACA,OAAA;YAAA,KAAA,EAAA,OAAA,IAAA,MAAA,CAAA,OAAA,cAAA,CAAA;YAAA,GAAA,EAAA;UAAA,CAAA;QACA,CAAA,MAAA,IAAA,OAAA,GAAA,aAAA,IAAA,OAAA,GAAA,WAAA,EAAA;UACA,OAAA;YAAA,KAAA,EAAA,OAAA,IAAA,MAAA,CAAA,OAAA,cAAA,CAAA;YAAA,GAAA,EAAA,OAAA,IAAA,MAAA,CAAA,OAAA,cAAA;UAAA,CAAA;QACA;QACA,OAAA,IAAA;MACA,CAAA;MAEA,IAAA,MAAA,GAAA,eAAA,CAAA,KAAA,CAAA;MACA,IAAA,MAAA,GAAA,eAAA,CAAA,KAAA,CAAA;MAEA,IAAA,CAAA,MAAA,IAAA,CAAA,MAAA,EAAA,OAAA,KAAA;;MAEA;MACA,OAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA;IACA,CAAA;IAEA,gBAAA,WAAA,iBAAA,IAAA,EAAA;MACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;MAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,WAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;MAEA;MACA,IAAA,aAAA,KAAA,WAAA,IAAA,WAAA,KAAA,aAAA,EAAA;QACA,OAAA;UAAA,OAAA,EAAA;QAAA,CAAA;MACA;MAEA,IAAA,SAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAA,WAAA,GAAA,SAAA,CAAA,MAAA,CAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAA,SAAA,GAAA,OAAA,CAAA,MAAA,CAAA,CAAA;;MAEA;MACA,IAAA,WAAA,GAAA,WAAA,GAAA,EAAA,GAAA,GAAA,EAAA;;MAEA;;MAEA;MACA,IAAA,QAAA;MAEA,IAAA,SAAA,KAAA,OAAA,EAAA;QACA;QACA,IAAA,WAAA,GAAA,SAAA,GAAA,EAAA,GAAA,GAAA;QACA,QAAA,GAAA,WAAA,GAAA,WAAA;;QAEA;QACA,IAAA,gBAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,CAAA;QACA,IAAA,QAAA,GAAA,gBAAA,EAAA;UACA;UACA,QAAA,GAAA,gBAAA;QACA;MACA,CAAA,MAAA;QACA;QACA,IAAA,UAAA,GAAA,OAAA,GAAA,SAAA;QACA,IAAA,eAAA,GAAA,SAAA,GAAA,EAAA,GAAA,GAAA;QACA,QAAA,GAAA,UAAA,GAAA,GAAA,GAAA,eAAA,GAAA,WAAA;;QAEA;QACA,IAAA,iBAAA,GAAA,IAAA,CAAA,sBAAA,CAAA,CAAA;QACA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,EAAA,iBAAA,CAAA;MACA;;MAEA;MACA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,EAAA,EAAA,CAAA,EAAA;;MAEA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,YAAA,IAAA,CAAA;MACA,IAAA,WAAA,GAAA,IAAA,CAAA,WAAA,IAAA,CAAA;;MAEA;MACA,IAAA,cAAA,GAAA,GAAA,EAAA;MACA,IAAA,SAAA,GAAA,CAAA,EAAA;MACA,IAAA,aAAA,GAAA,CAAA,YAAA,GAAA,CAAA,IAAA,SAAA;MACA,IAAA,cAAA,GAAA,cAAA,GAAA,aAAA;MACA,IAAA,WAAA,GAAA,cAAA,GAAA,YAAA;;MAEA;MACA,IAAA,UAAA,GAAA,WAAA,IAAA,WAAA,GAAA,SAAA,CAAA;MAEA,IAAA,UAAA,GAAA;QACA,GAAA,KAAA,MAAA,CAAA,WAAA,OAAA;QACA,MAAA,KAAA,MAAA,CAAA,QAAA,OAAA;QACA,QAAA,EAAA,UAAA;QACA,KAAA,KAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,WAAA,EAAA,EAAA,CAAA,MAAA;QAAA;QACA,IAAA,KAAA,MAAA,CAAA,UAAA,MAAA;QAAA;QACA,MAAA,EAAA,KAAA,GAAA,WAAA;QACA,QAAA,EAAA,SAAA;QAAA;QACA,SAAA,KAAA,MAAA,CAAA,QAAA,OAAA,CAAA;MACA,CAAA;MAEA,OAAA,UAAA;IACA,CAAA;IAEA;IACA,sBAAA,WAAA,uBAAA,EAAA;MACA;MACA,IAAA,WAAA,GAAA,EAAA,EAAA;MACA,IAAA,aAAA,GAAA,EAAA,EAAA;MACA,IAAA,WAAA,GAAA,EAAA,EAAA;MACA,IAAA,IAAA,GAAA,EAAA,EAAA;;MAEA;MACA,IAAA,kBAAA,GAAA,WAAA,GAAA,aAAA,GAAA,WAAA,GAAA,IAAA;;MAEA;MACA,OAAA,IAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,EAAA,CAAA;IACA,CAAA;IAEA;IACA,6BAAA,WAAA,8BAAA,EAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MAEA,OAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;QACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;QAEA;QACA,IAAA,UAAA,GAAA,aAAA,KAAA,WAAA;QACA,IAAA,eAAA,GAAA,cAAA,IAAA,aAAA,IAAA,cAAA,IAAA,WAAA;QAEA,OAAA,UAAA,IAAA,eAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA;IACA,yBAAA,WAAA,0BAAA,IAAA,EAAA;MACA,IAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,YAAA,GAAA,IAAA,CAAA,eAAA,IAAA,IAAA,CAAA,UAAA;MAEA,IAAA,KAAA,GAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,KAAA;QAEA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;QACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;QACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;QACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;QAEA;QACA,IAAA,SAAA,GAAA,aAAA,KAAA,WAAA,IAAA,aAAA,KAAA,cAAA;QACA,IAAA,CAAA,SAAA,EAAA,OAAA,KAAA;;QAEA;QACA,IAAA,SAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAA,OAAA,GAAA,QAAA,CAAA,IAAA,CAAA;QAEA,OAAA,OAAA,KAAA,SAAA;MACA,CAAA,CAAA;MAEA,OAAA,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,IAAA,CAAA;IACA,CAAA;IAEA;IACA,sBAAA,WAAA,uBAAA,IAAA,EAAA;MACA,IAAA,WAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,aAAA,GAAA,SAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,WAAA,GAAA,OAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MAEA,IAAA,WAAA,KAAA,aAAA,IAAA,WAAA,KAAA,WAAA,EAAA;QACA;QACA,UAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA;MACA,CAAA,MAAA,IAAA,WAAA,KAAA,aAAA,EAAA;QACA;QACA,UAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,OAAA,CAAA;MACA,CAAA,MAAA,IAAA,WAAA,KAAA,WAAA,EAAA;QACA;QACA,yBAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA;MACA,CAAA,MAAA;QACA;QACA;MACA;IACA,CAAA;IAEA;IACA,uBAAA,WAAA,wBAAA,IAAA,EAAA;MACA,IAAA,SAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;MAEA,IAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;MACA,IAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;MACA,IAAA,OAAA,GAAA,QAAA,CAAA,OAAA,CAAA,CAAA;MAEA,IAAA,IAAA,GAAA,CAAA,EAAA;QACA,UAAA,MAAA,CAAA,IAAA,YAAA,MAAA,CAAA,KAAA;MACA,CAAA,MAAA,IAAA,KAAA,GAAA,CAAA,EAAA;QACA,UAAA,MAAA,CAAA,KAAA,kBAAA,MAAA,CAAA,OAAA;MACA,CAAA,MAAA;QACA,UAAA,MAAA,CAAA,OAAA;MACA;IACA,CAAA;IAEA;IACA,oBAAA,WAAA,qBAAA,IAAA,EAAA,KAAA,EAAA;MACA,IAAA,UAAA,GAAA,IAAA,CAAA,6BAAA,CAAA,CAAA,CAAA,MAAA;MACA,IAAA,SAAA,GAAA,IAAA,CAAA,GAAA,CAAA,EAAA,GAAA,UAAA,EAAA,EAAA,CAAA,EAAA;MACA,IAAA,UAAA,GAAA,KAAA,IAAA,SAAA,GAAA,GAAA,CAAA,EAAA;;MAEA,OAAA;QACA,QAAA,EAAA,UAAA;QACA,KAAA,KAAA,MAAA,CAAA,SAAA,MAAA;QACA,QAAA,EAAA,OAAA;QACA,WAAA,EAAA,MAAA;QACA,YAAA,EAAA,KAAA;QACA,OAAA,EAAA,cAAA;QACA,aAAA,EAAA,KAAA;QACA,MAAA,EAAA,GAAA,GAAA;MACA,CAAA;IACA,CAAA;IAEA;IACA,iBAAA,WAAA,kBAAA,IAAA,EAAA;MAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,QAAA,SAAA,QAAA,SAAA,GAAA,SAAA,MAAA,CAAA;MACA;MACA,IAAA,YAAA,GAAA,CACA,iBAAA,EACA,mBAAA,EACA,kBAAA,EACA,mBAAA,EACA,iBAAA,EACA,iBAAA,EACA,gBAAA,EACA,mBAAA,CACA;;MAEA;MACA,IAAA,YAAA,GAAA;QACA,SAAA,EAAA,mBAAA;QACA,SAAA,EAAA,iBAAA;QACA,WAAA,EAAA,mBAAA;QACA,UAAA,EAAA,kBAAA;QACA,UAAA,EAAA;MACA,CAAA;;MAEA;MACA,OAAA,YAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,YAAA,CAAA,KAAA,GAAA,YAAA,CAAA,MAAA,CAAA;IACA,CAAA;IAIA,cAAA,WAAA,eAAA,IAAA,EAAA;MACA,IAAA,CAAA,IAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA;MACA,IAAA,KAAA,GAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA;MACA,IAAA,GAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA;MACA,IAAA,SAAA,GAAA,KAAA,CAAA,MAAA,CAAA,YAAA,CAAA;MACA,IAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;MAEA;MACA,IAAA,SAAA,KAAA,OAAA,EAAA;QACA,UAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,OAAA,MAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA;MACA;;MAEA;MACA,UAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,WAAA,CAAA,OAAA,MAAA,CAAA,GAAA,CAAA,MAAA,CAAA,WAAA,CAAA;IACA,CAAA;IAIA;IACA,WAAA,WAAA,YAAA,EAAA;MACA,IAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IAEA,WAAA,WAAA,YAAA,EAAA;MACA,IAAA,CAAA,UAAA,GAAA;QACA,UAAA,EAAA;MACA,CAAA;MACA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,CAAA;IACA,CAAA;IAEA,KAAA,WAAA,MAAA,EAAA,EAAA,IAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,IAAA,KAAA,GAAA,EAAA;QACA,KAAA,CAAA,OAAA,CAAA;UACA,KAAA,EAAA,MAAA;UACA,IAAA,EAAA,EAAA;UACA,OAAA,EAAA,aAAA;UACA,IAAA,EAAA,SAAA,KAAA,EAAA;YACA,MAAA,CAAA,gBAAA,CAAA,EAAA,EAAA,UAAA,CAAA;UACA;QACA,CAAA,CAAA;MACA,CAAA,MAAA,IAAA,IAAA,KAAA,GAAA,EAAA;QACA,IAAA,CAAA,WAAA,GAAA,IAAA;QACA,IAAA,CAAA,SAAA,GAAA,EAAA;MACA,CAAA,MAAA,IAAA,IAAA,KAAA,GAAA,EAAA;QACA,IAAA,IAAA,CAAA,YAAA,CAAA,MAAA,KAAA,CAAA,EAAA;UACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA;UACA;QACA;QACA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,EAAA,UAAA,CAAA;MACA;IACA,CAAA;IAEA,gBAAA,WAAA,iBAAA,EAAA,EAAA,MAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,MAAA,GAAA,CAAA,CAAA;MACA,MAAA,CAAA,EAAA,GAAA,EAAA;MACA,MAAA,CAAA,UAAA,GAAA,MAAA;MACA,IAAA,MAAA,KAAA,UAAA,EAAA;QACA,MAAA,CAAA,aAAA,GAAA,IAAA,CAAA,YAAA;MACA;MACA,SAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,CACA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA;UACA,MAAA,CAAA,SAAA,CAAA,CAAA;UACA;UACA,IAAA,MAAA,CAAA,WAAA,KAAA,OAAA,EAAA;YACA,MAAA,CAAA,mBAAA,CAAA,CAAA;UACA;UACA,MAAA,CAAA,WAAA,GAAA,KAAA;QACA,CAAA,MAAA;UACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA,CAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,OAAA,EAAA,QAAA,EAAA;MACA,IAAA,CAAA,WAAA,GAAA,OAAA;MACA,IAAA,CAAA,SAAA,CAAA,CAAA;IACA,CAAA;IAEA,cAAA,WAAA,eAAA,eAAA,EAAA,aAAA,EAAA;MACA,IAAA,CAAA,eAAA,GAAA,eAAA;MACA,IAAA,CAAA,aAAA,GAAA,aAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,MAAA,EAAA;MACA,OAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,KAAA;IACA,CAAA;IAEA,oBAAA,WAAA,qBAAA,KAAA,EAAA;MACA,OAAA,IAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,IAAA,KAAA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,CAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,CAAA,UAAA,GAAA,EAAA;MACA,IAAA,CAAA,OAAA,GAAA,IAAA;MACA,IAAA,CAAA,SAAA,GAAA,CAAA;;MAEA;MACA,IAAA,CAAA,KAAA,MAAA,IAAA,CAAA,KAAA,OAAA,EAAA;QACA,IAAA,CAAA,WAAA,GAAA,OAAA;MACA;MAEA,IAAA,CAAA,WAAA,CAAA,CAAA;MACA,IAAA,CAAA,SAAA,CAAA,CAAA;;MAEA;MACA,IAAA,IAAA,CAAA,WAAA,KAAA,OAAA,EAAA;QACA;QACA,IAAA,CAAA,SAAA,CAAA,YAAA;UACA,MAAA,CAAA,mBAAA,CAAA,CAAA;QACA,CAAA,CAAA;MACA;IACA,CAAA;IACA,eAAA,WAAA,gBAAA,EAAA;MAAA,IAAA,MAAA;MACA,SAAA,uBAAA;QAAA,EAAA,EAAA,IAAA,CAAA;MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,MAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,EAAA;MAAA,IAAA,MAAA;MACA,SAAA,wBAAA;QAAA,SAAA,EAAA,IAAA,CAAA;MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,MAAA;UACA,MAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;YACA,IAAA,IAAA,CAAA,OAAA,KAAA,MAAA,CAAA,aAAA,EAAA;cACA,MAAA,CAAA,QAAA,GAAA,IAAA,CAAA,IAAA;cACA;YACA;UACA,CAAA,CAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,CAAA,OAAA,GAAA,IAAA;MACA,IAAA,MAAA,GAAA,aAAA,KAAA,IAAA,CAAA,UAAA,CAAA;;MAEA;MACA,IAAA,MAAA,CAAA,UAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,UAAA,CAAA,IAAA,MAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,MAAA,CAAA,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,CAAA;MACA;MAEA,MAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;MACA,MAAA,CAAA,OAAA,GAAA,IAAA,CAAA,SAAA,KAAA,MAAA,GAAA,EAAA,GAAA,IAAA,CAAA,SAAA,KAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,SAAA,KAAA,KAAA,GAAA,GAAA,GAAA,GAAA;MACA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,WAAA,CAAA,OAAA;MACA,MAAA,CAAA,QAAA,GAAA,IAAA,CAAA,WAAA,CAAA,QAAA;MACA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,EAAA;QACA,MAAA,EAAA;MACA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,OAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA,CAAA,IAAA;UACA,IAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,KAAA;UACA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA;UACA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;UACA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA;UACA,OAAA,CAAA,UAAA,GAAA,OAAA,CAAA,UAAA;UACA,OAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,MAAA,CAAA,KAAA;QACA,CAAA,MAAA;UACA,OAAA,CAAA,UAAA,GAAA,EAAA;QACA;QACA,OAAA,CAAA,OAAA,GAAA,KAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,aAAA,WAAA,cAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA;IACA,CAAA;IAEA,WAAA,WAAA,YAAA,EAAA;MACA,IAAA,CAAA,SAAA,CAAA,CAAA;MACA;MACA,IAAA,IAAA,CAAA,WAAA,KAAA,OAAA,EAAA;QACA,IAAA,CAAA,mBAAA,CAAA,CAAA;MACA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,IAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,CAAA,QAAA,CAAA;QACA,KAAA,EAAA,MAAA;QACA,OAAA,iDAAA,MAAA,CAAA,IAAA,CAAA,SAAA,mBAAA;QACA,IAAA,EAAA,SAAA,KAAA,EAAA;UACA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,sBAAA,EAAA;YACA,EAAA,EAAA,IAAA,CAAA;UACA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;YACA,IAAA,GAAA,CAAA,OAAA,EAAA;cACA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,CAAA;cACA,OAAA,CAAA,SAAA,CAAA,CAAA;cACA,IAAA,OAAA,CAAA,WAAA,KAAA,OAAA,EAAA;gBACA,OAAA,CAAA,mBAAA,CAAA,CAAA;cACA;YACA,CAAA,MAAA;cACA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA;YACA;UACA,CAAA,CAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,IAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,0BAAA,EAAA;QACA,EAAA,EAAA,IAAA,CAAA,EAAA;QACA,SAAA,EAAA,IAAA,IAAA,CAAA;MACA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA;UACA,IAAA,CAAA,UAAA,GAAA,SAAA;UACA,IAAA,CAAA,eAAA,GAAA,IAAA,IAAA,CAAA,CAAA;UACA,OAAA,CAAA,iBAAA,CAAA,IAAA,CAAA;QACA,CAAA,MAAA;UACA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,IAAA,EAAA;MACA,IAAA,CAAA,WAAA,GAAA,IAAA;MACA,IAAA,CAAA,UAAA,GAAA;QACA,QAAA,EAAA,EAAA;QACA,cAAA,EAAA,EAAA;QACA,WAAA,EAAA;MACA,CAAA;MACA,IAAA,CAAA,kBAAA,GAAA,IAAA;IACA,CAAA;IAEA;IACA,UAAA,WAAA,WAAA,CAAA,EAAA;MAAA,IAAA,OAAA;MACA,CAAA,CAAA,cAAA,CAAA,CAAA;MACA,IAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,YAAA,CAAA,KAAA,CAAA;MACA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA;IACA,YAAA,WAAA,aAAA,IAAA,EAAA;MACA,IAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,GAAA;MACA,IAAA,CAAA,QAAA,EAAA;QACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA,CAAA;QACA,OAAA,KAAA;MACA;MACA,OAAA,IAAA;IACA,CAAA;IAEA;IACA,gBAAA,WAAA,iBAAA,IAAA,EAAA;MACA,IAAA,QAAA,GAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA;;MAEA;MACA,QAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;MAEA;MACA,QAAA,GAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,QAAA,EAAA;UACA,IAAA,CAAA,GAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;QACA;QACA,OAAA,IAAA;MACA,CAAA,CAAA;MAEA,IAAA,CAAA,UAAA,CAAA,QAAA,GAAA,QAAA;;MAEA;MACA,IAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,QAAA,CACA,MAAA,CAAA,UAAA,IAAA;UAAA,OAAA,IAAA,CAAA,MAAA,KAAA,MAAA,IAAA,IAAA,CAAA,QAAA;QAAA,EAAA,CACA,GAAA,CAAA,UAAA,IAAA;UAAA,OAAA,IAAA,CAAA,QAAA,CAAA,OAAA;QAAA,EAAA,CACA,IAAA,CAAA,GAAA,CAAA;MACA,CAAA,MAAA;QACA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,EAAA;MACA;IACA,CAAA;IAEA;IACA,cAAA,WAAA,eAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA;MAEA,IAAA,CAAA,oBAAA,GAAA,IAAA;MACA,IAAA,MAAA,GAAA;QACA,EAAA,EAAA,IAAA,CAAA,WAAA,CAAA,EAAA;QACA,UAAA,EAAA;MACA,CAAA;;MAEA;MACA,IAAA,IAAA,CAAA,UAAA,CAAA,WAAA,EAAA;QACA,MAAA,CAAA,WAAA,GAAA,IAAA,CAAA,UAAA,CAAA,WAAA;MACA;MAEA,IAAA,IAAA,CAAA,UAAA,CAAA,cAAA,EAAA;QACA,MAAA,CAAA,cAAA,GAAA,IAAA,CAAA,UAAA,CAAA,cAAA;MACA;MAEA,SAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,CACA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA,CAAA;UACA,OAAA,CAAA,kBAAA,GAAA,KAAA;UACA,OAAA,CAAA,SAAA,CAAA,CAAA;UACA;UACA,IAAA,OAAA,CAAA,WAAA,KAAA,OAAA,EAAA;YACA,OAAA,CAAA,mBAAA,CAAA,CAAA;UACA;UACA,OAAA,CAAA,WAAA,GAAA,KAAA;QACA,CAAA,MAAA;UACA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,OAAA,IAAA,MAAA,CAAA;QACA;MACA,CAAA,CAAA,CAAA,OAAA,CAAA,YAAA;QACA,OAAA,CAAA,oBAAA,GAAA,KAAA;MACA,CAAA,CAAA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAAA;IAEA,cAAA,WAAA,eAAA,IAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,eAAA,CAAA,IAAA,CAAA,IAAA,CAAA;IACA,CAAA;IAEA,QAAA,WAAA,SAAA,IAAA,EAAA;MACA,IAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA;IACA,CAAA;IAEA,SAAA,WAAA,UAAA,IAAA,EAAA;MACA,IAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA;IACA,CAAA;IAEA,gBAAA,WAAA,iBAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA;IACA,CAAA;IAEA,MAAA,WAAA,OAAA,EAAA;MACA,IAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;QACA,IAAA,IAAA,CAAA,UAAA,KAAA,SAAA,IAAA,IAAA,CAAA,eAAA,EAAA;UACA,OAAA,CAAA,iBAAA,CAAA,IAAA,CAAA;QACA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,IAAA,EAAA;MAAA,IAAA,OAAA;MACA,IAAA,KAAA,GAAA,WAAA,CAAA,YAAA;QACA,OAAA,CAAA,YAAA,CAAA,CAAA;MACA,CAAA,EAAA,IAAA,CAAA;MACA,IAAA,CAAA,aAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,CAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,MAAA,EAAA;MACA,IAAA,KAAA,GAAA,IAAA,CAAA,aAAA,CAAA,GAAA,CAAA,MAAA,CAAA;MACA,IAAA,KAAA,EAAA;QACA,aAAA,CAAA,KAAA,CAAA;QACA,IAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA;MACA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,EAAA;MACA,IAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,KAAA;QAAA,OAAA,aAAA,CAAA,KAAA,CAAA;MAAA,EAAA;MACA,IAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;IACA,CAAA;IAEA,qBAAA,WAAA,sBAAA,SAAA,EAAA,OAAA,EAAA;MACA,IAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA;MACA,IAAA,GAAA,GAAA,OAAA,CAAA,OAAA,CAAA;MACA,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,OAAA,EAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,GAAA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,SAAA,EAAA;MACA,IAAA,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA;MACA,IAAA,GAAA,GAAA,OAAA,CAAA,CAAA;MACA,IAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;MACA,IAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA,CAAA;MACA,IAAA,OAAA,GAAA,QAAA,CAAA,OAAA,CAAA,CAAA;MACA,UAAA,MAAA,CAAA,KAAA,QAAA,MAAA,CAAA,OAAA;IACA,CAAA;IAEA,aAAA,WAAA,cAAA,MAAA,EAAA;MACA,IAAA,SAAA,GAAA;QACA,GAAA,EAAA,KAAA;QACA,GAAA,EAAA,KAAA;QACA,GAAA,EAAA,KAAA;QACA,GAAA,EAAA;MACA,CAAA;MACA,OAAA,SAAA,CAAA,MAAA,CAAA,IAAA,IAAA;IACA,CAAA;IAEA,cAAA,WAAA,eAAA,MAAA,EAAA;MACA,IAAA,QAAA,GAAA;QACA,GAAA,EAAA,gBAAA;QACA,GAAA,EAAA,eAAA;QACA,GAAA,EAAA,kBAAA;QACA,GAAA,EAAA;MACA,CAAA;MACA,OAAA,QAAA,CAAA,MAAA,CAAA,IAAA,gBAAA;IACA,CAAA;IAEA,WAAA,WAAA,YAAA,IAAA,EAAA;MACA,OAAA,IAAA,KAAA,OAAA,GAAA,MAAA,GAAA,MAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,QAAA,EAAA;MACA,IAAA,WAAA,GAAA;QACA,GAAA,EAAA,GAAA;QACA,GAAA,EAAA,GAAA;QACA,GAAA,EAAA;MACA,CAAA;MACA,OAAA,WAAA,CAAA,QAAA,CAAA,IAAA,GAAA;IACA,CAAA;IAEA,gBAAA,WAAA,iBAAA,QAAA,EAAA;MACA,IAAA,QAAA,GAAA;QACA,GAAA,EAAA,eAAA;QACA,GAAA,EAAA,iBAAA;QACA,GAAA,EAAA;MACA,CAAA;MACA,OAAA,QAAA,CAAA,QAAA,CAAA,IAAA,iBAAA;IACA,CAAA;IAEA,iBAAA,WAAA,kBAAA,IAAA,EAAA;MACA,IAAA,SAAA,GAAA;QACA,SAAA,EAAA,KAAA;QACA,SAAA,EAAA,KAAA;QACA,WAAA,EAAA,KAAA;QACA,UAAA,EAAA,KAAA;QACA,UAAA,EAAA;MACA,CAAA;MACA,OAAA,SAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,IAAA;IACA,CAAA;IAEA,kBAAA,WAAA,mBAAA,IAAA,EAAA;MACA,IAAA,QAAA,GAAA;QACA,SAAA,EAAA,cAAA;QACA,SAAA,EAAA,cAAA;QACA,WAAA,EAAA,gBAAA;QACA,UAAA,EAAA,eAAA;QACA,UAAA,EAAA;MACA,CAAA;MACA,OAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,cAAA;IACA,CAAA;IAEA,UAAA,WAAA,WAAA,IAAA,EAAA;MACA,IAAA,CAAA,IAAA,EAAA,OAAA,KAAA;MACA,OAAA,OAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,aAAA,CAAA;IACA;EACA;AACA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"project-detail-app\">\r\n    <!-- 项目信息头部 -->\r\n    <div class=\"project-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"project-info\">\r\n          <button class=\"back-btn\" @click=\"goBack\">\r\n            ← 返回项目列表\r\n          </button>\r\n          <h1 class=\"project-title\">{{ projectInfo.projectName }}</h1>\r\n          <div class=\"project-meta\">\r\n            <span class=\"project-mode\">{{ getModeText(projectInfo.mode) }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <button class=\"btn-secondary\" v-if=\"projectInfo.mode==='hours'\" @click=\"viewProjectStats\">工时统计</button>\r\n          <button class=\"btn-primary\" v-if=\"userRole==='admin'\" @click=\"handleAddTask\">新建工单</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-content\">\r\n        <div class=\"filter-row\">\r\n          <div class=\"filter-group\" v-if=\"userRole === 'admin' && activeTab === 'all'\">\r\n            <label>处理人</label>\r\n            <a-select v-model=\"queryParam.handling\" placeholder=\"请选择处理人\">\r\n              <a-select-option v-for=\"d in projectMemberList\" :key=\"d.id\" :value=\"d.user_id\">\r\n                {{ d.realname }}\r\n              </a-select-option>\r\n            </a-select>\r\n          </div>\r\n\r\n          <div class=\"filter-group\">\r\n            <label>状态</label>\r\n            <a-select \r\n              v-model=\"queryParam.workStatus\" \r\n              placeholder=\"请选择状态\" \r\n              mode=\"multiple\"\r\n              class=\"multi-select-wrap\">\r\n              <a-select-option value=\"pending\">待处理</a-select-option>\r\n              <a-select-option value=\"working\">进行中</a-select-option>\r\n              <a-select-option value=\"completed\">待审核</a-select-option>\r\n              <a-select-option value=\"approved\">已审核</a-select-option>\r\n              <a-select-option value=\"rejected\">已驳回</a-select-option>\r\n            </a-select>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <button class=\"btn-primary\" @click=\"searchQuery\">搜索</button>\r\n            <button class=\"btn-ghost\" @click=\"searchReset\">重置</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 标签页切换 -->\r\n    <div class=\"tabs-container\">\r\n      <div class=\"tabs-header\">\r\n        <div class=\"tabs-left\">\r\n          <button\r\n            v-for=\"tab in tabs[this.userRole]\"\r\n            :key=\"tab.value\"\r\n            :class=\"['tab-btn', { active: activeTab === tab.value }]\"\r\n            @click=\"switchTab(tab.value)\"\r\n          >\r\n            {{ tab.label }}\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'pool'\">{{ poolTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'my'\">{{ myTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'all'\">{{ allTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'audit'\">{{ auditTotal }}</span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 视图切换按钮 - 仅在排期模式和全部工单标签页显示 -->\r\n        <div class=\"view-switcher\" v-if=\"projectInfo.mode === 'timespan' && (activeTab === 'all' || activeTab === 'my')\">\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'table' }]\"\r\n            @click=\"switchView('table')\"\r\n            title=\"表格视图\"\r\n          >\r\n            <i class=\"icon-table\">表格</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'calendar' }]\"\r\n            @click=\"switchView('calendar')\"\r\n            title=\"月历视图\"\r\n          >\r\n            <i class=\"icon-calendar\">月历</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'daily' }]\"\r\n            @click=\"switchView('daily')\"\r\n            title=\"按天视图\"\r\n          >\r\n            <i class=\"icon-daily\">按天</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'gantt' }]\"\r\n            @click=\"switchView('gantt')\"\r\n            title=\"甘特图\"\r\n          >\r\n            <i class=\"icon-gantt\">甘特图</i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div class=\"table-container\" v-show=\"currentView === 'table'\">\r\n        <a-spin :spinning=\"loading\" class=\"table-loading\">\r\n          <a-table\r\n            ref=\"table\"\r\n            size=\"middle\"\r\n            bordered\r\n            :rowKey=\"record => record.id\"\r\n            :columns=\"dataColumns[projectInfo.mode]\"\r\n            :dataSource=\"dataSource\"\r\n            :pagination=\"ipagination\"\r\n            class=\"enhanced-table\"\r\n            @change=\"handleTableChange\"\r\n          >\r\n            <template slot=\"prioritySlot\" slot-scope=\"text\">\r\n              <span class=\"priority-tag\" :class=\"'priority-' + text\">{{ getPriorityText(text) }}</span>\r\n            </template>\r\n\r\n            <template slot=\"workHourSlot\" slot-scope=\"text\">\r\n              <span class=\"work-hour\">{{ text }}h</span>\r\n            </template>\r\n\r\n            <template slot=\"workStatus\" slot-scope=\"text\">\r\n              <span class=\"status-tag\" :class=\"'status-' + text\">{{ getWorkStatusText(text) }}</span>\r\n            </template>\r\n\r\n            <template slot=\"plannedTimeSlot\" slot-scope=\"text, record\">\r\n              <div class=\"time-range\">\r\n                <div class=\"time-start\">{{ record.startTime }}</div>\r\n                <div class=\"time-divider\">至</div>\r\n                <div class=\"time-end\">{{ record.endTime }}</div>\r\n              </div>\r\n            </template>\r\n\r\n            <template slot=\"reviewTime\" slot-scope=\"text, record\">\r\n              <span v-if=\"record.workStatus === 'approved'\" class=\"status-tag\" :class=\"'status-' + text\">{{ text }}</span>\r\n            </template>\r\n\r\n            <!-- 操作列 -->\r\n            <template slot=\"action\" slot-scope=\"text, record\">\r\n              <div class=\"action-buttons\">\r\n                <a v-if=\"activeTab === 'pool'\" @click=\"claimTask(record)\" class=\"action-link claim\">领取</a>\r\n\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'rejected'\"\r\n                   @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link restart\">重新开始</a>\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'pending'\"\r\n                   @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link start\">开始</a>\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'working'\"\r\n                   @click=\"showFinishModal(record)\" class=\"action-link complete\">结束</a>\r\n\r\n                <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '1')\" class=\"action-link approve\">通过</a>\r\n                <a-divider v-if=\"activeTab === 'audit'\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '2')\" class=\"action-link reject\">拒绝</a>\r\n\r\n                <a-divider v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" @click=\"taskEdit(record)\" class=\"action-link edit\">编辑</a>\r\n\r\n                <a v-if=\"userRole === 'admin' && (activeTab === 'all' && record.workStatus === 'pending')\" @click=\"taskEdit2(record)\" class=\"action-link edit\">转交负责人</a>\r\n\r\n                <a-divider v-if=\"(activeTab !== 'all' && record.workStatus !== 'approved' && (record.workStatus !== 'completed' || activeTab === 'audit'))\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a @click=\"viewTaskDetail(record)\" class=\"action-link detail\">详情</a>\r\n              </div>\r\n            </template>\r\n          </a-table>\r\n        </a-spin>\r\n      </div>\r\n\r\n      <!-- 月历视图 -->\r\n      <div class=\"calendar-container\" v-show=\"currentView === 'calendar'\">\r\n        <div class=\"calendar-header\">\r\n          <button class=\"calendar-nav-btn\" @click=\"previousMonth\">\r\n            <i class=\"icon-prev\">‹</i>\r\n          </button>\r\n          <h3 class=\"calendar-title\">{{ currentMonth.format('YYYY年 MM月') }}</h3>\r\n          <button class=\"calendar-nav-btn\" @click=\"nextMonth\">\r\n            <i class=\"icon-next\">›</i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"calendar-grid\">\r\n          <div class=\"calendar-weekdays\">\r\n            <div class=\"weekday\" v-for=\"day in weekdays\" :key=\"day\">{{ day }}</div>\r\n          </div>\r\n\r\n          <div class=\"calendar-days\">\r\n            <div\r\n              v-for=\"day in calendarDays\"\r\n              :key=\"day.date\"\r\n              :class=\"['calendar-day', {\r\n                'other-month': !day.isCurrentMonth,\r\n                'today': day.isToday,\r\n                'has-tasks': day.tasks.length > 0\r\n              }]\"\r\n            >\r\n              <div class=\"day-number\">{{ day.dayNumber }}</div>\r\n              <div class=\"day-tasks\">\r\n                <div\r\n                  v-for=\"task in day.tasks.slice(0, 3)\"\r\n                  :key=\"task.id\"\r\n                  :class=\"['task-item', 'status-' + task.workStatus]\"\r\n                  @click=\"viewTaskDetail(task)\"\r\n                  :title=\"task.ordername\"\r\n                >\r\n                  <span class=\"task-name\">{{ task.ordername }}</span>\r\n                  <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5) }}</span>\r\n                </div>\r\n                <div v-if=\"day.tasks.length > 3\" class=\"more-tasks\" @click=\"showDayTasks(day)\">\r\n                  +{{ day.tasks.length - 3 }} 更多\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 甘特图视图 -->\r\n      <div class=\"gantt-container\" v-show=\"currentView === 'gantt'\">\r\n        <div class=\"gantt-header\">\r\n          <div class=\"gantt-controls\">\r\n            <button class=\"gantt-nav-btn\" @click=\"previousGanttPeriod\">\r\n              <i class=\"icon-prev\">‹</i>\r\n            </button>\r\n            <span class=\"gantt-period\">{{ ganttPeriod.start.format('MM/DD') }} - {{ ganttPeriod.end.format('MM/DD') }}</span>\r\n            <button class=\"gantt-nav-btn\" @click=\"nextGanttPeriod\">\r\n              <i class=\"icon-next\">›</i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"gantt-content\">\r\n          <div class=\"gantt-timeline\">\r\n            <div class=\"timeline-header\">\r\n              <div class=\"task-header\">工单</div>\r\n              <div class=\"dates-header\">\r\n                <div\r\n                  v-for=\"date in ganttDates\"\r\n                  :key=\"date.format('YYYY-MM-DD')\"\r\n                  :class=\"['date-cell', { 'today': date.isSame(moment(), 'day') }]\"\r\n                >\r\n                  <div class=\"date-day\">{{ date.format('DD') }}</div>\r\n                  <div class=\"date-weekday\">{{ date.format('ddd') }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"timeline-body\">\r\n              <div\r\n                v-for=\"task in ganttTasks\"\r\n                :key=\"task.id\"\r\n                class=\"gantt-row\"\r\n              >\r\n                <div class=\"task-info\">\r\n                  <div class=\"task-name\" :title=\"task.ordername\">{{ task.ordername }}</div>\r\n                  <div class=\"task-meta\">\r\n                    <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5)}}</span>\r\n                    <span :class=\"['task-status', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"gantt-bars\">\r\n                  <div\r\n                    :class=\"['gantt-bar', 'status-' + task.workStatus]\"\r\n                    :style=\"getGanttBarStyle(task)\"\r\n                    @click=\"viewTaskDetail(task)\"\r\n                    :title=\"`${task.ordername} (${task.startTime} - ${task.endTime})`\"\r\n                  >\r\n                    <div class=\"bar-content\">\r\n                      <span class=\"bar-text\">{{ task.ordername }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 按天视图 -->\r\n      <div class=\"daily-container\" v-show=\"currentView === 'daily'\">\r\n        <div class=\"daily-header\">\r\n          <div class=\"daily-controls\">\r\n            <button class=\"daily-nav-btn\" @click=\"previousDay\">\r\n              <i class=\"icon-prev\">‹</i>\r\n            </button>\r\n            <div class=\"date-picker-wrapper\">\r\n              <a-date-picker\r\n                v-model=\"currentDay\"\r\n                :format=\"'YYYY年MM月DD日'\"\r\n                :allowClear=\"false\"\r\n                @change=\"onDayChange\"\r\n                class=\"daily-date-picker\"\r\n              />\r\n            </div>\r\n            <button class=\"daily-nav-btn\" @click=\"nextDay\">\r\n              <i class=\"icon-next\">›</i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"daily-content\">\r\n          <!-- 跨日期工单展示区域 -->\r\n          <div class=\"cross-date-section\" v-if=\"getCrossDayTasksForCurrentDay().length > 0\">\r\n            <div class=\"cross-date-header\">\r\n              <h3 class=\"cross-date-title\">\r\n                <span class=\"title-icon\">📅</span>\r\n                跨日期工单\r\n                <span class=\"task-count\">{{ getCrossDayTasksForCurrentDay().length }}</span>\r\n              </h3>\r\n            </div>\r\n            <div class=\"cross-date-timeline\">\r\n              <div\r\n                v-for=\"(task, index) in getCrossDayTasksForCurrentDay()\"\r\n                :key=\"task.id\"\r\n                :class=\"['cross-date-task', getTaskColorClass(task, index)]\"\r\n                :style=\"getCrossDayTaskStyle(task, index)\"\r\n                @click=\"viewTaskDetail(task)\"\r\n              >\r\n                <div class=\"cross-task-content\">\r\n                  <div class=\"cross-task-header\">\r\n                    <div class=\"cross-task-title\">{{ task.ordername }}</div>\r\n                    <div :class=\"['cross-task-status', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"cross-task-details\">\r\n                    <div class=\"cross-task-handler\">\r\n                      <i class=\"icon-user\">👤</i>\r\n                      {{ task.handling }}\r\n                    </div>\r\n                    <div class=\"cross-task-time\">\r\n                      <i class=\"icon-time\">⏰</i>\r\n                      {{ formatCrossDayTaskTime(task) }}\r\n                    </div>\r\n                    <div class=\"cross-task-duration\">\r\n                      <i class=\"icon-duration\">⏱️</i>\r\n                      {{ getCrossDayTaskDuration(task) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 时间轴容器 -->\r\n          <div class=\"timeline-container\">\r\n            <div class=\"timeline-hours\">\r\n              <div\r\n                v-for=\"hour in timelineHours\"\r\n                :key=\"hour\"\r\n                class=\"hour-slot\"\r\n              >\r\n                <!-- 美观的小时标签 -->\r\n                <div class=\"hour-label\">\r\n                  <div class=\"hour-text\">{{ hour }}:00</div>\r\n                </div>\r\n\r\n                <!-- 时间线区域 -->\r\n                <div class=\"hour-line\">\r\n                  <!-- 半小时刻度线 -->\r\n                  <div class=\"half-hour-line\"></div>\r\n\r\n                  <!-- 15分钟刻度线 -->\r\n                  <div class=\"quarter-hour-line\" style=\"top: 25%\"></div>\r\n                  <div class=\"quarter-hour-line\" style=\"top: 75%\"></div>\r\n                </div>\r\n\r\n                <!-- 工单展示区域 -->\r\n                <div class=\"task-slots\">\r\n                  <div\r\n                    v-for=\"task in getTasksForHour(hour)\"\r\n                    :key=\"task.id\"\r\n                    :class=\"['task-card', getTaskColorClass(task)]\"\r\n                    :style=\"getTaskCardStyle(task)\"\r\n                    @click=\"viewTaskDetail(task)\"\r\n                  >\r\n                    <!-- 状态标签移到右上角 -->\r\n                    <div :class=\"['task-status-corner', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </div>\r\n\r\n                    <div class=\"task-card-content\">\r\n                      <div class=\"task-title\">{{ task.ordername }}</div>\r\n                      <div class=\"task-meta-row\">\r\n                        <div class=\"task-handler\">\r\n                          <i class=\"icon-user\">👤</i>\r\n                          {{ task.handling }}\r\n                        </div>\r\n                        <div class=\"task-time\">\r\n                          <i class=\"icon-time\">⏰</i>\r\n                          {{ formatTaskTime(task) }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 日期详情弹窗 -->\r\n    <a-modal\r\n      v-model=\"dayTasksVisible\"\r\n      :title=\"`${selectedDay ? selectedDay.date : ''} 的工单`\"\r\n      :footer=\"null\"\r\n      width=\"800px\"\r\n      class=\"day-tasks-modal\"\r\n    >\r\n      <div class=\"day-tasks-list\">\r\n        <div\r\n          v-for=\"task in selectedDayTasks\"\r\n          :key=\"task.id\"\r\n          class=\"day-task-item\"\r\n          @click=\"viewTaskDetail(task)\"\r\n        >\r\n          <div class=\"task-main\">\r\n            <h4 class=\"task-title\">{{ task.ordername }}</h4>\r\n            <div class=\"task-info-row\">\r\n              <span class=\"task-handler\">处理人: {{ task.handling }}</span>\r\n              <span :class=\"['task-status', 'status-' + task.workStatus]\">\r\n                {{ getWorkStatusText(task.workStatus) }}\r\n              </span>\r\n            </div>\r\n            <div class=\"task-time\">\r\n              {{ task.startTime }} - {{ task.endTime }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n\r\n    <!-- 其他弹窗 -->\r\n    <div>\r\n      <a-modal\r\n        title=\"工单审核\"\r\n        :visible=\"isShowAudit\"\r\n        @ok=\"audit(null,'3')\"\r\n        @cancel=\"isShowAudit = false\"\r\n        class=\"audit-modal\"\r\n      >\r\n        <a-form-model>\r\n          <a-form-model-item label=\"审核意见\" :labelCol=\"{ span: 5 }\" :wrapperCol=\"{ span: 19 }\">\r\n            <a-textarea :rows=\"3\" v-model=\"auditOpinion\" placeholder=\"请输入审核意见\"></a-textarea>\r\n          </a-form-model-item>\r\n        </a-form-model>\r\n      </a-modal>\r\n    </div>\r\n\r\n    <!-- 工单弹窗 -->\r\n    <work-order-task-modal\r\n      ref=\"taskModal\"\r\n      :project-info=\"projectInfo\"\r\n      @ok=\"taskModalOk\"\r\n    ></work-order-task-modal>\r\n\r\n          <!-- 工单详情弹窗 -->\r\n    <task-detail-modal ref=\"taskDetailModal\"></task-detail-modal>\r\n\r\n    <!-- 工时统计弹窗 -->\r\n    <project-stats-modal ref=\"projectStatsModal\"></project-stats-modal>\r\n    \r\n    <!-- 工单完成弹窗 -->\r\n    <a-modal\r\n      title=\"完成工单\"\r\n      :visible=\"finishModalVisible\"\r\n      :confirmLoading=\"finishConfirmLoading\"\r\n      @cancel=\"finishModalVisible = false\"\r\n      :footer=\"null\"\r\n      width=\"30%\"\r\n      class=\"finish-modal\"\r\n    >\r\n      <a-spin :spinning=\"finishConfirmLoading\">\r\n        <a-form-model :model=\"finishForm\" :label-col=\"{ span: 4 }\" :wrapper-col=\"{ span: 20 }\">\r\n          <a-form-model-item label=\"上传附件\">\r\n            <div class=\"upload-container\" @dragover.prevent @drop.prevent=\"handleDrop\">\r\n              <a-upload\r\n                name=\"file\"\r\n                :action=\"uploadAction\"\r\n                :headers=\"uploadHeaders\"\r\n                :file-list=\"finishForm.fileList\"\r\n                @change=\"handleFileChange\"\r\n                :multiple=\"true\"\r\n                :show-upload-list=\"true\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <a-button type=\"dashed\">\r\n                  <a-icon type=\"upload\" /> 点击上传\r\n                </a-button>\r\n                <div class=\"upload-tips\">\r\n                  <span>支持：点击上传、拖拽上传（附件非必选）</span>\r\n                </div>\r\n              </a-upload>\r\n            </div>\r\n          </a-form-model-item>\r\n          \r\n          <a-form-model-item label=\"完成说明\">\r\n            <a-textarea \r\n              :rows=\"4\" \r\n              v-model=\"finishForm.finishDescribe\" \r\n              placeholder=\"请输入完成说明（非必填）\"\r\n            />\r\n          </a-form-model-item>\r\n        </a-form-model>\r\n        \r\n        <div class=\"modal-footer\">\r\n          <a-button @click=\"finishModalVisible = false\">取消</a-button>\r\n          <a-button type=\"primary\" @click=\"handleFinishOk\" :loading=\"finishConfirmLoading\">立即结束</a-button>\r\n        </div>\r\n      </a-spin>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mixinDevice } from '@/utils/mixin'\r\nimport WorkOrderTaskModal from './WorkOrderTaskModal'\r\nimport TaskDetailModal from './TaskDetailModal'\r\nimport ProjectStatsModal from './ProjectStatsModal'\r\nimport moment from 'moment'\r\nimport { getAction, putAction, httpAction } from '@api/manage'\r\nimport { Modal } from 'ant-design-vue';\r\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\r\nimport Vue from 'vue';\r\n\r\nexport default {\r\n  name: 'ProjectDetail',\r\n  mixins: [mixinDevice],\r\n  components: {\r\n    WorkOrderTaskModal,\r\n    TaskDetailModal,\r\n    ProjectStatsModal\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'pool',\r\n      currentView: 'table', // 新增：当前视图类型\r\n      currentUserId: '',\r\n      projectInfo: {},\r\n      projectMemberList: [],\r\n      queryParam: {\r\n        workStatus: []\r\n      },\r\n      uploadAction: `${window._CONFIG['domianURL']}/sys/common/upload`,\r\n      uploadHeaders: {},\r\n      finishModalVisible: false,\r\n      finishConfirmLoading: false,\r\n      currentTask: null,\r\n      finishForm: {\r\n        fileList: [],\r\n        finishDescribe: '',\r\n        finishAnnex: ''\r\n      },\r\n      poolTasks: [],\r\n      myTasks: [],\r\n      workingTimers: new Map(),\r\n\r\n      // 月历相关\r\n      currentMonth: moment(),\r\n      weekdays: ['一', '二', '三', '四', '五', '六','日'],\r\n      dayTasksVisible: false,\r\n      selectedDay: null,\r\n      selectedDayTasks: [],\r\n\r\n      // 甘特图相关\r\n      ganttPeriod: {\r\n        start: moment().startOf('week'),\r\n        end: moment().endOf('week').add(6, 'days')\r\n      },\r\n\r\n      // 按天视图相关\r\n      currentDay: moment(),\r\n      dailyColumnAssignment: null, // 当天工单列分配缓存\r\n      timelineHours: ['00', ...Array.from({ length: 23 }, (_, i) => (i + 1).toString().padStart(2, '0'))], // 0-23小时，完整24小时\r\n\r\n      tabs: {\r\n        'admin' : [\r\n          { value: 'pool', label: '公海池' },\r\n          { value: 'my', label: '我的工单' },\r\n          { value: 'all', label: '全部工单' },\r\n          { value: 'audit', label: '待审核' }\r\n        ],\r\n        'member': [\r\n          { value: 'pool', label: '公海池' },\r\n          { value: 'my', label: '我的工单' }\r\n        ]\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 3,\r\n      total: 0,\r\n      poolTotal: 0,\r\n      myTotal: 0,\r\n      auditTotal: 0,\r\n      allTotal: 0,\r\n      userRole: 'member',\r\n      dataSource: [],\r\n      dataColumns: {\r\n        'hours': [\r\n          {\r\n            title: '工单名称',\r\n            align: 'center',\r\n            dataIndex: 'ordername',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '优先级',\r\n            align: 'center',\r\n            dataIndex: 'priority',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'prioritySlot' }\r\n          },\r\n          {\r\n            title: '处理人',\r\n            align: 'center',\r\n            dataIndex: 'handling',\r\n            width: 120\r\n          },\r\n          {\r\n            title: '工时',\r\n            align: 'center',\r\n            dataIndex: 'estimatedHours',\r\n            width: 120,\r\n            scopedSlots: { customRender: 'workHourSlot' }\r\n          },\r\n          {\r\n            title: '状态',\r\n            align: 'center',\r\n            dataIndex: 'workStatus',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'workStatus' }\r\n          },\r\n          {\r\n            title: '创建时间',\r\n            align: 'center',\r\n            dataIndex: 'createTime',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'createTime' }\r\n          },\r\n          {\r\n            title: '审核意见',\r\n            align: 'center',\r\n            dataIndex: 'reviewComment',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewComment' }\r\n          },\r\n          {\r\n            title: '完成时间',\r\n            align: 'center',\r\n            dataIndex: 'reviewTime',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewTime' }\r\n          },\r\n          {\r\n            title: '操作',\r\n            dataIndex: 'action',\r\n            align: 'center',\r\n            width: 180,\r\n            scopedSlots: { customRender: 'action' }\r\n          }\r\n        ],\r\n        'timespan': [\r\n          {\r\n            title: '工单名称',\r\n            align: 'center',\r\n            dataIndex: 'ordername',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '优先级',\r\n            align: 'center',\r\n            dataIndex: 'priority',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'prioritySlot' }\r\n          },\r\n          {\r\n            title: '处理人',\r\n            align: 'center',\r\n            dataIndex: 'handling',\r\n            width: 120\r\n          },\r\n          {\r\n            title: '计划时间',\r\n            align: 'center',\r\n            width: 120,\r\n            scopedSlots: { customRender: 'plannedTimeSlot' }\r\n          },\r\n          {\r\n            title: '状态',\r\n            align: 'center',\r\n            dataIndex: 'workStatus',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'workStatus' }\r\n          },\r\n          {\r\n            title: '审核意见',\r\n            align: 'center',\r\n            dataIndex: 'reviewComment',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewComment' }\r\n          },\r\n          {\r\n            title: '操作',\r\n            dataIndex: 'action',\r\n            align: 'center',\r\n            width: 180,\r\n            scopedSlots: { customRender: 'action' }\r\n          }\r\n        ]\r\n      },\r\n      loading: false,\r\n      selectedRowKeys: [],\r\n      selectionRows: [],\r\n      ipagination: {\r\n        current: 1,\r\n        pageSize: 15,\r\n        total: 0,\r\n      },\r\n      statusMap: {\r\n        'pending': '待处理',\r\n        'working': '进行中',\r\n        'completed': '待审核',\r\n        'approved': '已审核',\r\n        'rejected': '已驳回'\r\n      },\r\n      progressStageMap: {\r\n        '0': '准备阶段',\r\n        '1': '实施阶段',\r\n        '2': '完成阶段'\r\n      },\r\n      isShowAudit: false,\r\n      auditOpinion: '',\r\n      currentId: '',\r\n      allTasksForView: [],\r\n    }\r\n  },\r\n  computed: {\r\n    projectId() {\r\n      return this.$route.query.id\r\n    },\r\n\r\n    moment() {\r\n      return moment\r\n    },\r\n\r\n    // 月历计算属性\r\n    calendarDays() {\r\n      const startOfMonth = this.currentMonth.clone().startOf('month')\r\n      const endOfMonth = this.currentMonth.clone().endOf('month')\r\n      const startDate = startOfMonth.clone().startOf('week')\r\n      const endDate = endOfMonth.clone().endOf('week')\r\n\r\n      const days = []\r\n      const current = startDate.clone()\r\n\r\n      while (current.isSameOrBefore(endDate)) {\r\n        const dayTasks = this.getTasksForDate(current)\r\n        days.push({\r\n          date: current.format('YYYY-MM-DD'),\r\n          dayNumber: current.date(),\r\n          isCurrentMonth: current.isSame(this.currentMonth, 'month'),\r\n          isToday: current.isSame(moment(), 'day'),\r\n          tasks: dayTasks\r\n        })\r\n        current.add(1, 'day')\r\n      }\r\n\r\n      return days\r\n    },\r\n\r\n    // 甘特图计算属性\r\n    ganttDates() {\r\n      const dates = []\r\n      const current = this.ganttPeriod.start.clone()\r\n\r\n      while (current.isSameOrBefore(this.ganttPeriod.end)) {\r\n        dates.push(current.clone())\r\n        current.add(1, 'day')\r\n      }\r\n\r\n      return dates\r\n    },\r\n\r\n    ganttTasks() {\r\n      // 优先使用当前视图的数据，如果没有则使用表格数据\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n\r\n        return taskStart.isSameOrBefore(this.ganttPeriod.end) &&\r\n          taskEnd.isSameOrAfter(this.ganttPeriod.start)\r\n      })\r\n    }\r\n  },\r\n  created(options) {\r\n    console.log(\"222333444\")\r\n    console.log(options)\r\n    console.log(\"进入人33333333\")\r\n  },\r\n\r\n  mounted() {\r\n    console.log(\"进入人222222222\")\r\n    this.currentUserId = this.$store.getters.userInfo.id;\r\n    this.loadProjectInfo()\r\n    this.loadProjectMember();\r\n    this.loadTasks()\r\n    this.startWorkingTimers()\r\n    \r\n    // 设置上传头部\r\n    const token = Vue.ls.get(ACCESS_TOKEN)\r\n    this.uploadHeaders = { 'X-Access-Token': token }\r\n  },\r\n\r\n  beforeDestroy() {\r\n    this.clearWorkingTimers()\r\n  },\r\n\r\n  methods: {\r\n    // 视图切换\r\n    switchView(view) {\r\n      this.currentView = view\r\n\r\n      // 如果切换到日历或甘特图视图，重新加载当前标签页的数据\r\n      if (view !== 'table') {\r\n        this.loadAllTasksForView()\r\n      }\r\n    },\r\n\r\n    loadAllTasksForView() {\r\n      this.loading = true;\r\n      // 先清空旧数据\r\n      this.allTasksForView = []\r\n\r\n      let params = {...this.queryParam};\r\n      \r\n      // 将状态数组转换为逗号分隔的字符串\r\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\r\n        params.workStatus = params.workStatus.join(',');\r\n      }\r\n      \r\n      params.projectId = this.projectId;\r\n      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));\r\n      params.pageNo = 1;\r\n      params.pageSize = 1000;\r\n\r\n      this.$http.get('/WorkOrderTask/list', { params }).then(res => {\r\n        if (res.success) {\r\n          this.allTasksForView = res.result.data\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    // 月历相关方法\r\n    previousMonth() {\r\n      this.currentMonth = this.currentMonth.clone().subtract(1, 'month')\r\n    },\r\n\r\n    nextMonth() {\r\n      this.currentMonth = this.currentMonth.clone().add(1, 'month')\r\n    },\r\n\r\n    getTasksForDate(date) {\r\n      const dateStr = date.format('YYYY-MM-DD')\r\n      // 优先使用当前视图的数据，如果没有则使用表格数据\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const startDate = moment(task.startTime).format('YYYY-MM-DD')\r\n        const endDate = moment(task.endTime).format('YYYY-MM-DD')\r\n\r\n        return dateStr >= startDate && dateStr <= endDate\r\n      })\r\n    },\r\n\r\n    showDayTasks(day) {\r\n      this.selectedDay = day\r\n      this.selectedDayTasks = day.tasks\r\n      this.dayTasksVisible = true\r\n    },\r\n\r\n    // 甘特图相关方法\r\n    previousGanttPeriod() {\r\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().subtract(7, 'days')\r\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().subtract(7, 'days')\r\n    },\r\n\r\n    nextGanttPeriod() {\r\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().add(7, 'days')\r\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().add(7, 'days')\r\n    },\r\n\r\n    getGanttBarStyle(task) {\r\n      if (!task.startTime || !task.endTime) return { display: 'none' }\r\n\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const periodStart = this.ganttPeriod.start\r\n      const periodEnd = this.ganttPeriod.end\r\n\r\n      // 计算任务在甘特图中的位置\r\n      const totalDays = periodEnd.diff(periodStart, 'days') + 1\r\n      const dayWidth = 100 / totalDays\r\n\r\n      // 计算开始位置\r\n      let startOffset = 0\r\n      if (taskStart.isSameOrAfter(periodStart)) {\r\n        startOffset = taskStart.diff(periodStart, 'days') * dayWidth\r\n      }\r\n\r\n      // 计算宽度\r\n      let width = dayWidth\r\n      if (taskEnd.isAfter(taskStart)) {\r\n        const visibleStart = moment.max(taskStart, periodStart)\r\n        const visibleEnd = moment.min(taskEnd, periodEnd)\r\n        const visibleDays = visibleEnd.diff(visibleStart, 'days') + 1\r\n        width = visibleDays * dayWidth\r\n      }\r\n\r\n      return {\r\n        left: `${startOffset}%`,\r\n        width: `${width}%`\r\n      }\r\n    },\r\n\r\n    // 按天视图相关方法\r\n    previousDay() {\r\n      this.currentDay = this.currentDay.clone().subtract(1, 'day')\r\n      this.clearDailyColumnAssignment()\r\n    },\r\n\r\n    nextDay() {\r\n      this.currentDay = this.currentDay.clone().add(1, 'day')\r\n      this.clearDailyColumnAssignment()\r\n    },\r\n\r\n    onDayChange(date) {\r\n      if (date) {\r\n        this.currentDay = moment(date)\r\n        this.clearDailyColumnAssignment()\r\n      }\r\n    },\r\n\r\n    clearDailyColumnAssignment() {\r\n      // 清除当天的列分配缓存，强制重新计算\r\n      this.dailyColumnAssignment = null\r\n    },\r\n\r\n    getTasksForHour(hour) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      const tasks = tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 关键修改：排除跨日期工单，只显示当天的工单\r\n        if (taskStartDate !== taskEndDate) {\r\n          return false // 跨日期工单不在时间轴内显示\r\n        }\r\n\r\n        // 只处理当天的工单\r\n        if (currentDateStr !== taskStartDate) {\r\n          return false\r\n        }\r\n\r\n        const startHour = taskStart.hour()\r\n        const hourNum = parseInt(hour)\r\n\r\n        // 只在开始小时显示工单\r\n        return hourNum === startHour\r\n      })\r\n\r\n      // 为重叠的工单分配列位置，从最左侧开始\r\n      return this.assignTaskColumnsFromLeft(tasks, hour)\r\n    },\r\n\r\n    // 新的列分配方法：从最左侧开始排列\r\n    assignTaskColumnsFromLeft(tasks, hour) {\r\n      if (tasks.length <= 1) {\r\n        return tasks.map(task => ({ ...task, columnIndex: 0, totalColumns: 1 }))\r\n      }\r\n\r\n      // 简化的列分配：按开始时间排序，从左到右分配\r\n      const sortedTasks = tasks.sort((a, b) => {\r\n        const timeA = moment(a.startTime)\r\n        const timeB = moment(b.startTime)\r\n        return timeA.valueOf() - timeB.valueOf()\r\n      })\r\n\r\n      // 检查时间重叠，分配到不同列\r\n      const columns = []\r\n\r\n      sortedTasks.forEach(task => {\r\n        let assignedColumn = -1\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n\r\n        // 寻找不重叠的列\r\n        for (let colIndex = 0; colIndex < columns.length; colIndex++) {\r\n          let canUseColumn = true\r\n\r\n          for (let existingTask of columns[colIndex]) {\r\n            const existingStart = moment(existingTask.startTime)\r\n            const existingEnd = moment(existingTask.endTime)\r\n\r\n            // 检查时间重叠\r\n            if (taskStart.isBefore(existingEnd) && taskEnd.isAfter(existingStart)) {\r\n              canUseColumn = false\r\n              break\r\n            }\r\n          }\r\n\r\n          if (canUseColumn) {\r\n            assignedColumn = colIndex\r\n            break\r\n          }\r\n        }\r\n\r\n        // 如果没有找到合适的列，创建新列\r\n        if (assignedColumn === -1) {\r\n          assignedColumn = columns.length\r\n          columns.push([])\r\n        }\r\n\r\n        columns[assignedColumn].push(task)\r\n        task.columnIndex = assignedColumn\r\n        task.totalColumns = Math.min(4, columns.length) // 最多4列\r\n      })\r\n\r\n      // 确保所有工单都有相同的总列数\r\n      const totalColumns = Math.min(4, columns.length)\r\n      sortedTasks.forEach(task => {\r\n        task.totalColumns = totalColumns\r\n      })\r\n\r\n      return sortedTasks\r\n    },\r\n\r\n    assignTaskColumns(tasks, hour) {\r\n      // 保留原方法以兼容其他调用\r\n      return this.assignTaskColumnsFromLeft(tasks, hour)\r\n    },\r\n\r\n    performDailyColumnAssignment(allTasks) {\r\n      console.log('开始全局列分配，工单数量:', allTasks.length)\r\n\r\n      // 按开始时间排序所有工单\r\n      const sortedTasks = allTasks.sort((a, b) => {\r\n        const startA = moment(a.startTime)\r\n        const startB = moment(b.startTime)\r\n        return startA.valueOf() - startB.valueOf()\r\n      })\r\n\r\n      const columns = []\r\n\r\n      // 为每个工单分配列，使用更严格的重叠检测\r\n      sortedTasks.forEach((task, index) => {\r\n        let assignedColumn = -1\r\n\r\n        console.log(`处理工单 ${index + 1}: ${task.ordername}`)\r\n        console.log(`工单时间: ${task.startTime} - ${task.endTime}`)\r\n\r\n        // 尝试找到一个不重叠的列\r\n        for (let colIndex = 0; colIndex < columns.length; colIndex++) {\r\n          let canUseColumn = true\r\n\r\n          for (let existingTask of columns[colIndex]) {\r\n            const overlaps = this.tasksOverlapInTime(task, existingTask)\r\n            console.log(`检查与工单 ${existingTask.ordername} 的重叠: ${overlaps}`)\r\n\r\n            if (overlaps) {\r\n              canUseColumn = false\r\n              break\r\n            }\r\n          }\r\n\r\n          if (canUseColumn) {\r\n            assignedColumn = colIndex\r\n            console.log(`分配到列 ${colIndex}`)\r\n            break\r\n          }\r\n        }\r\n\r\n        // 如果没有找到合适的列，创建新列\r\n        if (assignedColumn === -1) {\r\n          assignedColumn = columns.length\r\n          columns.push([])\r\n          console.log(`创建新列 ${assignedColumn}`)\r\n        }\r\n\r\n        columns[assignedColumn].push(task)\r\n        task.columnIndex = assignedColumn\r\n        task.totalColumns = Math.min(4, columns.length) // 最多4列\r\n      })\r\n\r\n      // 确保所有工单都有相同的总列数\r\n      const totalColumns = Math.min(4, columns.length)\r\n      sortedTasks.forEach(task => {\r\n        task.totalColumns = totalColumns\r\n      })\r\n\r\n      console.log(`列分配完成，总列数: ${totalColumns}`)\r\n      console.log('各列工单分布:', columns.map((col, index) => ({\r\n        column: index,\r\n        tasks: col.map(t => t.ordername)\r\n      })))\r\n\r\n      // 缓存当天的列分配结果\r\n      this.dailyColumnAssignment = {\r\n        date: this.currentDay.format('YYYY-MM-DD'),\r\n        tasks: sortedTasks\r\n      }\r\n    },\r\n\r\n    tasksOverlapInTime(task1, task2) {\r\n      // 获取两个工单在当前日期的可见时间范围\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const range1 = this.getTaskVisibleRangeForDate(task1, currentDate)\r\n      const range2 = this.getTaskVisibleRangeForDate(task2, currentDate)\r\n\r\n      if (!range1 || !range2) {\r\n        console.log(`重叠检测失败: range1=${!!range1}, range2=${!!range2}`)\r\n        return false\r\n      }\r\n\r\n      // 改进的重叠检测：增加时间缓冲区，使相近的工单也被认为重叠\r\n      // 这样可以避免视觉上相近的工单在同一列显示\r\n      const bufferMinutes = 30 // 30分钟缓冲区\r\n      const range1StartWithBuffer = range1.start.clone().subtract(bufferMinutes, 'minutes')\r\n      const range1EndWithBuffer = range1.end.clone().add(bufferMinutes, 'minutes')\r\n      const range2StartWithBuffer = range2.start.clone().subtract(bufferMinutes, 'minutes')\r\n      const range2EndWithBuffer = range2.end.clone().add(bufferMinutes, 'minutes')\r\n\r\n      const overlaps = range1StartWithBuffer.isBefore(range2EndWithBuffer) && range2StartWithBuffer.isBefore(range1EndWithBuffer)\r\n\r\n      console.log(`重叠检测详情:`)\r\n      console.log(`  工单1 ${task1.ordername}: ${range1.start.format('HH:mm')} - ${range1.end.format('HH:mm')}`)\r\n      console.log(`  工单2 ${task2.ordername}: ${range2.start.format('HH:mm')} - ${range2.end.format('HH:mm')}`)\r\n      console.log(`  重叠结果: ${overlaps}`)\r\n\r\n      return overlaps\r\n    },\r\n\r\n    getTaskVisibleRangeForDate(task, date) {\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      // 如果工单不在当前日期范围内，返回null\r\n      if (date < taskStartDate || date > taskEndDate) {\r\n        return null\r\n      }\r\n\r\n      // 计算在当前日期的可见时间范围\r\n      const dayStart = moment(`${date} 00:00:00`)\r\n      const dayEnd = moment(`${date} 23:59:59`)\r\n\r\n      let visibleStart, visibleEnd\r\n\r\n      if (date === taskStartDate && date === taskEndDate) {\r\n        // 工单在同一天内\r\n        visibleStart = taskStart\r\n        visibleEnd = taskEnd\r\n      } else if (date === taskStartDate) {\r\n        // 当前日期是工单开始日期，延伸到当天结束\r\n        visibleStart = taskStart\r\n        visibleEnd = dayEnd\r\n      } else if (date === taskEndDate) {\r\n        // 修复：当前日期是工单结束日期，从当天开始到工单结束时间\r\n        // 但是对于按天视图，我们需要考虑工单在结束日期的实际显示需求\r\n        visibleStart = dayStart\r\n        visibleEnd = taskEnd\r\n      } else {\r\n        // 当前日期在工单开始和结束之间，显示全天\r\n        visibleStart = dayStart\r\n        visibleEnd = dayEnd\r\n      }\r\n\r\n      // 调试输出\r\n      console.log(`工单 ${task.ordername} 在 ${date} 的可见范围:`)\r\n      console.log(`  原始时间: ${taskStart.format('YYYY-MM-DD HH:mm')} - ${taskEnd.format('YYYY-MM-DD HH:mm')}`)\r\n      console.log(`  日期分类: 开始=${taskStartDate}, 当前=${date}, 结束=${taskEndDate}`)\r\n      console.log(`  可见范围: ${visibleStart.format('HH:mm')} - ${visibleEnd.format('HH:mm')}`)\r\n\r\n      return {\r\n        start: visibleStart,\r\n        end: visibleEnd\r\n      }\r\n    },\r\n\r\n    getAllTasksForCurrentDay() {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStartDate = moment(task.startTime).format('YYYY-MM-DD')\r\n        const taskEndDate = moment(task.endTime).format('YYYY-MM-DD')\r\n\r\n        // 检查任务是否在当前日期\r\n        return currentDateStr >= taskStartDate && currentDateStr <= taskEndDate\r\n      })\r\n    },\r\n\r\n    findOverlappingTasks(currentTask, allTasks) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const overlapping = []\r\n\r\n      allTasks.forEach(task => {\r\n        if (this.tasksOverlapOnDate(currentTask, task, currentDateStr)) {\r\n          overlapping.push(task.id)\r\n        }\r\n      })\r\n\r\n      return overlapping.sort() // 排序确保一致的列分配\r\n    },\r\n\r\n    getTaskVisibleRange(task) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n      const dayStart = moment(`${currentDateStr} 01:00:00`) // 从1:00开始\r\n      const dayEnd = moment(`${currentDateStr} 23:59:59`)   // 到23:59结束\r\n\r\n      let visibleStart, visibleEnd\r\n\r\n      if (currentDateStr === taskStartDate && currentDateStr === taskEndDate) {\r\n        visibleStart = moment.max(taskStart, dayStart)\r\n        visibleEnd = moment.min(taskEnd, dayEnd)\r\n      } else if (currentDateStr === taskStartDate) {\r\n        visibleStart = moment.max(taskStart, dayStart)\r\n        visibleEnd = dayEnd\r\n      } else if (currentDateStr === taskEndDate) {\r\n        visibleStart = dayStart\r\n        visibleEnd = moment.min(taskEnd, dayEnd)\r\n      } else if (currentDateStr > taskStartDate && currentDateStr < taskEndDate) {\r\n        visibleStart = dayStart\r\n        visibleEnd = dayEnd\r\n      } else {\r\n        return null\r\n      }\r\n\r\n      // 如果可见时间范围无效，返回null\r\n      if (visibleStart.isAfter(visibleEnd)) {\r\n        return null\r\n      }\r\n\r\n      return { start: visibleStart, end: visibleEnd }\r\n    },\r\n\r\n    timeRangesOverlap(range1, range2) {\r\n      // 检查两个时间范围是否重叠\r\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)\r\n    },\r\n\r\n    tasksOverlapOnDate(task1, task2, dateStr) {\r\n      // 计算两个工单在指定日期的时间范围\r\n      const getVisibleRange = (task) => {\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        if (dateStr === taskStartDate && dateStr === taskEndDate) {\r\n          return { start: taskStart, end: taskEnd }\r\n        } else if (dateStr === taskStartDate) {\r\n          return { start: taskStart, end: moment(`${dateStr} 23:59:59`) }\r\n        } else if (dateStr === taskEndDate) {\r\n          return { start: moment(`${dateStr} 00:00:00`), end: taskEnd }\r\n        } else if (dateStr > taskStartDate && dateStr < taskEndDate) {\r\n          return { start: moment(`${dateStr} 00:00:00`), end: moment(`${dateStr} 23:59:59`) }\r\n        }\r\n        return null\r\n      }\r\n\r\n      const range1 = getVisibleRange(task1)\r\n      const range2 = getVisibleRange(task2)\r\n\r\n      if (!range1 || !range2) return false\r\n\r\n      // 检查时间范围是否重叠\r\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)\r\n    },\r\n\r\n    getTaskCardStyle(task) {\r\n      if (!task.startTime || !task.endTime) return {}\r\n\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      // 只处理当天的工单（跨日期工单已在上层过滤）\r\n      if (taskStartDate !== taskEndDate || currentDate !== taskStartDate) {\r\n        return { display: 'none' }\r\n      }\r\n\r\n      const startHour = taskStart.hour()\r\n      const startMinute = taskStart.minute()\r\n      const endHour = taskEnd.hour()\r\n      const endMinute = taskEnd.minute()\r\n\r\n      // 计算在120px高度时间槽中的精确位置\r\n      const topOffsetPx = (startMinute / 60) * 120 // 使用120px高度\r\n\r\n      // 注释：不再需要durationMinutes变量，直接计算精确高度\r\n\r\n      // 精确计算高度，确保底部对齐结束时间刻度线\r\n      let heightPx\r\n\r\n      if (startHour === endHour) {\r\n        // 同一小时内的工单 - 精确计算到分钟\r\n        const endOffsetPx = (endMinute / 60) * 120\r\n        heightPx = endOffsetPx - topOffsetPx\r\n\r\n        // 确保最小高度能显示内容（一行布局后内容更紧凑）\r\n        const minContentHeight = this.calculateContentHeight()\r\n        if (heightPx < minContentHeight) {\r\n          // 如果时间范围太短，优先保证内容显示，但尽量接近结束时间\r\n          heightPx = minContentHeight\r\n        }\r\n      } else {\r\n        // 跨小时的工单 - 精确计算到结束时间位置\r\n        const totalHours = endHour - startHour\r\n        const endMinuteOffset = (endMinute / 60) * 120\r\n        heightPx = totalHours * 120 + endMinuteOffset - topOffsetPx\r\n\r\n        // 跨小时工单通常有足够空间，但仍需检查最小内容高度\r\n        const minContentHeight = this.calculateContentHeight()\r\n        heightPx = Math.max(heightPx, minContentHeight)\r\n      }\r\n\r\n      // 确保高度为正数且不小于最小值\r\n      heightPx = Math.max(heightPx, 60) // 绝对最小高度60px\r\n\r\n      // 计算多列布局，从最左侧开始\r\n      const totalColumns = task.totalColumns || 1\r\n      const columnIndex = task.columnIndex || 0\r\n\r\n      // 优化列宽计算，确保内容完整显示\r\n      const containerWidth = 100 // 容器总宽度百分比\r\n      const columnGap = 2 // 列间距百分比\r\n      const totalGapWidth = (totalColumns - 1) * columnGap\r\n      const availableWidth = containerWidth - totalGapWidth\r\n      const columnWidth = availableWidth / totalColumns\r\n\r\n      // 从最左侧开始排列（0%开始）\r\n      const leftOffset = columnIndex * (columnWidth + columnGap)\r\n\r\n      const finalStyle = {\r\n        top: `${topOffsetPx}px`,\r\n        height: `${heightPx}px`,\r\n        position: 'absolute',\r\n        width: `${Math.max(columnWidth, 20)}%`, // 确保最小宽度\r\n        left: `${leftOffset}%`, // 从最左侧开始\r\n        zIndex: 10001 + columnIndex,\r\n        overflow: 'visible', // 改为visible，允许内容自适应\r\n        minHeight: `${heightPx}px` // 使用计算出的高度作为最小高度\r\n      }\r\n\r\n      return finalStyle\r\n    },\r\n\r\n    // 新增：计算内容所需高度（一行布局，状态在右上角）\r\n    calculateContentHeight() {\r\n      // 基础内容高度计算（根据实际CSS样式）\r\n      const titleHeight = 22 // 标题行高度（14px字体 * 1.3行高）\r\n      const metaRowHeight = 28 // 处理人和时间在一行的高度\r\n      const cardPadding = 32 // 卡片上下内边距总和（16px * 2）\r\n      const gaps = 12 // 标题和meta行之间的间距\r\n\r\n      // 总内容高度（处理人和时间在一行，更紧凑）\r\n      const totalContentHeight = titleHeight + metaRowHeight + cardPadding + gaps\r\n\r\n      // 确保最小高度能显示完整内容，一行布局后高度更小\r\n      return Math.max(totalContentHeight, 80)\r\n    },\r\n\r\n    // 获取当前日期的跨日期工单\r\n    getCrossDayTasksForCurrentDay() {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 检查是否为跨日期工单且在当前日期范围内\r\n        const isCrossDay = taskStartDate !== taskEndDate\r\n        const isInCurrentDate = currentDateStr >= taskStartDate && currentDateStr <= taskEndDate\r\n\r\n        return isCrossDay && isInCurrentDate\r\n      })\r\n    },\r\n\r\n    // 获取当前日期的当天工单（非跨日期）\r\n    getCurrentDayTasksForHour(hour) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      const tasks = tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 只显示当天工单（非跨日期）\r\n        const isSameDay = taskStartDate === taskEndDate && taskStartDate === currentDateStr\r\n        if (!isSameDay) return false\r\n\r\n        // 修复：只在工单开始的小时槽显示，避免重复\r\n        const startHour = taskStart.hour()\r\n        const hourNum = parseInt(hour)\r\n\r\n        return hourNum === startHour\r\n      })\r\n\r\n      return this.assignTaskColumns(tasks, hour)\r\n    },\r\n\r\n    // 格式化跨日期工单的时间显示\r\n    formatCrossDayTaskTime(task) {\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      if (currentDate === taskStartDate && currentDate === taskEndDate) {\r\n        // 同一天（不应该出现在这里）\r\n        return `${taskStart.format('HH:mm')} - ${taskEnd.format('HH:mm')}`\r\n      } else if (currentDate === taskStartDate) {\r\n        // 开始日期\r\n        return `${taskStart.format('HH:mm')} - 次日`\r\n      } else if (currentDate === taskEndDate) {\r\n        // 结束日期\r\n        return `前日 - ${taskEnd.format('HH:mm')}`\r\n      } else {\r\n        // 中间日期\r\n        return `全天`\r\n      }\r\n    },\r\n\r\n    // 计算跨日期工单的持续时间\r\n    getCrossDayTaskDuration(task) {\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const duration = moment.duration(taskEnd.diff(taskStart))\r\n\r\n      const days = Math.floor(duration.asDays())\r\n      const hours = duration.hours()\r\n      const minutes = duration.minutes()\r\n\r\n      if (days > 0) {\r\n        return `${days}天${hours}小时`\r\n      } else if (hours > 0) {\r\n        return `${hours}小时${minutes}分钟`\r\n      } else {\r\n        return `${minutes}分钟`\r\n      }\r\n    },\r\n\r\n    // 计算跨日期工单在时间轴上的样式\r\n    getCrossDayTaskStyle(task, index) {\r\n      const totalTasks = this.getCrossDayTasksForCurrentDay().length\r\n      const taskWidth = Math.min(95 / totalTasks, 25) // 最大宽度25%，根据工单数量调整\r\n      const leftOffset = index * (taskWidth + 1.5) // 1.5%间距，增加间距\r\n\r\n      return {\r\n        position: 'relative',\r\n        width: `${taskWidth}%`,\r\n        minWidth: '200px',\r\n        marginRight: '12px',\r\n        marginBottom: '8px',\r\n        display: 'inline-block',\r\n        verticalAlign: 'top',\r\n        zIndex: 100 + index\r\n      }\r\n    },\r\n\r\n    // 获取工单颜色类名\r\n    getTaskColorClass(task, index = 0) {\r\n      // 基于工单状态和索引生成颜色类\r\n      const colorClasses = [\r\n        'task-color-blue',\r\n        'task-color-purple',\r\n        'task-color-green',\r\n        'task-color-orange',\r\n        'task-color-pink',\r\n        'task-color-cyan',\r\n        'task-color-red',\r\n        'task-color-indigo'\r\n      ]\r\n\r\n      // 优先根据状态分配颜色\r\n      const statusColors = {\r\n        'pending': 'task-color-orange',\r\n        'working': 'task-color-blue',\r\n        'completed': 'task-color-purple',\r\n        'approved': 'task-color-green',\r\n        'rejected': 'task-color-red'\r\n      }\r\n\r\n      // 如果有状态对应的颜色，使用状态颜色，否则使用循环颜色\r\n      return statusColors[task.workStatus] || colorClasses[index % colorClasses.length]\r\n    },\r\n\r\n\r\n\r\n    formatTaskTime(task) {\r\n      if (!task.startTime || !task.endTime) return ''\r\n      const start = moment(task.startTime)\r\n      const end = moment(task.endTime)\r\n      const startDate = start.format('YYYY-MM-DD')\r\n      const endDate = end.format('YYYY-MM-DD')\r\n\r\n      // 如果是同一天，只显示时间范围（更紧凑）\r\n      if (startDate === endDate) {\r\n        return `${start.format('HH:mm')}-${end.format('HH:mm')}`\r\n      }\r\n\r\n      // 跨天显示紧凑格式\r\n      return `${start.format('M/D HH:mm')}-${end.format('M/D HH:mm')}`\r\n    },\r\n\r\n\r\n\r\n    // 原有方法保持不变\r\n    searchQuery(){\r\n      this.loadTasks();\r\n    },\r\n\r\n    searchReset(){\r\n      this.queryParam = {\r\n        workStatus: []\r\n      }\r\n      this.ipagination.current = 1;\r\n    },\r\n\r\n    audit(id, type){\r\n      if(type === '1'){\r\n        Modal.confirm({\r\n          title: '审核确认',\r\n          icon: '',\r\n          content: '确定要通过此条工单吗？',\r\n          onOk: () => {\r\n            this.updateWorkStatus(id, 'approved');\r\n          }\r\n        })\r\n      }else if(type === '2'){\r\n        this.isShowAudit = true;\r\n        this.currentId = id;\r\n      }else if(type === '3'){\r\n        if(this.auditOpinion.length === 0){\r\n          this.$message.error(\"请输入审核意见！\");\r\n          return;\r\n        }\r\n        this.updateWorkStatus(this.currentId, 'rejected');\r\n      }\r\n    },\r\n\r\n    updateWorkStatus(id, status) {\r\n      let params = {}\r\n      params.id = id\r\n      params.workStatus = status\r\n      if(status === 'rejected'){\r\n        params.reviewComment = this.auditOpinion;\r\n      }\r\n      putAction('/WorkOrderTask/edit', params)\r\n        .then(res => {\r\n          if (res.success) {\r\n            this.$message.success('操作成功！')\r\n            this.loadTasks()\r\n            // 如果当前不是表格视图，也要重新加载数据\r\n            if (this.currentView !== 'table') {\r\n              this.loadAllTasksForView()\r\n            }\r\n            this.isShowAudit = false;\r\n          } else {\r\n            this.$message.error('操作失败！')\r\n          }\r\n        })\r\n    },\r\n\r\n    handleTableChange(current, pageSize) {\r\n      this.ipagination = current\r\n      this.loadTasks()\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys, selectionRows) {\r\n      this.selectedRowKeys = selectedRowKeys\r\n      this.selectionRows = selectionRows\r\n    },\r\n\r\n    getWorkStatusText(status) {\r\n      return this.statusMap[status] || '待处理'\r\n    },\r\n\r\n    getProgressStageText(stage) {\r\n      return this.progressStageMap[stage] || '未开始'\r\n    },\r\n\r\n    switchTab(e) {\r\n      this.dataSource = []\r\n      this.loading = true\r\n      this.activeTab = e\r\n\r\n      // 如果切换到不支持多视图的标签页，强制切换回表格视图\r\n      if (e === 'pool' || e === 'audit') {\r\n        this.currentView = 'table'\r\n      }\r\n\r\n      this.searchReset();\r\n      this.loadTasks()\r\n\r\n      // 如果当前不是表格视图，也要重新加载数据\r\n      if (this.currentView !== 'table') {\r\n        // 延迟加载，确保标签切换完成\r\n        this.$nextTick(() => {\r\n          this.loadAllTasksForView()\r\n        })\r\n      }\r\n    },\r\n    loadProjectInfo() {\r\n      getAction(`/project/queryById`, { id: this.projectId }).then(res => {\r\n        if (res.success) {\r\n          this.projectInfo = res.result\r\n        }\r\n      })\r\n    },\r\n\r\n    loadProjectMember() {\r\n      getAction(`/projectMember/list`, { projectId: this.projectId }).then(res => {\r\n        if (res.success) {\r\n          this.projectMemberList = res.result;\r\n          this.projectMemberList.forEach(item => {\r\n            if(item.user_id === this.currentUserId){\r\n              this.userRole = item.role;\r\n              return;\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    loadTasks() {\r\n      this.loading = true;\r\n      let params = {...this.queryParam};\r\n      \r\n      // 将状态数组转换为逗号分隔的字符串\r\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\r\n        params.workStatus = params.workStatus.join(',');\r\n      }\r\n      \r\n      params.projectId = this.projectId;\r\n      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));\r\n      params.pageNo = this.ipagination.current;\r\n      params.pageSize = this.ipagination.pageSize;\r\n      this.$http.get('/WorkOrderTask/list', {\r\n        params: params\r\n      }).then(res => {\r\n        if (res.success) {\r\n          this.dataSource = res.result.data\r\n          let numJSON = res.result.data2\r\n          this.poolTotal = numJSON.poolTotal\r\n          this.myTotal = numJSON.myTotal\r\n          this.allTotal = numJSON.allTotal\r\n          this.auditTotal = numJSON.auditTotal\r\n          this.ipagination.total = res.result.total\r\n        } else {\r\n          this.dataSource = []\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    handleAddTask() {\r\n      this.$refs.taskModal.add()\r\n    },\r\n\r\n    taskModalOk() {\r\n      this.loadTasks()\r\n      // 如果当前不是表格视图，也要重新加载数据\r\n      if (this.currentView !== 'table') {\r\n        this.loadAllTasksForView()\r\n      }\r\n    },\r\n\r\n    claimTask(task) {\r\n      this.$confirm({\r\n        title: '确认领取',\r\n        content: `确定要领取工单\"${task.ordername}\"吗？`,\r\n        onOk: () => {\r\n          this.$http.post('/WorkOrderTask/claim', {\r\n            id: task.id\r\n          }).then(res => {\r\n            if (res.success) {\r\n              this.$message.success('领取成功')\r\n              this.loadTasks()\r\n              if (this.currentView !== 'table') {\r\n                this.loadAllTasksForView()\r\n              }\r\n            } else {\r\n              this.$message.error('领取失败')\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    startWork(task) {\r\n      this.$http.post('/WorkOrderTask/startWork', {\r\n        id: task.id,\r\n        startTime: new Date()\r\n      }).then(res => {\r\n        if (res.success) {\r\n          this.$message.success('工单已开始')\r\n          task.workStatus = 'working'\r\n          task.actualStartTime = new Date()\r\n          this.startWorkingTimer(task)\r\n        } else {\r\n          this.$message.error('开始失败')\r\n        }\r\n      })\r\n    },\r\n\r\n    showFinishModal(task) {\r\n      this.currentTask = task;\r\n      this.finishForm = {\r\n        fileList: [],\r\n        finishDescribe: '',\r\n        finishAnnex: ''\r\n      };\r\n      this.finishModalVisible = true;\r\n    },\r\n    \r\n    // 处理拖拽上传\r\n    handleDrop(e) {\r\n      e.preventDefault();\r\n      const files = Array.from(e.dataTransfer.files);\r\n      files.forEach(file => {\r\n        this.uploadFile(file);\r\n      });\r\n    },\r\n    \r\n    // 上传前验证\r\n    beforeUpload(file) {\r\n      const isLt100M = file.size / 1024 / 1024 < 100;\r\n      if (!isLt100M) {\r\n        this.$message.error('文件必须小于 100MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 处理上传变更\r\n    handleFileChange(info) {\r\n      let fileList = [...info.fileList];\r\n      \r\n      // 限制最多5个文件\r\n      fileList = fileList.slice(-5);\r\n      \r\n      // 更新文件状态\r\n      fileList = fileList.map(file => {\r\n        if (file.response) {\r\n          file.url = file.response.message;\r\n        }\r\n        return file;\r\n      });\r\n      \r\n      this.finishForm.fileList = fileList;\r\n      \r\n      // 将文件URL拼接成字符串保存到finishAnnex\r\n      if (fileList.length > 0) {\r\n        this.finishForm.finishAnnex = fileList\r\n          .filter(file => file.status === 'done' && file.response)\r\n          .map(file => file.response.message)\r\n          .join(',');\r\n      } else {\r\n        this.finishForm.finishAnnex = '';\r\n      }\r\n    },\r\n    \r\n    // 完成工单提交\r\n    handleFinishOk() {\r\n      if (!this.currentTask) return;\r\n      \r\n      this.finishConfirmLoading = true;\r\n      const params = {\r\n        id: this.currentTask.id,\r\n        workStatus: 'completed'\r\n      };\r\n      \r\n      // 添加附件和说明字段\r\n      if (this.finishForm.finishAnnex) {\r\n        params.finishAnnex = this.finishForm.finishAnnex;\r\n      }\r\n      \r\n      if (this.finishForm.finishDescribe) {\r\n        params.finishDescribe = this.finishForm.finishDescribe;\r\n      }\r\n\r\n      putAction('/WorkOrderTask/edit', params)\r\n        .then(res => {\r\n          if (res.success) {\r\n            this.$message.success('工单已结束，等待审核！')\r\n            this.finishModalVisible = false;\r\n            this.loadTasks()\r\n            // 如果当前不是表格视图，也要重新加载数据\r\n            if (this.currentView !== 'table') {\r\n              this.loadAllTasksForView()\r\n            }\r\n            this.isShowAudit = false;\r\n          } else {\r\n            this.$message.error(res.message || '结束失败')\r\n          }\r\n        }).finally(() => {\r\n            this.finishConfirmLoading = false;\r\n      });\r\n      \r\n      // httpAction('/WorkOrderTask/endWork', params, 'post').then(res => {\r\n      //   if (res.success) {\r\n      //     this.$message.success('工单已结束，等待审核');\r\n      //     this.currentTask.workStatus = 'completed';\r\n      //     this.currentTask.actualEndTime = endTime;\r\n      //     this.currentTask.actualHours = workingHours;\r\n      //     this.clearWorkingTimer(this.currentTask.id);\r\n      //     this.finishModalVisible = false;\r\n      //     this.loadTasks();\r\n      //   } else {\r\n      //     this.$message.error(res.message || '结束失败');\r\n      //   }\r\n      // }).finally(() => {\r\n      //   this.finishConfirmLoading = false;\r\n      // });\r\n    },\r\n\r\n    viewTaskDetail(task) {\r\n      this.$refs.taskDetailModal.show(task)\r\n    },\r\n\r\n    taskEdit(task) {\r\n      let data = JSON.parse(JSON.stringify(task));\r\n      data.handling = data.handlingId;\r\n      this.$refs.taskModal.show(data);\r\n    },\r\n\r\n    taskEdit2(task) {\r\n      let data = JSON.parse(JSON.stringify(task));\r\n      data.handling = data.handlingId;\r\n      this.$refs.taskModal.show2(data);\r\n    },\r\n\r\n    viewProjectStats() {\r\n      this.$refs.projectStatsModal.show(this.projectInfo)\r\n    },\r\n\r\n    goBack() {\r\n      this.$router.go(-1)\r\n    },\r\n\r\n    startWorkingTimers() {\r\n      this.myTasks.forEach(task => {\r\n        if (task.workStatus === 'working' && task.actualStartTime) {\r\n          this.startWorkingTimer(task)\r\n        }\r\n      })\r\n    },\r\n\r\n    startWorkingTimer(task) {\r\n      const timer = setInterval(() => {\r\n        this.$forceUpdate()\r\n      }, 1000)\r\n      this.workingTimers.set(task.id, timer)\r\n    },\r\n\r\n    clearWorkingTimer(taskId) {\r\n      const timer = this.workingTimers.get(taskId)\r\n      if (timer) {\r\n        clearInterval(timer)\r\n        this.workingTimers.delete(taskId)\r\n      }\r\n    },\r\n\r\n    clearWorkingTimers() {\r\n      this.workingTimers.forEach(timer => clearInterval(timer))\r\n      this.workingTimers.clear()\r\n    },\r\n\r\n    calculateWorkingHours(startTime, endTime) {\r\n      const start = moment(startTime)\r\n      const end = moment(endTime)\r\n      return Math.round(end.diff(start, 'hours', true) * 100) / 100\r\n    },\r\n\r\n    getWorkingDuration(startTime) {\r\n      const start = moment(startTime)\r\n      const now = moment()\r\n      const duration = moment.duration(now.diff(start))\r\n      const hours = Math.floor(duration.asHours())\r\n      const minutes = duration.minutes()\r\n      return `${hours}h ${minutes}m`\r\n    },\r\n\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '未开始',\r\n        '1': '进行中',\r\n        '2': '已完成',\r\n        '3': '已暂停'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    getStatusClass(status) {\r\n      const classMap = {\r\n        '0': 'status-pending',\r\n        '1': 'status-active',\r\n        '2': 'status-completed',\r\n        '3': 'status-paused'\r\n      }\r\n      return classMap[status] || 'status-pending'\r\n    },\r\n\r\n    getModeText(mode) {\r\n      return mode === 'hours' ? '工时模式' : '排期模式'\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const priorityMap = {\r\n        '2': '高',\r\n        '1': '中',\r\n        '0': '低'\r\n      }\r\n      return priorityMap[priority] || '中'\r\n    },\r\n\r\n    getPriorityClass(priority) {\r\n      const classMap = {\r\n        '2': 'priority-high',\r\n        '1': 'priority-medium',\r\n        '0': 'priority-low'\r\n      }\r\n      return classMap[priority] || 'priority-medium'\r\n    },\r\n\r\n    getTaskStatusText(task) {\r\n      const statusMap = {\r\n        'pending': '待开始',\r\n        'working': '进行中',\r\n        'completed': '待审核',\r\n        'approved': '已完成',\r\n        'rejected': '已驳回'\r\n      }\r\n      return statusMap[task.workStatus] || '未知'\r\n    },\r\n\r\n    getTaskStatusClass(task) {\r\n      const classMap = {\r\n        'pending': 'task-pending',\r\n        'working': 'task-working',\r\n        'completed': 'task-completed',\r\n        'approved': 'task-approved',\r\n        'rejected': 'task-rejected'\r\n      }\r\n      return classMap[task.workStatus] || 'task-pending'\r\n    },\r\n\r\n    formatDate(date) {\r\n      if (!date) return '未设置'\r\n      return moment(date).format('MM-DD HH:mm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n// 基础样式保持不变\r\n.project-detail-app {\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: 100vh;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',\r\n  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n}\r\n\r\n// 多选框换行显示样式\r\n:deep(.multi-select-wrap) {\r\n  .ant-select-selection--multiple {\r\n    .ant-select-selection__rendered {\r\n      height: auto;\r\n      max-height: none;\r\n      overflow-y: auto;\r\n\r\n      // 使标签能够换行显示\r\n      ul {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n      }\r\n\r\n      .ant-select-selection__choice {\r\n        margin-top: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 项目头部样式保持不变...\r\n.project-header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  padding: 32px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    gap: 24px;\r\n  }\r\n\r\n  .project-info {\r\n    flex: 1;\r\n\r\n    .back-btn {\r\n      background: none;\r\n      border: none;\r\n      color: #5a67d8;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      cursor: pointer;\r\n      margin-bottom: 16px;\r\n      padding: 8px 16px;\r\n      border-radius: 10px;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, rgba(90, 103, 216, 0.1), rgba(79, 172, 254, 0.1));\r\n        opacity: 0;\r\n        transition: opacity 0.3s ease;\r\n      }\r\n\r\n      &:hover {\r\n        color: #4c51bf;\r\n        transform: translateX(-3px);\r\n\r\n        &::before {\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .project-title {\r\n      margin: 0 0 16px 0;\r\n      font-size: 32px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      line-height: 1.2;\r\n      letter-spacing: -0.025em;\r\n    }\r\n\r\n    .project-meta {\r\n      display: flex;\r\n      gap: 12px;\r\n      align-items: center;\r\n\r\n      .project-mode {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        color: white;\r\n        padding: 8px 16px;\r\n        border-radius: 20px;\r\n        font-size: 13px;\r\n        font-weight: 600;\r\n        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n  }\r\n\r\n  .header-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n\r\n    .btn-primary, .btn-secondary {\r\n      padding: 12px 24px;\r\n      border: none;\r\n      border-radius: 14px;\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n      overflow: hidden;\r\n      letter-spacing: 0.025em;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        width: 0;\r\n        height: 0;\r\n        background: rgba(255, 255, 255, 0.25);\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        transition: width 0.6s, height 0.6s;\r\n      }\r\n\r\n      &:active::before {\r\n        width: 300px;\r\n        height: 300px;\r\n      }\r\n    }\r\n\r\n    .btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 15px 35px rgba(76, 81, 191, 0.5);\r\n      }\r\n    }\r\n\r\n    .btn-secondary {\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #4a5568;\r\n      border: 1px solid rgba(74, 85, 104, 0.15);\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        background: white;\r\n        border-color: rgba(74, 85, 104, 0.25);\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 筛选区域样式保持不变...\r\n.filter-section {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  overflow: hidden;\r\n\r\n  .filter-content {\r\n    padding: 28px 32px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: flex-end;\r\n      gap: 24px;\r\n    }\r\n\r\n    .filter-group {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 10px;\r\n      min-width: 200px;\r\n\r\n      label {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #4a5568;\r\n        margin-bottom: 4px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .filter-actions {\r\n      display: flex;\r\n      gap: 12px;\r\n      margin-top: 32px;\r\n\r\n      .btn-primary, .btn-ghost {\r\n        padding: 12px 24px;\r\n        border-radius: 12px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        letter-spacing: 0.025em;\r\n      }\r\n\r\n      .btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .btn-ghost {\r\n        background: transparent;\r\n        color: #718096;\r\n        border: 1.5px solid #e2e8f0;\r\n\r\n        &:hover {\r\n          background: #f7fafc;\r\n          border-color: #cbd5e0;\r\n          color: #4a5568;\r\n          transform: translateY(-1px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优化标签页容器\r\n.tabs-container {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  overflow: hidden;\r\n\r\n  .tabs-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n    border-bottom: 1px solid rgba(226, 232, 240, 0.6);\r\n    padding: 0 20px 0 0;\r\n    position: relative;\r\n\r\n    .tabs-left {\r\n      display: flex;\r\n      flex: 1;\r\n\r\n      .tab-btn {\r\n        flex: 1;\r\n        padding: 20px 24px;\r\n        border: none;\r\n        background: none;\r\n        cursor: pointer;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n        color: #64748b;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 10px;\r\n        position: relative;\r\n        letter-spacing: 0.025em;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          inset: 0;\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n          opacity: 0;\r\n          transition: opacity 0.3s ease;\r\n        }\r\n\r\n        &:hover::before {\r\n          opacity: 1;\r\n        }\r\n\r\n        &.active {\r\n          color: #4c51bf;\r\n          background: rgba(255, 255, 255, 0.95);\r\n          font-weight: 700;\r\n\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            width: 50px;\r\n            height: 3px;\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            border-radius: 2px;\r\n            box-shadow: 0 2px 8px rgba(76, 81, 191, 0.3);\r\n          }\r\n        }\r\n\r\n        .tab-count {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          padding: 4px 10px;\r\n          border-radius: 12px;\r\n          font-size: 12px;\r\n          font-weight: 700;\r\n          min-width: 22px;\r\n          text-align: center;\r\n          box-shadow: 0 3px 10px rgba(76, 81, 191, 0.3);\r\n          letter-spacing: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 新增视图切换器\r\n    .view-switcher {\r\n      display: flex;\r\n      gap: 4px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      padding: 6px;\r\n      border-radius: 12px;\r\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n\r\n      .view-btn {\r\n        padding: 10px 12px;\r\n        border: none;\r\n        background: transparent;\r\n        border-radius: 8px;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #64748b;\r\n        font-size: 16px;\r\n\r\n        &:hover {\r\n          background: rgba(76, 81, 191, 0.1);\r\n          color: #4c51bf;\r\n          transform: scale(1.05);\r\n        }\r\n\r\n        &.active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n          transform: scale(1.05);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 表格容器保持原样...\r\n.table-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .table-loading {\r\n    .ant-spin-container {\r\n      transition: all 0.4s ease;\r\n    }\r\n\r\n    &.ant-spin-spinning .ant-spin-container {\r\n      opacity: 0.5;\r\n      filter: blur(2px);\r\n    }\r\n  }\r\n}\r\n\r\n// 月历视图样式\r\n.calendar-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .calendar-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 24px;\r\n    margin-bottom: 28px;\r\n\r\n    .calendar-nav-btn {\r\n      width: 44px;\r\n      height: 44px;\r\n      border: none;\r\n      border-radius: 12px;\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n\r\n    .calendar-title {\r\n      font-size: 24px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      margin: 0;\r\n      min-width: 180px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .calendar-grid {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .calendar-weekdays {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n\r\n      .weekday {\r\n        padding: 16px;\r\n        text-align: center;\r\n        font-weight: 700;\r\n        color: #374151;\r\n        font-size: 14px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .calendar-days {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n\r\n      .calendar-day {\r\n        min-height: 120px;\r\n        border-right: 1px solid #f1f5f9;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        padding: 12px;\r\n        transition: all 0.3s ease;\r\n        position: relative;\r\n\r\n        &:nth-child(7n) {\r\n          border-right: none;\r\n        }\r\n\r\n        &:hover {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        }\r\n\r\n        &.other-month {\r\n          background: #fafafa;\r\n          color: #cbd5e0;\r\n        }\r\n\r\n        &.today {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n\r\n          .day-number {\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            color: white;\r\n            border-radius: 50%;\r\n            width: 28px;\r\n            height: 28px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            font-weight: 700;\r\n          }\r\n        }\r\n\r\n        &.has-tasks {\r\n          .day-number {\r\n            font-weight: 700;\r\n            color: #4c51bf;\r\n          }\r\n        }\r\n\r\n        .day-number {\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .day-tasks {\r\n          .task-item {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);\r\n            border-left: 3px solid #4c51bf;\r\n            padding: 4px 8px;\r\n            margin-bottom: 4px;\r\n            border-radius: 4px;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            font-size: 11px;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(76, 81, 191, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);\r\n              transform: translateX(2px);\r\n            }\r\n\r\n            &.status-pending {\r\n              border-left-color: #ed8936;\r\n              background: rgba(237, 137, 54, 0.1);\r\n            }\r\n\r\n            &.status-working {\r\n              border-left-color: #4299e1;\r\n              background: rgba(66, 153, 225, 0.1);\r\n            }\r\n\r\n            &.status-completed {\r\n              border-left-color: #9f7aea;\r\n              background: rgba(159, 122, 234, 0.1);\r\n            }\r\n\r\n            &.status-approved {\r\n              border-left-color: #48bb78;\r\n              background: rgba(72, 187, 120, 0.1);\r\n            }\r\n\r\n            &.status-rejected {\r\n              border-left-color: #f56565;\r\n              background: rgba(245, 101, 101, 0.1);\r\n            }\r\n\r\n            .task-name {\r\n              display: block;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-handler {\r\n              display: block;\r\n              color: #718096;\r\n              font-size: 10px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n\r\n          .more-tasks {\r\n            color: #4c51bf;\r\n            font-size: 11px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            padding: 2px 4px;\r\n            border-radius: 4px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              background: rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 甘特图视图样式\r\n.gantt-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .gantt-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .gantt-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n\r\n      .gantt-nav-btn {\r\n        width: 40px;\r\n        height: 40px;\r\n        border: none;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .gantt-period {\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        color: #2d3748;\r\n        min-width: 200px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .gantt-content {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .gantt-timeline {\r\n      .timeline-header {\r\n        display: flex;\r\n        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n        border-bottom: 2px solid #e2e8f0;\r\n\r\n        .task-header {\r\n          width: 300px;\r\n          padding: 20px 24px;\r\n          font-weight: 700;\r\n          color: #374151;\r\n          font-size: 14px;\r\n          letter-spacing: 0.025em;\r\n          border-right: 2px solid #e2e8f0;\r\n          display: flex;\r\n          align-items: center;\r\n        }\r\n\r\n        .dates-header {\r\n          flex: 1;\r\n          display: flex;\r\n\r\n          .date-cell {\r\n            flex: 1;\r\n            padding: 12px 8px;\r\n            text-align: center;\r\n            border-right: 1px solid #f1f5f9;\r\n            transition: all 0.3s ease;\r\n\r\n            &.today {\r\n              background: rgba(76, 81, 191, 0.1);\r\n\r\n              .date-day {\r\n                color: #4c51bf;\r\n                font-weight: 700;\r\n              }\r\n            }\r\n\r\n            .date-day {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n            }\r\n\r\n            .date-weekday {\r\n              font-size: 10px;\r\n              color: #718096;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.5px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .timeline-body {\r\n        .gantt-row {\r\n          display: flex;\r\n          border-bottom: 1px solid #f1f5f9;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n          }\r\n\r\n          .task-info {\r\n            width: 300px;\r\n            padding: 20px 24px;\r\n            border-right: 1px solid #f1f5f9;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n\r\n            .task-name {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              margin-bottom: 6px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-meta {\r\n              display: flex;\r\n              gap: 12px;\r\n              align-items: center;\r\n\r\n              .task-handler {\r\n                font-size: 12px;\r\n                color: #718096;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 4px 8px;\r\n                border-radius: 8px;\r\n                font-size: 10px;\r\n                font-weight: 600;\r\n                text-transform: uppercase;\r\n                letter-spacing: 0.5px;\r\n\r\n                &.status-pending {\r\n                  background: rgba(237, 137, 54, 0.1);\r\n                  color: #dd6b20;\r\n                }\r\n\r\n                &.status-working {\r\n                  background: rgba(66, 153, 225, 0.1);\r\n                  color: #3182ce;\r\n                }\r\n\r\n                &.status-completed {\r\n                  background: rgba(159, 122, 234, 0.1);\r\n                  color: #805ad5;\r\n                }\r\n\r\n                &.status-approved {\r\n                  background: rgba(72, 187, 120, 0.1);\r\n                  color: #38a169;\r\n                }\r\n\r\n                &.status-rejected {\r\n                  background: rgba(245, 101, 101, 0.1);\r\n                  color: #e53e3e;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .gantt-bars {\r\n            flex: 1;\r\n            padding: 16px 8px;\r\n            position: relative;\r\n            min-height: 80px;\r\n\r\n            .gantt-bar {\r\n              position: absolute;\r\n              height: 28px;\r\n              top: 50%;\r\n              transform: translateY(-50%);\r\n              border-radius: 14px;\r\n              cursor: pointer;\r\n              transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n              overflow: hidden;\r\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n              &:hover {\r\n                transform: translateY(-50%) scale(1.02);\r\n                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\r\n                z-index: 10;\r\n              }\r\n\r\n              &.status-pending {\r\n                background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              }\r\n\r\n              &.status-working {\r\n                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              }\r\n\r\n              &.status-completed {\r\n                background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              }\r\n\r\n              &.status-approved {\r\n                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              }\r\n\r\n              &.status-rejected {\r\n                background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              }\r\n\r\n              .bar-content {\r\n                height: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 0 12px;\r\n\r\n                .bar-text {\r\n                  color: white;\r\n                  font-size: 12px;\r\n                  font-weight: 600;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 按天视图样式\r\n.daily-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .daily-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .daily-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 24px;\r\n\r\n      .daily-nav-btn {\r\n        width: 44px;\r\n        height: 44px;\r\n        border: none;\r\n        border-radius: 12px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .date-picker-wrapper {\r\n        .daily-date-picker {\r\n          /deep/ .ant-calendar-picker-input {\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            text-align: center;\r\n            border: 2px solid #e2e8f0;\r\n            border-radius: 12px;\r\n            padding: 12px 20px;\r\n            min-width: 200px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover, &:focus {\r\n              border-color: #4c51bf;\r\n              box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 跨日期工单展示区域\r\n  .cross-date-section {\r\n    margin-bottom: 32px;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    border-radius: 20px;\r\n    padding: 24px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    backdrop-filter: blur(20px);\r\n\r\n    .cross-date-header {\r\n      margin-bottom: 20px;\r\n\r\n      .cross-date-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 700;\r\n        color: #1e293b;\r\n\r\n        .title-icon {\r\n          font-size: 24px;\r\n        }\r\n\r\n        .task-count {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: white;\r\n          padding: 6px 12px;\r\n          border-radius: 20px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n          margin-left: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .cross-date-timeline {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 12px;\r\n      align-items: flex-start;\r\n    }\r\n\r\n    .cross-date-task {\r\n      border-radius: 16px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      border: none;\r\n      position: relative;\r\n      overflow: hidden;\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        transform: translateY(-4px);\r\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: inherit;\r\n        filter: brightness(1.1);\r\n        z-index: -1;\r\n      }\r\n    }\r\n\r\n    .cross-task-content {\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .cross-task-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 12px;\r\n      gap: 12px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      color: white;\r\n      line-height: 1.4;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n      flex: 1;\r\n    }\r\n\r\n    .cross-task-status {\r\n      padding: 6px 12px;\r\n      border-radius: 20px;\r\n      font-size: 11px;\r\n      font-weight: 600;\r\n      text-transform: uppercase;\r\n      letter-spacing: 0.5px;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #1e293b;\r\n      white-space: nowrap;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    .cross-task-details {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n    }\r\n\r\n    .cross-task-handler,\r\n    .cross-task-time,\r\n    .cross-task-duration {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      font-size: 13px;\r\n      color: white;\r\n      background: rgba(255, 255, 255, 0.15);\r\n      padding: 6px 12px;\r\n      border-radius: 10px;\r\n      font-weight: 500;\r\n      backdrop-filter: blur(10px);\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n\r\n      i {\r\n        font-size: 14px;\r\n        opacity: 0.9;\r\n      }\r\n    }\r\n  }\r\n\r\n  .daily-content {\r\n    background: white;\r\n    border-radius: 20px;\r\n    overflow: visible;\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n\r\n    .timeline-container {\r\n      position: relative;\r\n      z-index: 1;\r\n      overflow: visible;\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n\r\n      .timeline-hours {\r\n        position: relative;\r\n        z-index: 2;\r\n        overflow: visible;\r\n\r\n        .hour-slot {\r\n          position: relative;\r\n          min-height: 120px; // 增加最小高度，为自适应工单提供更多空间\r\n          height: auto; // 改为auto，允许高度自适应\r\n          border-bottom: 2px solid #e2e8f0;\r\n          display: block;\r\n          overflow: visible; // 确保工单可以溢出时间槽\r\n          z-index: auto;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);\r\n          }\r\n\r\n          &:last-child {\r\n            border-bottom: 2px solid #e2e8f0;\r\n            border-bottom-left-radius: 20px;\r\n            border-bottom-right-radius: 20px;\r\n          }\r\n\r\n          &:first-child {\r\n            border-top-left-radius: 20px;\r\n            border-top-right-radius: 20px;\r\n          }\r\n\r\n          .hour-label {\r\n            width: 90px;\r\n            height: 120px; // 增加高度\r\n            min-height: 120px;\r\n            padding: 0;\r\n            font-size: 14px;\r\n            font-weight: 700;\r\n            color: white;\r\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n            border-right: 3px solid #e2e8f0;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: absolute;\r\n            left: 0;\r\n            top: 0;\r\n            z-index: 10;\r\n            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\r\n\r\n            .hour-text {\r\n              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n              letter-spacing: 0.5px;\r\n            }\r\n          }\r\n\r\n          .hour-line {\r\n            margin-left: 90px;\r\n            min-height: 120px; // 增加高度\r\n            height: auto; // 改为auto\r\n            position: relative;\r\n            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);\r\n            z-index: 1;\r\n\r\n            .half-hour-line {\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #d1d5db 0%, #e5e7eb 100%);\r\n              z-index: 2;\r\n\r\n              &::before {\r\n                content: '';\r\n                position: absolute;\r\n                left: -90px;\r\n                right: 0;\r\n                height: 1px;\r\n                background: linear-gradient(90deg, rgba(209, 213, 219, 0.5) 0%, #d1d5db 100%);\r\n              }\r\n            }\r\n\r\n            .quarter-hour-line {\r\n              position: absolute;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);\r\n              z-index: 2;\r\n              opacity: 0.6;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-slots {\r\n          position: absolute;\r\n          left: 90px;\r\n          right: 0;\r\n          top: 0;\r\n          height: auto; // 改为auto，允许高度自适应\r\n          min-height: 120px; // 更新最小高度\r\n          padding: 0; // 移除padding，让工单从最左侧开始\r\n          overflow: visible; // 确保内容可见\r\n          z-index: 100;\r\n          pointer-events: none;\r\n\r\n          .task-card {\r\n            border-radius: 12px;\r\n            padding: 16px; // 增加内边距确保内容完整显示\r\n            cursor: pointer;\r\n            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);\r\n            border: none;\r\n            font-size: 12px;\r\n            line-height: 1.4;\r\n            overflow: visible; // 改为visible，允许内容自适应\r\n            position: absolute;\r\n            z-index: 1000;\r\n            pointer-events: auto;\r\n            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n            backdrop-filter: blur(10px);\r\n            border-left: 4px solid;\r\n            min-height: 120px; // 增加最小高度确保内容显示\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: flex-start; // 改为flex-start，让内容从顶部开始\r\n            height: auto; // 允许高度自适应\r\n\r\n            &:hover {\r\n              transform: translateY(-2px) scale(1.02);\r\n              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 8px 15px rgba(0, 0, 0, 0.15);\r\n              z-index: 1001;\r\n            }\r\n\r\n            // 右上角状态标签\r\n            .task-status-corner {\r\n              position: absolute;\r\n              top: 8px;\r\n              right: 8px;\r\n              font-size: 9px;\r\n              font-weight: 600;\r\n              padding: 4px 8px;\r\n              border-radius: 10px;\r\n              line-height: 1.1;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.3px;\r\n              background: rgba(255, 255, 255, 0.95);\r\n              color: #1e293b;\r\n              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\r\n              backdrop-filter: blur(10px);\r\n              z-index: 10;\r\n              white-space: nowrap;\r\n              border: 1px solid rgba(255, 255, 255, 0.3);\r\n            }\r\n\r\n            // 多彩工单颜色方案\r\n            &.task-color-blue {\r\n              background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 1) 100%);\r\n              border-left-color: #1d4ed8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\r\n            }\r\n\r\n            &.task-color-purple {\r\n              background: linear-gradient(135deg, rgba(147, 51, 234, 0.95) 0%, rgba(126, 34, 206, 1) 100%);\r\n              border-left-color: #6b21a8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);\r\n            }\r\n\r\n            &.task-color-green {\r\n              background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(21, 128, 61, 1) 100%);\r\n              border-left-color: #14532d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);\r\n            }\r\n\r\n            &.task-color-orange {\r\n              background: linear-gradient(135deg, rgba(249, 115, 22, 0.95) 0%, rgba(234, 88, 12, 1) 100%);\r\n              border-left-color: #9a3412;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);\r\n            }\r\n\r\n            &.task-color-pink {\r\n              background: linear-gradient(135deg, rgba(236, 72, 153, 0.95) 0%, rgba(219, 39, 119, 1) 100%);\r\n              border-left-color: #831843;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);\r\n            }\r\n\r\n            &.task-color-cyan {\r\n              background: linear-gradient(135deg, rgba(6, 182, 212, 0.95) 0%, rgba(8, 145, 178, 1) 100%);\r\n              border-left-color: #164e63;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);\r\n            }\r\n\r\n            &.task-color-red {\r\n              background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 1) 100%);\r\n              border-left-color: #7f1d1d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);\r\n            }\r\n\r\n            &.task-color-indigo {\r\n              background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(79, 70, 229, 1) 100%);\r\n              border-left-color: #312e81;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);\r\n            }\r\n          }\r\n        }\r\n\r\n      // 工单卡片内容样式 - 一行布局，状态在右上角\r\n      .task-card-content {\r\n        position: relative;\r\n        z-index: 1;\r\n        width: 100%;\r\n        height: auto; // 改为auto，允许高度自适应\r\n        min-height: 100%; // 确保至少填满容器\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: flex-start; // 从顶部开始排列\r\n        gap: 12px; // 标题和meta行之间的间距\r\n        padding: 0; // 移除额外padding，使用卡片的padding\r\n        padding-right: 60px; // 为右上角状态标签留出空间\r\n\r\n        .task-title {\r\n          font-size: 14px; // 增大字体\r\n          font-weight: 700;\r\n          margin-bottom: 0; // 移除margin，使用gap\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          line-height: 1.3; // 增加行高\r\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n          letter-spacing: 0.2px;\r\n          flex-shrink: 0; // 防止标题被压缩\r\n          min-height: 20px; // 确保最小高度\r\n        }\r\n\r\n        // 处理人和时间在一行显示\r\n        .task-meta-row {\r\n          display: flex;\r\n          flex-direction: row; // 水平排列\r\n          gap: 8px; // 处理人和时间之间的间距\r\n          align-items: center;\r\n          font-size: 11px;\r\n          flex-shrink: 0; // 防止被压缩\r\n\r\n          .task-handler,\r\n          .task-time {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 4px; // 图标和文字间距\r\n            font-weight: 500;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            padding: 6px 10px; // 适中的内边距\r\n            border-radius: 8px;\r\n            backdrop-filter: blur(5px);\r\n            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n            line-height: 1.2;\r\n            min-height: 24px; // 确保最小高度\r\n            flex: 1; // 平分可用空间\r\n\r\n            i {\r\n              font-size: 11px;\r\n              opacity: 0.9;\r\n              flex-shrink: 0; // 防止图标被压缩\r\n            }\r\n          }\r\n\r\n          .task-handler {\r\n            max-width: 45%; // 限制处理人宽度\r\n          }\r\n\r\n          .task-time {\r\n            max-width: 55%; // 时间范围可以稍宽一些\r\n          }\r\n        }\r\n      }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n  // 跨日期工单颜色样式\r\n  .cross-date-task {\r\n    &.task-color-blue {\r\n      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n    }\r\n\r\n    &.task-color-purple {\r\n      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\r\n    }\r\n\r\n    &.task-color-green {\r\n      background: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n    }\r\n\r\n    &.task-color-orange {\r\n      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\r\n    }\r\n\r\n    &.task-color-pink {\r\n      background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);\r\n    }\r\n\r\n    &.task-color-cyan {\r\n      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);\r\n    }\r\n\r\n    &.task-color-red {\r\n      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\r\n    }\r\n\r\n    &.task-color-indigo {\r\n      background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\r\n    }\r\n  }\r\n\r\n// 确保工单卡片始终在最上层的全局样式\r\n.daily-container .task-card {\r\n  z-index: 9999 !important;\r\n  position: absolute !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;\r\n}\r\n\r\n// 日期详情弹窗样式\r\n.day-tasks-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n\r\n  .day-tasks-list {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n\r\n    .day-task-item {\r\n      padding: 16px;\r\n      border: 1px solid #f1f5f9;\r\n      border-radius: 12px;\r\n      margin-bottom: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        border-color: rgba(76, 81, 191, 0.2);\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n      }\r\n\r\n      .task-main {\r\n        .task-title {\r\n          margin: 0 0 8px 0;\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n        }\r\n\r\n        .task-info-row {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .task-handler {\r\n            color: #718096;\r\n            font-size: 14px;\r\n          }\r\n\r\n          .task-status {\r\n            padding: 4px 12px;\r\n            border-radius: 12px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n\r\n            &.status-pending {\r\n              background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-working {\r\n              background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-completed {\r\n              background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-approved {\r\n              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-rejected {\r\n              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              color: white;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-time {\r\n          color: #4c51bf;\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 其他样式保持不变...\r\n.enhanced-table {\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n  /deep/ .ant-table {\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n\r\n    .ant-table-thead > tr > th {\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n      font-weight: 700;\r\n      font-size: 14px;\r\n      color: #374151;\r\n      padding: 20px 16px;\r\n      text-align: center;\r\n      letter-spacing: 0.025em;\r\n\r\n      &:first-child {\r\n        border-top-left-radius: 16px;\r\n      }\r\n\r\n      &:last-child {\r\n        border-top-right-radius: 16px;\r\n      }\r\n    }\r\n\r\n    .ant-table-tbody > tr {\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        transform: scale(1.001);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);\r\n      }\r\n\r\n      > td {\r\n        padding: 18px 16px;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        font-size: 14px;\r\n        color: #374151;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    .ant-table-pagination {\r\n      margin: 28px 0 0;\r\n      text-align: center;\r\n\r\n      .ant-pagination-item {\r\n        border-radius: 10px;\r\n        border: 1px solid #e2e8f0;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        font-weight: 500;\r\n\r\n        &:hover {\r\n          border-color: #4c51bf;\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 4px 12px rgba(76, 81, 191, 0.15);\r\n        }\r\n\r\n        &.ant-pagination-item-active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          border-color: transparent;\r\n          box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n          a {\r\n            color: white;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优先级标签\r\n.priority-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.priority-0 {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.priority-1 {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.priority-2 {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 状态标签\r\n.status-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.status-pending {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.status-working {\r\n    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);\r\n  }\r\n\r\n  &.status-completed {\r\n    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(159, 122, 234, 0.3);\r\n  }\r\n\r\n  &.status-approved {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.status-rejected {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 工时显示\r\n.work-hour {\r\n  font-weight: 600;\r\n  color: #4c51bf;\r\n  background: rgba(76, 81, 191, 0.1);\r\n  padding: 6px 10px;\r\n  border-radius: 10px;\r\n  font-size: 13px;\r\n  letter-spacing: 0.025em;\r\n}\r\n\r\n// 时间范围显示\r\n.time-range {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n\r\n  .time-start, .time-end {\r\n    font-weight: 600;\r\n    color: #374151;\r\n  }\r\n\r\n  .time-divider {\r\n    color: #9ca3af;\r\n    font-size: 10px;\r\n    text-align: center;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n// 操作按钮\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n\r\n  .action-link {\r\n    padding: 6px 12px;\r\n    border-radius: 10px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    text-decoration: none;\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    letter-spacing: 0.025em;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n      transition: left 0.5s;\r\n    }\r\n\r\n    &:hover::before {\r\n      left: 100%;\r\n    }\r\n\r\n    &.claim {\r\n      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(66, 153, 225, 0.3);\r\n    }\r\n\r\n    &.start, &.restart {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.complete {\r\n      background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(159, 122, 234, 0.3);\r\n    }\r\n\r\n    &.approve {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.reject {\r\n      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(245, 101, 101, 0.3);\r\n    }\r\n\r\n    &.edit {\r\n      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(237, 137, 54, 0.3);\r\n    }\r\n\r\n    &.detail {\r\n      background: linear-gradient(135deg, #718096 0%, #4a5568 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(113, 128, 150, 0.3);\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      filter: brightness(1.05);\r\n      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .action-divider {\r\n    height: 16px;\r\n    margin: 0 4px;\r\n    border-color: #e2e8f0;\r\n  }\r\n}\r\n\r\n// Ant Design 组件样式覆盖\r\n/deep/ .ant-select {\r\n  .ant-select-selection {\r\n    height: 44px;\r\n    border: 1.5px solid #e2e8f0;\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    backdrop-filter: blur(10px);\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n    &:hover {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 4px 20px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    &.ant-select-selection--focused {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    .ant-select-selection__rendered {\r\n      line-height: 40px;\r\n      margin-left: 14px;\r\n      margin-right: 14px;\r\n      color: #374151;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .ant-select-arrow {\r\n      right: 14px;\r\n      color: #4c51bf;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ .ant-modal {\r\n  .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n    padding: 24px 32px;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 700;\r\n      letter-spacing: 0.025em;\r\n    }\r\n  }\r\n\r\n  .ant-modal-close {\r\n    top: 24px;\r\n    right: 32px;\r\n\r\n    .ant-modal-close-x {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .ant-modal-body {\r\n    padding: 32px;\r\n    background: rgba(255, 255, 255, 0.98);\r\n  }\r\n\r\n  .ant-modal-footer {\r\n    padding: 20px 32px 28px;\r\n    text-align: center;\r\n    border-top: 1px solid #f1f5f9;\r\n    background: rgba(255, 255, 255, 0.98);\r\n\r\n    .ant-btn {\r\n      border-radius: 12px;\r\n      font-weight: 600;\r\n      padding: 10px 24px;\r\n      height: auto;\r\n      letter-spacing: 0.025em;\r\n\r\n      &.ant-btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px 10px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .task-info {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-day {\r\n      min-height: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .project-detail-app {\r\n    padding: 16px;\r\n  }\r\n\r\n  .tabs-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    padding: 20px;\r\n\r\n    .tabs-left {\r\n      order: 2;\r\n    }\r\n\r\n    .view-switcher {\r\n      order: 1;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-header {\r\n      .calendar-title {\r\n        font-size: 20px;\r\n        min-width: 150px;\r\n      }\r\n    }\r\n\r\n    .calendar-day {\r\n      min-height: 80px;\r\n      padding: 8px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .gantt-content {\r\n      overflow-x: auto;\r\n    }\r\n\r\n    .task-info {\r\n      width: 200px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .tabs-left {\r\n    flex-direction: column;\r\n\r\n    .tab-btn {\r\n      padding: 12px 16px;\r\n    }\r\n  }\r\n\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .calendar-day {\r\n    min-height: 60px;\r\n    padding: 4px;\r\n\r\n    .task-item {\r\n      font-size: 10px;\r\n      padding: 2px 4px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 完成工单模态框样式\r\n.finish-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n  \r\n  .upload-container {\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    transition: all 0.3s;\r\n    \r\n    &:hover {\r\n      border-color: #1890ff;\r\n    }\r\n  }\r\n  \r\n  .upload-tips {\r\n    margin-top: 8px;\r\n    color: #999;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    text-align: right;\r\n    margin-top: 24px;\r\n    \r\n    .ant-btn {\r\n      margin-left: 8px;\r\n    }\r\n    \r\n    .ant-btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      border: none;\r\n      box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n      \r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式优化\r\n  @media (max-width: 768px) {\r\n    .cross-date-timeline {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .cross-date-task {\r\n      width: 100% !important;\r\n      margin-right: 0 !important;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 70px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 70px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 70px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .cross-date-task {\r\n      padding: 16px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 14px;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 60px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 60px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 60px;\r\n    }\r\n\r\n    .task-card {\r\n      padding: 8px 12px;\r\n    }\r\n\r\n    .task-card-content .task-title {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n</style>"], "sourceRoot": "src/views/admin/WorkOrderTask/modules"}]}