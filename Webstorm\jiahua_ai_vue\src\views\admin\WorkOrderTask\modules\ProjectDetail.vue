<template>
  <div class="project-detail-app">
    <!-- 项目信息头部 -->
    <div class="project-header">
      <div class="header-content">
        <div class="project-info">
          <button class="back-btn" @click="goBack">
            ← 返回项目列表
          </button>
          <h1 class="project-title">{{ projectInfo.projectName }}</h1>
          <div class="project-meta">
            <span class="project-mode">{{ getModeText(projectInfo.mode) }}</span>
          </div>
        </div>
        <div class="header-actions">
          <button class="btn-secondary" v-if="projectInfo.mode==='hours'" @click="viewProjectStats">工时统计</button>
          <button class="btn-primary" v-if="userRole==='admin'" @click="handleAddTask">新建工单</button>
        </div>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="filter-row">
          <div class="filter-group" v-if="userRole === 'admin' && activeTab === 'all'">
            <label>处理人</label>
            <a-select v-model="queryParam.handling" placeholder="请选择处理人">
              <a-select-option v-for="d in projectMemberList" :key="d.id" :value="d.user_id">
                {{ d.realname }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-group">
            <label>状态</label>
            <a-select 
              v-model="queryParam.workStatus" 
              placeholder="请选择状态" 
              mode="multiple"
              class="multi-select-wrap">
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="working">进行中</a-select-option>
              <a-select-option value="completed">待审核</a-select-option>
              <a-select-option value="approved">已审核</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
            </a-select>
          </div>

          <div class="filter-actions">
            <button class="btn-primary" @click="searchQuery">搜索</button>
            <button class="btn-ghost" @click="searchReset">重置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页切换 -->
    <div class="tabs-container">
      <div class="tabs-header">
        <div class="tabs-left">
          <button
            v-for="tab in tabs[this.userRole]"
            :key="tab.value"
            :class="['tab-btn', { active: activeTab === tab.value }]"
            @click="switchTab(tab.value)"
          >
            {{ tab.label }}
            <span class="tab-count" v-if="tab.value === 'pool'">{{ poolTotal }}</span>
            <span class="tab-count" v-if="tab.value === 'my'">{{ myTotal }}</span>
            <span class="tab-count" v-if="tab.value === 'all'">{{ allTotal }}</span>
            <span class="tab-count" v-if="tab.value === 'audit'">{{ auditTotal }}</span>
          </button>
        </div>

        <!-- 视图切换按钮 - 仅在排期模式和全部工单标签页显示 -->
        <div class="view-switcher" v-if="projectInfo.mode === 'timespan' && (activeTab === 'all' || activeTab === 'my')">
          <button
            :class="['view-btn', { active: currentView === 'table' }]"
            @click="switchView('table')"
            title="表格视图"
          >
            <i class="icon-table">表格</i>
          </button>
          <button
            :class="['view-btn', { active: currentView === 'calendar' }]"
            @click="switchView('calendar')"
            title="月历视图"
          >
            <i class="icon-calendar">月历</i>
          </button>
          <button
            :class="['view-btn', { active: currentView === 'daily' }]"
            @click="switchView('daily')"
            title="按天视图"
          >
            <i class="icon-daily">按天</i>
          </button>
          <button
            :class="['view-btn', { active: currentView === 'gantt' }]"
            @click="switchView('gantt')"
            title="甘特图"
          >
            <i class="icon-gantt">甘特图</i>
          </button>
        </div>
      </div>

      <!-- 表格视图 -->
      <div class="table-container" v-show="currentView === 'table'">
        <a-spin :spinning="loading" class="table-loading">
          <a-table
            ref="table"
            size="middle"
            bordered
            :rowKey="record => record.id"
            :columns="dataColumns[projectInfo.mode]"
            :dataSource="dataSource"
            :pagination="ipagination"
            class="enhanced-table"
            @change="handleTableChange"
          >
            <template slot="prioritySlot" slot-scope="text">
              <span class="priority-tag" :class="'priority-' + text">{{ getPriorityText(text) }}</span>
            </template>

            <template slot="workHourSlot" slot-scope="text">
              <span class="work-hour">{{ text }}h</span>
            </template>

            <template slot="workStatus" slot-scope="text">
              <span class="status-tag" :class="'status-' + text">{{ getWorkStatusText(text) }}</span>
            </template>

            <template slot="plannedTimeSlot" slot-scope="text, record">
              <div class="time-range">
                <div class="time-start">{{ record.startTime }}</div>
                <div class="time-divider">至</div>
                <div class="time-end">{{ record.endTime }}</div>
              </div>
            </template>

            <template slot="reviewTime" slot-scope="text, record">
              <span v-if="record.workStatus === 'approved'" class="status-tag" :class="'status-' + text">{{ text }}</span>
            </template>

            <!-- 操作列 -->
            <template slot="action" slot-scope="text, record">
              <div class="action-buttons">
                <a v-if="activeTab === 'pool'" @click="claimTask(record)" class="action-link claim">领取</a>

                <a v-if="activeTab === 'my' && record.workStatus === 'rejected'"
                   @click="updateWorkStatus(record.id, 'working')" class="action-link restart">重新开始</a>
                <a v-if="activeTab === 'my' && record.workStatus === 'pending'"
                   @click="updateWorkStatus(record.id, 'working')" class="action-link start">开始</a>
                <a v-if="activeTab === 'my' && record.workStatus === 'working'"
                   @click="showFinishModal(record)" class="action-link complete">结束</a>

                <a v-if="activeTab === 'audit'" @click="audit(record.id, '1')" class="action-link approve">通过</a>
                <a-divider v-if="activeTab === 'audit'" type="vertical" class="action-divider"/>
                <a v-if="activeTab === 'audit'" @click="audit(record.id, '2')" class="action-link reject">拒绝</a>

                <a-divider v-if="record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')" type="vertical" class="action-divider"/>
                <a v-if="record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')" @click="taskEdit(record)" class="action-link edit">编辑</a>

                <a v-if="userRole === 'admin' && (activeTab === 'all' && record.workStatus === 'pending')" @click="taskEdit2(record)" class="action-link edit">转交负责人</a>

                <a-divider v-if="(activeTab !== 'all' && record.workStatus !== 'approved' && (record.workStatus !== 'completed' || activeTab === 'audit'))" type="vertical" class="action-divider"/>
                <a @click="viewTaskDetail(record)" class="action-link detail">详情</a>
              </div>
            </template>
          </a-table>
        </a-spin>
      </div>

      <!-- 月历视图 -->
      <div class="calendar-container" v-show="currentView === 'calendar'">
        <div class="calendar-header">
          <button class="calendar-nav-btn" @click="previousMonth">
            <i class="icon-prev">‹</i>
          </button>
          <h3 class="calendar-title">{{ currentMonth.format('YYYY年 MM月') }}</h3>
          <button class="calendar-nav-btn" @click="nextMonth">
            <i class="icon-next">›</i>
          </button>
        </div>

        <div class="calendar-grid">
          <div class="calendar-weekdays">
            <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
          </div>

          <div class="calendar-days">
            <div
              v-for="day in calendarDays"
              :key="day.date"
              :class="['calendar-day', {
                'other-month': !day.isCurrentMonth,
                'today': day.isToday,
                'has-tasks': day.tasks.length > 0
              }]"
            >
              <div class="day-number">{{ day.dayNumber }}</div>
              <div class="day-tasks">
                <div
                  v-for="task in day.tasks.slice(0, 3)"
                  :key="task.id"
                  :class="['task-item', 'status-' + task.workStatus]"
                  @click="viewTaskDetail(task)"
                  :title="task.ordername"
                >
                  <span class="task-name">{{ task.ordername }}</span>
                  <span class="task-handler">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5) }}</span>
                </div>
                <div v-if="day.tasks.length > 3" class="more-tasks" @click="showDayTasks(day)">
                  +{{ day.tasks.length - 3 }} 更多
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 甘特图视图 -->
      <div class="gantt-container" v-show="currentView === 'gantt'">
        <div class="gantt-header">
          <div class="gantt-controls">
            <button class="gantt-nav-btn" @click="previousGanttPeriod">
              <i class="icon-prev">‹</i>
            </button>
            <span class="gantt-period">{{ ganttPeriod.start.format('MM/DD') }} - {{ ganttPeriod.end.format('MM/DD') }}</span>
            <button class="gantt-nav-btn" @click="nextGanttPeriod">
              <i class="icon-next">›</i>
            </button>
          </div>
        </div>

        <div class="gantt-content">
          <div class="gantt-timeline">
            <div class="timeline-header">
              <div class="task-header">工单</div>
              <div class="dates-header">
                <div
                  v-for="date in ganttDates"
                  :key="date.format('YYYY-MM-DD')"
                  :class="['date-cell', { 'today': date.isSame(moment(), 'day') }]"
                >
                  <div class="date-day">{{ date.format('DD') }}</div>
                  <div class="date-weekday">{{ date.format('ddd') }}</div>
                </div>
              </div>
            </div>

            <div class="timeline-body">
              <div
                v-for="task in ganttTasks"
                :key="task.id"
                class="gantt-row"
              >
                <div class="task-info">
                  <div class="task-name" :title="task.ordername">{{ task.ordername }}</div>
                  <div class="task-meta">
                    <span class="task-handler">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5)}}</span>
                    <span :class="['task-status', 'status-' + task.workStatus]">
                      {{ getWorkStatusText(task.workStatus) }}
                    </span>
                  </div>
                </div>

                <div class="gantt-bars">
                  <div
                    :class="['gantt-bar', 'status-' + task.workStatus]"
                    :style="getGanttBarStyle(task)"
                    @click="viewTaskDetail(task)"
                    :title="`${task.ordername} (${task.startTime} - ${task.endTime})`"
                  >
                    <div class="bar-content">
                      <span class="bar-text">{{ task.ordername }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 按天视图 -->
      <div class="daily-container" v-show="currentView === 'daily'">
        <div class="daily-header">
          <div class="daily-controls">
            <button class="daily-nav-btn" @click="previousDay">
              <i class="icon-prev">‹</i>
            </button>
            <div class="date-picker-wrapper">
              <a-date-picker
                v-model="currentDay"
                :format="'YYYY年MM月DD日'"
                :allowClear="false"
                @change="onDayChange"
                class="daily-date-picker"
              />
            </div>
            <button class="daily-nav-btn" @click="nextDay">
              <i class="icon-next">›</i>
            </button>
          </div>
        </div>

        <div class="daily-content">
          <!-- 跨日期工单展示区域 -->
          <div class="cross-date-section" v-if="getCrossDayTasksForCurrentDay().length > 0">
            <div class="cross-date-header">
              <h3 class="cross-date-title">
                <span class="title-icon">📅</span>
                跨日期工单
                <span class="task-count">{{ getCrossDayTasksForCurrentDay().length }}</span>
              </h3>
            </div>
            <div class="cross-date-timeline">
              <div
                v-for="(task, index) in getCrossDayTasksForCurrentDay()"
                :key="task.id"
                :class="['cross-date-task', getTaskColorClass(task, index)]"
                :style="getCrossDayTaskStyle(task, index)"
                @click="viewTaskDetail(task)"
              >
                <div class="cross-task-content">
                  <div class="cross-task-header">
                    <div class="cross-task-title">{{ task.ordername }}</div>
                    <div :class="['cross-task-status', 'status-' + task.workStatus]">
                      {{ getWorkStatusText(task.workStatus) }}
                    </div>
                  </div>
                  <div class="cross-task-details">
                    <div class="cross-task-handler">
                      <i class="icon-user">👤</i>
                      {{ task.handling }}
                    </div>
                    <div class="cross-task-time">
                      <i class="icon-time">⏰</i>
                      {{ formatCrossDayTaskTime(task) }}
                    </div>
                    <div class="cross-task-duration">
                      <i class="icon-duration">⏱️</i>
                      {{ getCrossDayTaskDuration(task) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 时间轴容器 -->
          <div class="timeline-container">
            <div class="timeline-hours">
              <div
                v-for="hour in timelineHours"
                :key="hour"
                class="hour-slot"
              >
                <!-- 美观的小时标签 -->
                <div class="hour-label">
                  <div class="hour-text">{{ hour }}:00</div>
                </div>

                <!-- 时间线区域 -->
                <div class="hour-line">
                  <!-- 半小时刻度线 -->
                  <div class="half-hour-line"></div>

                  <!-- 15分钟刻度线 -->
                  <div class="quarter-hour-line" style="top: 25%"></div>
                  <div class="quarter-hour-line" style="top: 75%"></div>
                </div>

                <!-- 工单展示区域 -->
                <div class="task-slots">
                  <div
                    v-for="task in getTasksForHour(hour)"
                    :key="task.id"
                    :class="['task-card', getTaskColorClass(task)]"
                    :style="getTaskCardStyle(task)"
                    @click="viewTaskDetail(task)"
                  >
                    <div class="task-card-content">
                      <div class="task-title">{{ task.ordername }}</div>
                      <div class="task-meta">
                        <div class="task-handler">
                          <i class="icon-user">👤</i>
                          {{ task.handling }}
                        </div>
                        <div class="task-time">
                          <i class="icon-time">⏰</i>
                          {{ formatTaskTime(task) }}
                        </div>
                      </div>
                      <div :class="['task-status', 'status-' + task.workStatus]">
                        {{ getWorkStatusText(task.workStatus) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期详情弹窗 -->
    <a-modal
      v-model="dayTasksVisible"
      :title="`${selectedDay ? selectedDay.date : ''} 的工单`"
      :footer="null"
      width="800px"
      class="day-tasks-modal"
    >
      <div class="day-tasks-list">
        <div
          v-for="task in selectedDayTasks"
          :key="task.id"
          class="day-task-item"
          @click="viewTaskDetail(task)"
        >
          <div class="task-main">
            <h4 class="task-title">{{ task.ordername }}</h4>
            <div class="task-info-row">
              <span class="task-handler">处理人: {{ task.handling }}</span>
              <span :class="['task-status', 'status-' + task.workStatus]">
                {{ getWorkStatusText(task.workStatus) }}
              </span>
            </div>
            <div class="task-time">
              {{ task.startTime }} - {{ task.endTime }}
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 其他弹窗 -->
    <div>
      <a-modal
        title="工单审核"
        :visible="isShowAudit"
        @ok="audit(null,'3')"
        @cancel="isShowAudit = false"
        class="audit-modal"
      >
        <a-form-model>
          <a-form-model-item label="审核意见" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
            <a-textarea :rows="3" v-model="auditOpinion" placeholder="请输入审核意见"></a-textarea>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </div>

    <!-- 工单弹窗 -->
    <work-order-task-modal
      ref="taskModal"
      :project-info="projectInfo"
      @ok="taskModalOk"
    ></work-order-task-modal>

          <!-- 工单详情弹窗 -->
    <task-detail-modal ref="taskDetailModal"></task-detail-modal>

    <!-- 工时统计弹窗 -->
    <project-stats-modal ref="projectStatsModal"></project-stats-modal>
    
    <!-- 工单完成弹窗 -->
    <a-modal
      title="完成工单"
      :visible="finishModalVisible"
      :confirmLoading="finishConfirmLoading"
      @cancel="finishModalVisible = false"
      :footer="null"
      width="30%"
      class="finish-modal"
    >
      <a-spin :spinning="finishConfirmLoading">
        <a-form-model :model="finishForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-model-item label="上传附件">
            <div class="upload-container" @dragover.prevent @drop.prevent="handleDrop">
              <a-upload
                name="file"
                :action="uploadAction"
                :headers="uploadHeaders"
                :file-list="finishForm.fileList"
                @change="handleFileChange"
                :multiple="true"
                :show-upload-list="true"
                :before-upload="beforeUpload"
              >
                <a-button type="dashed">
                  <a-icon type="upload" /> 点击上传
                </a-button>
                <div class="upload-tips">
                  <span>支持：点击上传、拖拽上传（附件非必选）</span>
                </div>
              </a-upload>
            </div>
          </a-form-model-item>
          
          <a-form-model-item label="完成说明">
            <a-textarea 
              :rows="4" 
              v-model="finishForm.finishDescribe" 
              placeholder="请输入完成说明（非必填）"
            />
          </a-form-model-item>
        </a-form-model>
        
        <div class="modal-footer">
          <a-button @click="finishModalVisible = false">取消</a-button>
          <a-button type="primary" @click="handleFinishOk" :loading="finishConfirmLoading">立即结束</a-button>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import WorkOrderTaskModal from './WorkOrderTaskModal'
import TaskDetailModal from './TaskDetailModal'
import ProjectStatsModal from './ProjectStatsModal'
import moment from 'moment'
import { getAction, putAction, httpAction } from '@api/manage'
import { Modal } from 'ant-design-vue';
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue';

export default {
  name: 'ProjectDetail',
  mixins: [mixinDevice],
  components: {
    WorkOrderTaskModal,
    TaskDetailModal,
    ProjectStatsModal
  },
  data() {
    return {
      activeTab: 'pool',
      currentView: 'table', // 新增：当前视图类型
      currentUserId: '',
      projectInfo: {},
      projectMemberList: [],
      queryParam: {
        workStatus: []
      },
      uploadAction: `${window._CONFIG['domianURL']}/sys/common/upload`,
      uploadHeaders: {},
      finishModalVisible: false,
      finishConfirmLoading: false,
      currentTask: null,
      finishForm: {
        fileList: [],
        finishDescribe: '',
        finishAnnex: ''
      },
      poolTasks: [],
      myTasks: [],
      workingTimers: new Map(),

      // 月历相关
      currentMonth: moment(),
      weekdays: ['一', '二', '三', '四', '五', '六','日'],
      dayTasksVisible: false,
      selectedDay: null,
      selectedDayTasks: [],

      // 甘特图相关
      ganttPeriod: {
        start: moment().startOf('week'),
        end: moment().endOf('week').add(6, 'days')
      },

      // 按天视图相关
      currentDay: moment(),
      dailyColumnAssignment: null, // 当天工单列分配缓存
      timelineHours: ['00', ...Array.from({ length: 23 }, (_, i) => (i + 1).toString().padStart(2, '0'))], // 0-23小时，完整24小时

      tabs: {
        'admin' : [
          { value: 'pool', label: '公海池' },
          { value: 'my', label: '我的工单' },
          { value: 'all', label: '全部工单' },
          { value: 'audit', label: '待审核' }
        ],
        'member': [
          { value: 'pool', label: '公海池' },
          { value: 'my', label: '我的工单' }
        ]
      },
      pageNo: 1,
      pageSize: 3,
      total: 0,
      poolTotal: 0,
      myTotal: 0,
      auditTotal: 0,
      allTotal: 0,
      userRole: 'member',
      dataSource: [],
      dataColumns: {
        'hours': [
          {
            title: '工单名称',
            align: 'center',
            dataIndex: 'ordername',
            width: 200
          },
          {
            title: '优先级',
            align: 'center',
            dataIndex: 'priority',
            width: 100,
            scopedSlots: { customRender: 'prioritySlot' }
          },
          {
            title: '处理人',
            align: 'center',
            dataIndex: 'handling',
            width: 120
          },
          {
            title: '工时',
            align: 'center',
            dataIndex: 'estimatedHours',
            width: 120,
            scopedSlots: { customRender: 'workHourSlot' }
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'workStatus',
            width: 100,
            scopedSlots: { customRender: 'workStatus' }
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime',
            width: 100,
            scopedSlots: { customRender: 'createTime' }
          },
          {
            title: '审核意见',
            align: 'center',
            dataIndex: 'reviewComment',
            width: 100,
            scopedSlots: { customRender: 'reviewComment' }
          },
          {
            title: '完成时间',
            align: 'center',
            dataIndex: 'reviewTime',
            width: 100,
            scopedSlots: { customRender: 'reviewTime' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 180,
            scopedSlots: { customRender: 'action' }
          }
        ],
        'timespan': [
          {
            title: '工单名称',
            align: 'center',
            dataIndex: 'ordername',
            width: 200
          },
          {
            title: '优先级',
            align: 'center',
            dataIndex: 'priority',
            width: 100,
            scopedSlots: { customRender: 'prioritySlot' }
          },
          {
            title: '处理人',
            align: 'center',
            dataIndex: 'handling',
            width: 120
          },
          {
            title: '计划时间',
            align: 'center',
            width: 120,
            scopedSlots: { customRender: 'plannedTimeSlot' }
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'workStatus',
            width: 100,
            scopedSlots: { customRender: 'workStatus' }
          },
          {
            title: '审核意见',
            align: 'center',
            dataIndex: 'reviewComment',
            width: 100,
            scopedSlots: { customRender: 'reviewComment' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 180,
            scopedSlots: { customRender: 'action' }
          }
        ]
      },
      loading: false,
      selectedRowKeys: [],
      selectionRows: [],
      ipagination: {
        current: 1,
        pageSize: 15,
        total: 0,
      },
      statusMap: {
        'pending': '待处理',
        'working': '进行中',
        'completed': '待审核',
        'approved': '已审核',
        'rejected': '已驳回'
      },
      progressStageMap: {
        '0': '准备阶段',
        '1': '实施阶段',
        '2': '完成阶段'
      },
      isShowAudit: false,
      auditOpinion: '',
      currentId: '',
      allTasksForView: [],
    }
  },
  computed: {
    projectId() {
      return this.$route.query.id
    },

    moment() {
      return moment
    },

    // 月历计算属性
    calendarDays() {
      const startOfMonth = this.currentMonth.clone().startOf('month')
      const endOfMonth = this.currentMonth.clone().endOf('month')
      const startDate = startOfMonth.clone().startOf('week')
      const endDate = endOfMonth.clone().endOf('week')

      const days = []
      const current = startDate.clone()

      while (current.isSameOrBefore(endDate)) {
        const dayTasks = this.getTasksForDate(current)
        days.push({
          date: current.format('YYYY-MM-DD'),
          dayNumber: current.date(),
          isCurrentMonth: current.isSame(this.currentMonth, 'month'),
          isToday: current.isSame(moment(), 'day'),
          tasks: dayTasks
        })
        current.add(1, 'day')
      }

      return days
    },

    // 甘特图计算属性
    ganttDates() {
      const dates = []
      const current = this.ganttPeriod.start.clone()

      while (current.isSameOrBefore(this.ganttPeriod.end)) {
        dates.push(current.clone())
        current.add(1, 'day')
      }

      return dates
    },

    ganttTasks() {
      // 优先使用当前视图的数据，如果没有则使用表格数据
      const tasksForView = this.allTasksForView || this.dataSource
      return tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false
        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)

        return taskStart.isSameOrBefore(this.ganttPeriod.end) &&
          taskEnd.isSameOrAfter(this.ganttPeriod.start)
      })
    }
  },
  created(options) {
    console.log("222333444")
    console.log(options)
    console.log("进入人33333333")
  },

  mounted() {
    console.log("进入人222222222")
    this.currentUserId = this.$store.getters.userInfo.id;
    this.loadProjectInfo()
    this.loadProjectMember();
    this.loadTasks()
    this.startWorkingTimers()
    
    // 设置上传头部
    const token = Vue.ls.get(ACCESS_TOKEN)
    this.uploadHeaders = { 'X-Access-Token': token }
  },

  beforeDestroy() {
    this.clearWorkingTimers()
  },

  methods: {
    // 视图切换
    switchView(view) {
      this.currentView = view

      // 如果切换到日历或甘特图视图，重新加载当前标签页的数据
      if (view !== 'table') {
        this.loadAllTasksForView()
      }
    },

    loadAllTasksForView() {
      this.loading = true;
      // 先清空旧数据
      this.allTasksForView = []

      let params = {...this.queryParam};
      
      // 将状态数组转换为逗号分隔的字符串
      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {
        params.workStatus = params.workStatus.join(',');
      }
      
      params.projectId = this.projectId;
      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));
      params.pageNo = 1;
      params.pageSize = 1000;

      this.$http.get('/WorkOrderTask/list', { params }).then(res => {
        if (res.success) {
          this.allTasksForView = res.result.data
        }
        this.loading = false
      })
    },

    // 月历相关方法
    previousMonth() {
      this.currentMonth = this.currentMonth.clone().subtract(1, 'month')
    },

    nextMonth() {
      this.currentMonth = this.currentMonth.clone().add(1, 'month')
    },

    getTasksForDate(date) {
      const dateStr = date.format('YYYY-MM-DD')
      // 优先使用当前视图的数据，如果没有则使用表格数据
      const tasksForView = this.allTasksForView || this.dataSource

      return tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false

        const startDate = moment(task.startTime).format('YYYY-MM-DD')
        const endDate = moment(task.endTime).format('YYYY-MM-DD')

        return dateStr >= startDate && dateStr <= endDate
      })
    },

    showDayTasks(day) {
      this.selectedDay = day
      this.selectedDayTasks = day.tasks
      this.dayTasksVisible = true
    },

    // 甘特图相关方法
    previousGanttPeriod() {
      this.ganttPeriod.start = this.ganttPeriod.start.clone().subtract(7, 'days')
      this.ganttPeriod.end = this.ganttPeriod.end.clone().subtract(7, 'days')
    },

    nextGanttPeriod() {
      this.ganttPeriod.start = this.ganttPeriod.start.clone().add(7, 'days')
      this.ganttPeriod.end = this.ganttPeriod.end.clone().add(7, 'days')
    },

    getGanttBarStyle(task) {
      if (!task.startTime || !task.endTime) return { display: 'none' }

      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const periodStart = this.ganttPeriod.start
      const periodEnd = this.ganttPeriod.end

      // 计算任务在甘特图中的位置
      const totalDays = periodEnd.diff(periodStart, 'days') + 1
      const dayWidth = 100 / totalDays

      // 计算开始位置
      let startOffset = 0
      if (taskStart.isSameOrAfter(periodStart)) {
        startOffset = taskStart.diff(periodStart, 'days') * dayWidth
      }

      // 计算宽度
      let width = dayWidth
      if (taskEnd.isAfter(taskStart)) {
        const visibleStart = moment.max(taskStart, periodStart)
        const visibleEnd = moment.min(taskEnd, periodEnd)
        const visibleDays = visibleEnd.diff(visibleStart, 'days') + 1
        width = visibleDays * dayWidth
      }

      return {
        left: `${startOffset}%`,
        width: `${width}%`
      }
    },

    // 按天视图相关方法
    previousDay() {
      this.currentDay = this.currentDay.clone().subtract(1, 'day')
      this.clearDailyColumnAssignment()
    },

    nextDay() {
      this.currentDay = this.currentDay.clone().add(1, 'day')
      this.clearDailyColumnAssignment()
    },

    onDayChange(date) {
      if (date) {
        this.currentDay = moment(date)
        this.clearDailyColumnAssignment()
      }
    },

    clearDailyColumnAssignment() {
      // 清除当天的列分配缓存，强制重新计算
      this.dailyColumnAssignment = null
    },

    getTasksForHour(hour) {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const tasksForView = this.allTasksForView || this.dataSource

      const tasks = tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false

        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)
        const taskStartDate = taskStart.format('YYYY-MM-DD')
        const taskEndDate = taskEnd.format('YYYY-MM-DD')

        // 关键修改：排除跨日期工单，只显示当天的工单
        if (taskStartDate !== taskEndDate) {
          return false // 跨日期工单不在时间轴内显示
        }

        // 只处理当天的工单
        if (currentDateStr !== taskStartDate) {
          return false
        }

        const startHour = taskStart.hour()
        const hourNum = parseInt(hour)

        // 只在开始小时显示工单
        return hourNum === startHour
      })

      // 为重叠的工单分配列位置，从最左侧开始
      return this.assignTaskColumnsFromLeft(tasks, hour)
    },

    // 新的列分配方法：从最左侧开始排列
    assignTaskColumnsFromLeft(tasks, hour) {
      if (tasks.length <= 1) {
        return tasks.map(task => ({ ...task, columnIndex: 0, totalColumns: 1 }))
      }

      // 简化的列分配：按开始时间排序，从左到右分配
      const sortedTasks = tasks.sort((a, b) => {
        const timeA = moment(a.startTime)
        const timeB = moment(b.startTime)
        return timeA.valueOf() - timeB.valueOf()
      })

      // 检查时间重叠，分配到不同列
      const columns = []

      sortedTasks.forEach(task => {
        let assignedColumn = -1
        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)

        // 寻找不重叠的列
        for (let colIndex = 0; colIndex < columns.length; colIndex++) {
          let canUseColumn = true

          for (let existingTask of columns[colIndex]) {
            const existingStart = moment(existingTask.startTime)
            const existingEnd = moment(existingTask.endTime)

            // 检查时间重叠
            if (taskStart.isBefore(existingEnd) && taskEnd.isAfter(existingStart)) {
              canUseColumn = false
              break
            }
          }

          if (canUseColumn) {
            assignedColumn = colIndex
            break
          }
        }

        // 如果没有找到合适的列，创建新列
        if (assignedColumn === -1) {
          assignedColumn = columns.length
          columns.push([])
        }

        columns[assignedColumn].push(task)
        task.columnIndex = assignedColumn
        task.totalColumns = Math.min(4, columns.length) // 最多4列
      })

      // 确保所有工单都有相同的总列数
      const totalColumns = Math.min(4, columns.length)
      sortedTasks.forEach(task => {
        task.totalColumns = totalColumns
      })

      return sortedTasks
    },

    assignTaskColumns(tasks, hour) {
      // 保留原方法以兼容其他调用
      return this.assignTaskColumnsFromLeft(tasks, hour)
    },

    performDailyColumnAssignment(allTasks) {
      console.log('开始全局列分配，工单数量:', allTasks.length)

      // 按开始时间排序所有工单
      const sortedTasks = allTasks.sort((a, b) => {
        const startA = moment(a.startTime)
        const startB = moment(b.startTime)
        return startA.valueOf() - startB.valueOf()
      })

      const columns = []

      // 为每个工单分配列，使用更严格的重叠检测
      sortedTasks.forEach((task, index) => {
        let assignedColumn = -1

        console.log(`处理工单 ${index + 1}: ${task.ordername}`)
        console.log(`工单时间: ${task.startTime} - ${task.endTime}`)

        // 尝试找到一个不重叠的列
        for (let colIndex = 0; colIndex < columns.length; colIndex++) {
          let canUseColumn = true

          for (let existingTask of columns[colIndex]) {
            const overlaps = this.tasksOverlapInTime(task, existingTask)
            console.log(`检查与工单 ${existingTask.ordername} 的重叠: ${overlaps}`)

            if (overlaps) {
              canUseColumn = false
              break
            }
          }

          if (canUseColumn) {
            assignedColumn = colIndex
            console.log(`分配到列 ${colIndex}`)
            break
          }
        }

        // 如果没有找到合适的列，创建新列
        if (assignedColumn === -1) {
          assignedColumn = columns.length
          columns.push([])
          console.log(`创建新列 ${assignedColumn}`)
        }

        columns[assignedColumn].push(task)
        task.columnIndex = assignedColumn
        task.totalColumns = Math.min(4, columns.length) // 最多4列
      })

      // 确保所有工单都有相同的总列数
      const totalColumns = Math.min(4, columns.length)
      sortedTasks.forEach(task => {
        task.totalColumns = totalColumns
      })

      console.log(`列分配完成，总列数: ${totalColumns}`)
      console.log('各列工单分布:', columns.map((col, index) => ({
        column: index,
        tasks: col.map(t => t.ordername)
      })))

      // 缓存当天的列分配结果
      this.dailyColumnAssignment = {
        date: this.currentDay.format('YYYY-MM-DD'),
        tasks: sortedTasks
      }
    },

    tasksOverlapInTime(task1, task2) {
      // 获取两个工单在当前日期的可见时间范围
      const currentDate = this.currentDay.format('YYYY-MM-DD')
      const range1 = this.getTaskVisibleRangeForDate(task1, currentDate)
      const range2 = this.getTaskVisibleRangeForDate(task2, currentDate)

      if (!range1 || !range2) {
        console.log(`重叠检测失败: range1=${!!range1}, range2=${!!range2}`)
        return false
      }

      // 改进的重叠检测：增加时间缓冲区，使相近的工单也被认为重叠
      // 这样可以避免视觉上相近的工单在同一列显示
      const bufferMinutes = 30 // 30分钟缓冲区
      const range1StartWithBuffer = range1.start.clone().subtract(bufferMinutes, 'minutes')
      const range1EndWithBuffer = range1.end.clone().add(bufferMinutes, 'minutes')
      const range2StartWithBuffer = range2.start.clone().subtract(bufferMinutes, 'minutes')
      const range2EndWithBuffer = range2.end.clone().add(bufferMinutes, 'minutes')

      const overlaps = range1StartWithBuffer.isBefore(range2EndWithBuffer) && range2StartWithBuffer.isBefore(range1EndWithBuffer)

      console.log(`重叠检测详情:`)
      console.log(`  工单1 ${task1.ordername}: ${range1.start.format('HH:mm')} - ${range1.end.format('HH:mm')}`)
      console.log(`  工单2 ${task2.ordername}: ${range2.start.format('HH:mm')} - ${range2.end.format('HH:mm')}`)
      console.log(`  重叠结果: ${overlaps}`)

      return overlaps
    },

    getTaskVisibleRangeForDate(task, date) {
      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const taskStartDate = taskStart.format('YYYY-MM-DD')
      const taskEndDate = taskEnd.format('YYYY-MM-DD')

      // 如果工单不在当前日期范围内，返回null
      if (date < taskStartDate || date > taskEndDate) {
        return null
      }

      // 计算在当前日期的可见时间范围
      const dayStart = moment(`${date} 00:00:00`)
      const dayEnd = moment(`${date} 23:59:59`)

      let visibleStart, visibleEnd

      if (date === taskStartDate && date === taskEndDate) {
        // 工单在同一天内
        visibleStart = taskStart
        visibleEnd = taskEnd
      } else if (date === taskStartDate) {
        // 当前日期是工单开始日期，延伸到当天结束
        visibleStart = taskStart
        visibleEnd = dayEnd
      } else if (date === taskEndDate) {
        // 修复：当前日期是工单结束日期，从当天开始到工单结束时间
        // 但是对于按天视图，我们需要考虑工单在结束日期的实际显示需求
        visibleStart = dayStart
        visibleEnd = taskEnd
      } else {
        // 当前日期在工单开始和结束之间，显示全天
        visibleStart = dayStart
        visibleEnd = dayEnd
      }

      // 调试输出
      console.log(`工单 ${task.ordername} 在 ${date} 的可见范围:`)
      console.log(`  原始时间: ${taskStart.format('YYYY-MM-DD HH:mm')} - ${taskEnd.format('YYYY-MM-DD HH:mm')}`)
      console.log(`  日期分类: 开始=${taskStartDate}, 当前=${date}, 结束=${taskEndDate}`)
      console.log(`  可见范围: ${visibleStart.format('HH:mm')} - ${visibleEnd.format('HH:mm')}`)

      return {
        start: visibleStart,
        end: visibleEnd
      }
    },

    getAllTasksForCurrentDay() {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const tasksForView = this.allTasksForView || this.dataSource

      return tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false

        const taskStartDate = moment(task.startTime).format('YYYY-MM-DD')
        const taskEndDate = moment(task.endTime).format('YYYY-MM-DD')

        // 检查任务是否在当前日期
        return currentDateStr >= taskStartDate && currentDateStr <= taskEndDate
      })
    },

    findOverlappingTasks(currentTask, allTasks) {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const overlapping = []

      allTasks.forEach(task => {
        if (this.tasksOverlapOnDate(currentTask, task, currentDateStr)) {
          overlapping.push(task.id)
        }
      })

      return overlapping.sort() // 排序确保一致的列分配
    },

    getTaskVisibleRange(task) {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const taskStartDate = taskStart.format('YYYY-MM-DD')
      const taskEndDate = taskEnd.format('YYYY-MM-DD')
      const dayStart = moment(`${currentDateStr} 01:00:00`) // 从1:00开始
      const dayEnd = moment(`${currentDateStr} 23:59:59`)   // 到23:59结束

      let visibleStart, visibleEnd

      if (currentDateStr === taskStartDate && currentDateStr === taskEndDate) {
        visibleStart = moment.max(taskStart, dayStart)
        visibleEnd = moment.min(taskEnd, dayEnd)
      } else if (currentDateStr === taskStartDate) {
        visibleStart = moment.max(taskStart, dayStart)
        visibleEnd = dayEnd
      } else if (currentDateStr === taskEndDate) {
        visibleStart = dayStart
        visibleEnd = moment.min(taskEnd, dayEnd)
      } else if (currentDateStr > taskStartDate && currentDateStr < taskEndDate) {
        visibleStart = dayStart
        visibleEnd = dayEnd
      } else {
        return null
      }

      // 如果可见时间范围无效，返回null
      if (visibleStart.isAfter(visibleEnd)) {
        return null
      }

      return { start: visibleStart, end: visibleEnd }
    },

    timeRangesOverlap(range1, range2) {
      // 检查两个时间范围是否重叠
      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)
    },

    tasksOverlapOnDate(task1, task2, dateStr) {
      // 计算两个工单在指定日期的时间范围
      const getVisibleRange = (task) => {
        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)
        const taskStartDate = taskStart.format('YYYY-MM-DD')
        const taskEndDate = taskEnd.format('YYYY-MM-DD')

        if (dateStr === taskStartDate && dateStr === taskEndDate) {
          return { start: taskStart, end: taskEnd }
        } else if (dateStr === taskStartDate) {
          return { start: taskStart, end: moment(`${dateStr} 23:59:59`) }
        } else if (dateStr === taskEndDate) {
          return { start: moment(`${dateStr} 00:00:00`), end: taskEnd }
        } else if (dateStr > taskStartDate && dateStr < taskEndDate) {
          return { start: moment(`${dateStr} 00:00:00`), end: moment(`${dateStr} 23:59:59`) }
        }
        return null
      }

      const range1 = getVisibleRange(task1)
      const range2 = getVisibleRange(task2)

      if (!range1 || !range2) return false

      // 检查时间范围是否重叠
      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)
    },

    getTaskCardStyle(task) {
      if (!task.startTime || !task.endTime) return {}

      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const currentDate = this.currentDay.format('YYYY-MM-DD')
      const taskStartDate = taskStart.format('YYYY-MM-DD')
      const taskEndDate = taskEnd.format('YYYY-MM-DD')

      // 只处理当天的工单（跨日期工单已在上层过滤）
      if (taskStartDate !== taskEndDate || currentDate !== taskStartDate) {
        return { display: 'none' }
      }

      const startHour = taskStart.hour()
      const startMinute = taskStart.minute()
      const endHour = taskEnd.hour()
      const endMinute = taskEnd.minute()

      // 计算在120px高度时间槽中的精确位置
      const topOffsetPx = (startMinute / 60) * 120 // 使用120px高度

      // 计算持续时间（分钟）
      const durationMinutes = taskEnd.diff(taskStart, 'minutes')

      // 计算基础高度
      let baseHeightPx
      if (startHour === endHour) {
        // 同一小时内的工单
        baseHeightPx = Math.max((durationMinutes / 60) * 120, 120) // 最小一个时间槽高度
      } else {
        // 跨小时的工单
        baseHeightPx = (durationMinutes / 60) * 120
      }

      // 根据内容动态调整高度
      const contentHeight = this.calculateContentHeight(task)
      const heightPx = Math.max(baseHeightPx, contentHeight)

      // 计算多列布局，从最左侧开始
      const totalColumns = task.totalColumns || 1
      const columnIndex = task.columnIndex || 0

      // 优化列宽计算，确保内容完整显示
      const containerWidth = 100 // 容器总宽度百分比
      const columnGap = 2 // 列间距百分比
      const totalGapWidth = (totalColumns - 1) * columnGap
      const availableWidth = containerWidth - totalGapWidth
      const columnWidth = availableWidth / totalColumns

      // 从最左侧开始排列（0%开始）
      const leftOffset = columnIndex * (columnWidth + columnGap)

      const finalStyle = {
        top: `${topOffsetPx}px`,
        height: `${heightPx}px`,
        position: 'absolute',
        width: `${Math.max(columnWidth, 20)}%`, // 确保最小宽度
        left: `${leftOffset}%`, // 从最左侧开始
        zIndex: 10001 + columnIndex,
        overflow: 'visible', // 改为visible，允许内容自适应
        minHeight: `${contentHeight}px` // 动态最小高度
      }

      return finalStyle
    },

    // 新增：计算内容所需高度
    calculateContentHeight(task) {
      // 基础内容高度计算（根据实际CSS样式）
      const titleHeight = 22 // 标题行高度（14px字体 * 1.3行高 + margin）
      const metaItemHeight = 24 // 每个meta项高度（11px字体 + 6px padding * 2 + line-height）
      const statusHeight = 22 // 状态标签高度（10px字体 + 6px padding * 2 + line-height）
      const cardPadding = 32 // 卡片上下内边距总和（16px * 2）
      const gaps = 16 // 元素间隙总和（8px * 2）

      // 计算meta项数量（处理人 + 时间）
      const metaItems = 2
      const metaHeight = metaItems * metaItemHeight + (metaItems - 1) * 6 // 6px gap between items

      // 总内容高度
      const totalContentHeight = titleHeight + metaHeight + statusHeight + cardPadding + gaps

      // 确保最小高度能显示完整内容，并且不小于120px（一个时间槽高度）
      return Math.max(totalContentHeight, 120)
    },

    // 获取当前日期的跨日期工单
    getCrossDayTasksForCurrentDay() {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const tasksForView = this.allTasksForView || this.dataSource

      return tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false

        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)
        const taskStartDate = taskStart.format('YYYY-MM-DD')
        const taskEndDate = taskEnd.format('YYYY-MM-DD')

        // 检查是否为跨日期工单且在当前日期范围内
        const isCrossDay = taskStartDate !== taskEndDate
        const isInCurrentDate = currentDateStr >= taskStartDate && currentDateStr <= taskEndDate

        return isCrossDay && isInCurrentDate
      })
    },

    // 获取当前日期的当天工单（非跨日期）
    getCurrentDayTasksForHour(hour) {
      const currentDateStr = this.currentDay.format('YYYY-MM-DD')
      const tasksForView = this.allTasksForView || this.dataSource

      const tasks = tasksForView.filter(task => {
        if (!task.startTime || !task.endTime) return false

        const taskStart = moment(task.startTime)
        const taskEnd = moment(task.endTime)
        const taskStartDate = taskStart.format('YYYY-MM-DD')
        const taskEndDate = taskEnd.format('YYYY-MM-DD')

        // 只显示当天工单（非跨日期）
        const isSameDay = taskStartDate === taskEndDate && taskStartDate === currentDateStr
        if (!isSameDay) return false

        // 修复：只在工单开始的小时槽显示，避免重复
        const startHour = taskStart.hour()
        const hourNum = parseInt(hour)

        return hourNum === startHour
      })

      return this.assignTaskColumns(tasks, hour)
    },

    // 格式化跨日期工单的时间显示
    formatCrossDayTaskTime(task) {
      const currentDate = this.currentDay.format('YYYY-MM-DD')
      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const taskStartDate = taskStart.format('YYYY-MM-DD')
      const taskEndDate = taskEnd.format('YYYY-MM-DD')

      if (currentDate === taskStartDate && currentDate === taskEndDate) {
        // 同一天（不应该出现在这里）
        return `${taskStart.format('HH:mm')} - ${taskEnd.format('HH:mm')}`
      } else if (currentDate === taskStartDate) {
        // 开始日期
        return `${taskStart.format('HH:mm')} - 次日`
      } else if (currentDate === taskEndDate) {
        // 结束日期
        return `前日 - ${taskEnd.format('HH:mm')}`
      } else {
        // 中间日期
        return `全天`
      }
    },

    // 计算跨日期工单的持续时间
    getCrossDayTaskDuration(task) {
      const taskStart = moment(task.startTime)
      const taskEnd = moment(task.endTime)
      const duration = moment.duration(taskEnd.diff(taskStart))

      const days = Math.floor(duration.asDays())
      const hours = duration.hours()
      const minutes = duration.minutes()

      if (days > 0) {
        return `${days}天${hours}小时`
      } else if (hours > 0) {
        return `${hours}小时${minutes}分钟`
      } else {
        return `${minutes}分钟`
      }
    },

    // 计算跨日期工单在时间轴上的样式
    getCrossDayTaskStyle(task, index) {
      const totalTasks = this.getCrossDayTasksForCurrentDay().length
      const taskWidth = Math.min(95 / totalTasks, 25) // 最大宽度25%，根据工单数量调整
      const leftOffset = index * (taskWidth + 1.5) // 1.5%间距，增加间距

      return {
        position: 'relative',
        width: `${taskWidth}%`,
        minWidth: '200px',
        marginRight: '12px',
        marginBottom: '8px',
        display: 'inline-block',
        verticalAlign: 'top',
        zIndex: 100 + index
      }
    },

    // 获取工单颜色类名
    getTaskColorClass(task, index = 0) {
      // 基于工单状态和索引生成颜色类
      const colorClasses = [
        'task-color-blue',
        'task-color-purple',
        'task-color-green',
        'task-color-orange',
        'task-color-pink',
        'task-color-cyan',
        'task-color-red',
        'task-color-indigo'
      ]

      // 优先根据状态分配颜色
      const statusColors = {
        'pending': 'task-color-orange',
        'working': 'task-color-blue',
        'completed': 'task-color-purple',
        'approved': 'task-color-green',
        'rejected': 'task-color-red'
      }

      // 如果有状态对应的颜色，使用状态颜色，否则使用循环颜色
      return statusColors[task.workStatus] || colorClasses[index % colorClasses.length]
    },



    formatTaskTime(task) {
      if (!task.startTime || !task.endTime) return ''
      const start = moment(task.startTime).format('HH:mm')
      const end = moment(task.endTime).format('HH:mm')
      const startDate = moment(task.startTime).format('MM-DD')
      const endDate = moment(task.endTime).format('MM-DD')

      // 如果跨天，显示日期
      if (startDate !== endDate) {
        return `${startDate} ${start} - ${endDate} ${end}`
      }
      return `${start}-${end}`
    },



    // 原有方法保持不变
    searchQuery(){
      this.loadTasks();
    },

    searchReset(){
      this.queryParam = {
        workStatus: []
      }
      this.ipagination.current = 1;
    },

    audit(id, type){
      if(type === '1'){
        Modal.confirm({
          title: '审核确认',
          icon: '',
          content: '确定要通过此条工单吗？',
          onOk: () => {
            this.updateWorkStatus(id, 'approved');
          }
        })
      }else if(type === '2'){
        this.isShowAudit = true;
        this.currentId = id;
      }else if(type === '3'){
        if(this.auditOpinion.length === 0){
          this.$message.error("请输入审核意见！");
          return;
        }
        this.updateWorkStatus(this.currentId, 'rejected');
      }
    },

    updateWorkStatus(id, status) {
      let params = {}
      params.id = id
      params.workStatus = status
      if(status === 'rejected'){
        params.reviewComment = this.auditOpinion;
      }
      putAction('/WorkOrderTask/edit', params)
        .then(res => {
          if (res.success) {
            this.$message.success('操作成功！')
            this.loadTasks()
            // 如果当前不是表格视图，也要重新加载数据
            if (this.currentView !== 'table') {
              this.loadAllTasksForView()
            }
            this.isShowAudit = false;
          } else {
            this.$message.error('操作失败！')
          }
        })
    },

    handleTableChange(current, pageSize) {
      this.ipagination = current
      this.loadTasks()
    },

    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },

    getWorkStatusText(status) {
      return this.statusMap[status] || '待处理'
    },

    getProgressStageText(stage) {
      return this.progressStageMap[stage] || '未开始'
    },

    switchTab(e) {
      this.dataSource = []
      this.loading = true
      this.activeTab = e

      // 如果切换到不支持多视图的标签页，强制切换回表格视图
      if (e === 'pool' || e === 'audit') {
        this.currentView = 'table'
      }

      this.searchReset();
      this.loadTasks()

      // 如果当前不是表格视图，也要重新加载数据
      if (this.currentView !== 'table') {
        // 延迟加载，确保标签切换完成
        this.$nextTick(() => {
          this.loadAllTasksForView()
        })
      }
    },
    loadProjectInfo() {
      getAction(`/project/queryById`, { id: this.projectId }).then(res => {
        if (res.success) {
          this.projectInfo = res.result
        }
      })
    },

    loadProjectMember() {
      getAction(`/projectMember/list`, { projectId: this.projectId }).then(res => {
        if (res.success) {
          this.projectMemberList = res.result;
          this.projectMemberList.forEach(item => {
            if(item.user_id === this.currentUserId){
              this.userRole = item.role;
              return;
            }
          })
        }
      })
    },

    loadTasks() {
      this.loading = true;
      let params = {...this.queryParam};
      
      // 将状态数组转换为逗号分隔的字符串
      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {
        params.workStatus = params.workStatus.join(',');
      }
      
      params.projectId = this.projectId;
      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));
      params.pageNo = this.ipagination.current;
      params.pageSize = this.ipagination.pageSize;
      this.$http.get('/WorkOrderTask/list', {
        params: params
      }).then(res => {
        if (res.success) {
          this.dataSource = res.result.data
          let numJSON = res.result.data2
          this.poolTotal = numJSON.poolTotal
          this.myTotal = numJSON.myTotal
          this.allTotal = numJSON.allTotal
          this.auditTotal = numJSON.auditTotal
          this.ipagination.total = res.result.total
        } else {
          this.dataSource = []
        }
        this.loading = false
      })
    },

    handleAddTask() {
      this.$refs.taskModal.add()
    },

    taskModalOk() {
      this.loadTasks()
      // 如果当前不是表格视图，也要重新加载数据
      if (this.currentView !== 'table') {
        this.loadAllTasksForView()
      }
    },

    claimTask(task) {
      this.$confirm({
        title: '确认领取',
        content: `确定要领取工单"${task.ordername}"吗？`,
        onOk: () => {
          this.$http.post('/WorkOrderTask/claim', {
            id: task.id
          }).then(res => {
            if (res.success) {
              this.$message.success('领取成功')
              this.loadTasks()
              if (this.currentView !== 'table') {
                this.loadAllTasksForView()
              }
            } else {
              this.$message.error('领取失败')
            }
          })
        }
      })
    },

    startWork(task) {
      this.$http.post('/WorkOrderTask/startWork', {
        id: task.id,
        startTime: new Date()
      }).then(res => {
        if (res.success) {
          this.$message.success('工单已开始')
          task.workStatus = 'working'
          task.actualStartTime = new Date()
          this.startWorkingTimer(task)
        } else {
          this.$message.error('开始失败')
        }
      })
    },

    showFinishModal(task) {
      this.currentTask = task;
      this.finishForm = {
        fileList: [],
        finishDescribe: '',
        finishAnnex: ''
      };
      this.finishModalVisible = true;
    },
    
    // 处理拖拽上传
    handleDrop(e) {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      files.forEach(file => {
        this.uploadFile(file);
      });
    },
    
    // 上传前验证
    beforeUpload(file) {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        this.$message.error('文件必须小于 100MB!');
        return false;
      }
      return true;
    },
    
    // 处理上传变更
    handleFileChange(info) {
      let fileList = [...info.fileList];
      
      // 限制最多5个文件
      fileList = fileList.slice(-5);
      
      // 更新文件状态
      fileList = fileList.map(file => {
        if (file.response) {
          file.url = file.response.message;
        }
        return file;
      });
      
      this.finishForm.fileList = fileList;
      
      // 将文件URL拼接成字符串保存到finishAnnex
      if (fileList.length > 0) {
        this.finishForm.finishAnnex = fileList
          .filter(file => file.status === 'done' && file.response)
          .map(file => file.response.message)
          .join(',');
      } else {
        this.finishForm.finishAnnex = '';
      }
    },
    
    // 完成工单提交
    handleFinishOk() {
      if (!this.currentTask) return;
      
      this.finishConfirmLoading = true;
      const params = {
        id: this.currentTask.id,
        workStatus: 'completed'
      };
      
      // 添加附件和说明字段
      if (this.finishForm.finishAnnex) {
        params.finishAnnex = this.finishForm.finishAnnex;
      }
      
      if (this.finishForm.finishDescribe) {
        params.finishDescribe = this.finishForm.finishDescribe;
      }

      putAction('/WorkOrderTask/edit', params)
        .then(res => {
          if (res.success) {
            this.$message.success('工单已结束，等待审核！')
            this.finishModalVisible = false;
            this.loadTasks()
            // 如果当前不是表格视图，也要重新加载数据
            if (this.currentView !== 'table') {
              this.loadAllTasksForView()
            }
            this.isShowAudit = false;
          } else {
            this.$message.error(res.message || '结束失败')
          }
        }).finally(() => {
            this.finishConfirmLoading = false;
      });
      
      // httpAction('/WorkOrderTask/endWork', params, 'post').then(res => {
      //   if (res.success) {
      //     this.$message.success('工单已结束，等待审核');
      //     this.currentTask.workStatus = 'completed';
      //     this.currentTask.actualEndTime = endTime;
      //     this.currentTask.actualHours = workingHours;
      //     this.clearWorkingTimer(this.currentTask.id);
      //     this.finishModalVisible = false;
      //     this.loadTasks();
      //   } else {
      //     this.$message.error(res.message || '结束失败');
      //   }
      // }).finally(() => {
      //   this.finishConfirmLoading = false;
      // });
    },

    viewTaskDetail(task) {
      this.$refs.taskDetailModal.show(task)
    },

    taskEdit(task) {
      let data = JSON.parse(JSON.stringify(task));
      data.handling = data.handlingId;
      this.$refs.taskModal.show(data);
    },

    taskEdit2(task) {
      let data = JSON.parse(JSON.stringify(task));
      data.handling = data.handlingId;
      this.$refs.taskModal.show2(data);
    },

    viewProjectStats() {
      this.$refs.projectStatsModal.show(this.projectInfo)
    },

    goBack() {
      this.$router.go(-1)
    },

    startWorkingTimers() {
      this.myTasks.forEach(task => {
        if (task.workStatus === 'working' && task.actualStartTime) {
          this.startWorkingTimer(task)
        }
      })
    },

    startWorkingTimer(task) {
      const timer = setInterval(() => {
        this.$forceUpdate()
      }, 1000)
      this.workingTimers.set(task.id, timer)
    },

    clearWorkingTimer(taskId) {
      const timer = this.workingTimers.get(taskId)
      if (timer) {
        clearInterval(timer)
        this.workingTimers.delete(taskId)
      }
    },

    clearWorkingTimers() {
      this.workingTimers.forEach(timer => clearInterval(timer))
      this.workingTimers.clear()
    },

    calculateWorkingHours(startTime, endTime) {
      const start = moment(startTime)
      const end = moment(endTime)
      return Math.round(end.diff(start, 'hours', true) * 100) / 100
    },

    getWorkingDuration(startTime) {
      const start = moment(startTime)
      const now = moment()
      const duration = moment.duration(now.diff(start))
      const hours = Math.floor(duration.asHours())
      const minutes = duration.minutes()
      return `${hours}h ${minutes}m`
    },

    getStatusText(status) {
      const statusMap = {
        '0': '未开始',
        '1': '进行中',
        '2': '已完成',
        '3': '已暂停'
      }
      return statusMap[status] || '未知'
    },

    getStatusClass(status) {
      const classMap = {
        '0': 'status-pending',
        '1': 'status-active',
        '2': 'status-completed',
        '3': 'status-paused'
      }
      return classMap[status] || 'status-pending'
    },

    getModeText(mode) {
      return mode === 'hours' ? '工时模式' : '排期模式'
    },

    getPriorityText(priority) {
      const priorityMap = {
        '2': '高',
        '1': '中',
        '0': '低'
      }
      return priorityMap[priority] || '中'
    },

    getPriorityClass(priority) {
      const classMap = {
        '2': 'priority-high',
        '1': 'priority-medium',
        '0': 'priority-low'
      }
      return classMap[priority] || 'priority-medium'
    },

    getTaskStatusText(task) {
      const statusMap = {
        'pending': '待开始',
        'working': '进行中',
        'completed': '待审核',
        'approved': '已完成',
        'rejected': '已驳回'
      }
      return statusMap[task.workStatus] || '未知'
    },

    getTaskStatusClass(task) {
      const classMap = {
        'pending': 'task-pending',
        'working': 'task-working',
        'completed': 'task-completed',
        'approved': 'task-approved',
        'rejected': 'task-rejected'
      }
      return classMap[task.workStatus] || 'task-pending'
    },

    formatDate(date) {
      if (!date) return '未设置'
      return moment(date).format('MM-DD HH:mm')
    }
  }
}
</script>

<style lang="less" scoped>
// 基础样式保持不变
.project-detail-app {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

// 多选框换行显示样式
:deep(.multi-select-wrap) {
  .ant-select-selection--multiple {
    .ant-select-selection__rendered {
      height: auto;
      max-height: none;
      overflow-y: auto;

      // 使标签能够换行显示
      ul {
        display: flex;
        flex-wrap: wrap;
      }

      .ant-select-selection__choice {
        margin-top: 4px;
      }
    }
  }
}

// 项目头部样式保持不变...
.project-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;
  }

  .project-info {
    flex: 1;

    .back-btn {
      background: none;
      border: none;
      color: #5a67d8;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 16px;
      padding: 8px 16px;
      border-radius: 10px;
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 10px;
        background: linear-gradient(135deg, rgba(90, 103, 216, 0.1), rgba(79, 172, 254, 0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        color: #4c51bf;
        transform: translateX(-3px);

        &::before {
          opacity: 1;
        }
      }
    }

    .project-title {
      margin: 0 0 16px 0;
      font-size: 32px;
      font-weight: 700;
      color: #2d3748;
      line-height: 1.2;
      letter-spacing: -0.025em;
    }

    .project-meta {
      display: flex;
      gap: 12px;
      align-items: center;

      .project-mode {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        letter-spacing: 0.025em;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .btn-primary, .btn-secondary {
      padding: 12px 24px;
      border: none;
      border-radius: 14px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
      position: relative;
      overflow: hidden;
      letter-spacing: 0.025em;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.25);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
      }

      &:active::before {
        width: 300px;
        height: 300px;
      }
    }

    .btn-primary {
      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
      color: white;
      box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(76, 81, 191, 0.5);
      }
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.9);
      color: #4a5568;
      border: 1px solid rgba(74, 85, 104, 0.15);
      backdrop-filter: blur(10px);

      &:hover {
        background: white;
        border-color: rgba(74, 85, 104, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 筛选区域样式保持不变...
.filter-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;

  .filter-content {
    padding: 28px 32px;

    .filter-row {
      display: flex;
      align-items: flex-end;
      gap: 24px;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 200px;

      label {
        font-size: 14px;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 4px;
        letter-spacing: 0.025em;
      }
    }

    .filter-actions {
      display: flex;
      gap: 12px;
      margin-top: 32px;

      .btn-primary, .btn-ghost {
        padding: 12px 24px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        letter-spacing: 0.025em;
      }

      .btn-primary {
        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
        color: white;
        border: none;
        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);
        }
      }

      .btn-ghost {
        background: transparent;
        color: #718096;
        border: 1.5px solid #e2e8f0;

        &:hover {
          background: #f7fafc;
          border-color: #cbd5e0;
          color: #4a5568;
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 优化标签页容器
.tabs-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;

  .tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    padding: 0 20px 0 0;
    position: relative;

    .tabs-left {
      display: flex;
      flex: 1;

      .tab-btn {
        flex: 1;
        padding: 20px 24px;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 15px;
        font-weight: 600;
        color: #64748b;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        position: relative;
        letter-spacing: 0.025em;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover::before {
          opacity: 1;
        }

        &.active {
          color: #4c51bf;
          background: rgba(255, 255, 255, 0.95);
          font-weight: 700;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(76, 81, 191, 0.3);
          }
        }

        .tab-count {
          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
          color: white;
          padding: 4px 10px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 700;
          min-width: 22px;
          text-align: center;
          box-shadow: 0 3px 10px rgba(76, 81, 191, 0.3);
          letter-spacing: 0;
        }
      }
    }

    // 新增视图切换器
    .view-switcher {
      display: flex;
      gap: 4px;
      background: rgba(255, 255, 255, 0.8);
      padding: 6px;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

      .view-btn {
        padding: 10px 12px;
        border: none;
        background: transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        font-size: 16px;

        &:hover {
          background: rgba(76, 81, 191, 0.1);
          color: #4c51bf;
          transform: scale(1.05);
        }

        &.active {
          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
          color: white;
          box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);
          transform: scale(1.05);
        }
      }
    }
  }
}

// 表格容器保持原样...
.table-container {
  padding: 28px 32px 32px;

  .table-loading {
    .ant-spin-container {
      transition: all 0.4s ease;
    }

    &.ant-spin-spinning .ant-spin-container {
      opacity: 0.5;
      filter: blur(2px);
    }
  }
}

// 月历视图样式
.calendar-container {
  padding: 28px 32px 32px;
  min-height: 600px;

  .calendar-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-bottom: 28px;

    .calendar-nav-btn {
      width: 44px;
      height: 44px;
      border: none;
      border-radius: 12px;
      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
      box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);
      }
    }

    .calendar-title {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin: 0;
      min-width: 180px;
      text-align: center;
    }
  }

  .calendar-grid {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);

    .calendar-weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 2px solid #e2e8f0;

      .weekday {
        padding: 16px;
        text-align: center;
        font-weight: 700;
        color: #374151;
        font-size: 14px;
        letter-spacing: 0.025em;
      }
    }

    .calendar-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);

      .calendar-day {
        min-height: 120px;
        border-right: 1px solid #f1f5f9;
        border-bottom: 1px solid #f1f5f9;
        padding: 12px;
        transition: all 0.3s ease;
        position: relative;

        &:nth-child(7n) {
          border-right: none;
        }

        &:hover {
          background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
        }

        &.other-month {
          background: #fafafa;
          color: #cbd5e0;
        }

        &.today {
          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);

          .day-number {
            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
          }
        }

        &.has-tasks {
          .day-number {
            font-weight: 700;
            color: #4c51bf;
          }
        }

        .day-number {
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          margin-bottom: 8px;
        }

        .day-tasks {
          .task-item {
            background: linear-gradient(135deg, rgba(76, 81, 191, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
            border-left: 3px solid #4c51bf;
            padding: 4px 8px;
            margin-bottom: 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;

            &:hover {
              background: linear-gradient(135deg, rgba(76, 81, 191, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);
              transform: translateX(2px);
            }

            &.status-pending {
              border-left-color: #ed8936;
              background: rgba(237, 137, 54, 0.1);
            }

            &.status-working {
              border-left-color: #4299e1;
              background: rgba(66, 153, 225, 0.1);
            }

            &.status-completed {
              border-left-color: #9f7aea;
              background: rgba(159, 122, 234, 0.1);
            }

            &.status-approved {
              border-left-color: #48bb78;
              background: rgba(72, 187, 120, 0.1);
            }

            &.status-rejected {
              border-left-color: #f56565;
              background: rgba(245, 101, 101, 0.1);
            }

            .task-name {
              display: block;
              font-weight: 600;
              color: #374151;
              line-height: 1.2;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .task-handler {
              display: block;
              color: #718096;
              font-size: 10px;
              margin-top: 2px;
            }
          }

          .more-tasks {
            color: #4c51bf;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(76, 81, 191, 0.1);
            }
          }
        }
      }
    }
  }
}

// 甘特图视图样式
.gantt-container {
  padding: 28px 32px 32px;

  .gantt-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 28px;

    .gantt-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .gantt-nav-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 10px;
        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);
        }
      }

      .gantt-period {
        font-size: 18px;
        font-weight: 700;
        color: #2d3748;
        min-width: 200px;
        text-align: center;
      }
    }
  }

  .gantt-content {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);

    .gantt-timeline {
      .timeline-header {
        display: flex;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 2px solid #e2e8f0;

        .task-header {
          width: 300px;
          padding: 20px 24px;
          font-weight: 700;
          color: #374151;
          font-size: 14px;
          letter-spacing: 0.025em;
          border-right: 2px solid #e2e8f0;
          display: flex;
          align-items: center;
        }

        .dates-header {
          flex: 1;
          display: flex;

          .date-cell {
            flex: 1;
            padding: 12px 8px;
            text-align: center;
            border-right: 1px solid #f1f5f9;
            transition: all 0.3s ease;

            &.today {
              background: rgba(76, 81, 191, 0.1);

              .date-day {
                color: #4c51bf;
                font-weight: 700;
              }
            }

            .date-day {
              font-size: 14px;
              font-weight: 600;
              color: #374151;
              line-height: 1.2;
            }

            .date-weekday {
              font-size: 10px;
              color: #718096;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-top: 2px;
            }
          }
        }
      }

      .timeline-body {
        .gantt-row {
          display: flex;
          border-bottom: 1px solid #f1f5f9;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
          }

          .task-info {
            width: 300px;
            padding: 20px 24px;
            border-right: 1px solid #f1f5f9;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .task-name {
              font-size: 14px;
              font-weight: 600;
              color: #374151;
              margin-bottom: 6px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .task-meta {
              display: flex;
              gap: 12px;
              align-items: center;

              .task-handler {
                font-size: 12px;
                color: #718096;
              }

              .task-status {
                padding: 4px 8px;
                border-radius: 8px;
                font-size: 10px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                &.status-pending {
                  background: rgba(237, 137, 54, 0.1);
                  color: #dd6b20;
                }

                &.status-working {
                  background: rgba(66, 153, 225, 0.1);
                  color: #3182ce;
                }

                &.status-completed {
                  background: rgba(159, 122, 234, 0.1);
                  color: #805ad5;
                }

                &.status-approved {
                  background: rgba(72, 187, 120, 0.1);
                  color: #38a169;
                }

                &.status-rejected {
                  background: rgba(245, 101, 101, 0.1);
                  color: #e53e3e;
                }
              }
            }
          }

          .gantt-bars {
            flex: 1;
            padding: 16px 8px;
            position: relative;
            min-height: 80px;

            .gantt-bar {
              position: absolute;
              height: 28px;
              top: 50%;
              transform: translateY(-50%);
              border-radius: 14px;
              cursor: pointer;
              transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

              &:hover {
                transform: translateY(-50%) scale(1.02);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                z-index: 10;
              }

              &.status-pending {
                background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
              }

              &.status-working {
                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
              }

              &.status-completed {
                background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
              }

              &.status-approved {
                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
              }

              &.status-rejected {
                background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
              }

              .bar-content {
                height: 100%;
                display: flex;
                align-items: center;
                padding: 0 12px;

                .bar-text {
                  color: white;
                  font-size: 12px;
                  font-weight: 600;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                }
              }
            }
          }
        }
      }
    }
  }
}

// 按天视图样式
.daily-container {
  padding: 28px 32px 32px;
  min-height: 600px;

  .daily-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 28px;

    .daily-controls {
      display: flex;
      align-items: center;
      gap: 24px;

      .daily-nav-btn {
        width: 44px;
        height: 44px;
        border: none;
        border-radius: 12px;
        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);
        }
      }

      .date-picker-wrapper {
        .daily-date-picker {
          /deep/ .ant-calendar-picker-input {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 20px;
            min-width: 200px;
            transition: all 0.3s ease;

            &:hover, &:focus {
              border-color: #4c51bf;
              box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);
            }
          }
        }
      }
    }
  }

  // 跨日期工单展示区域
  .cross-date-section {
    margin-bottom: 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);

    .cross-date-header {
      margin-bottom: 20px;

      .cross-date-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;

        .title-icon {
          font-size: 24px;
        }

        .task-count {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
          margin-left: auto;
        }
      }
    }

    .cross-date-timeline {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: flex-start;
    }

    .cross-date-task {
      border-radius: 16px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border: none;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        filter: brightness(1.1);
        z-index: -1;
      }
    }

    .cross-task-content {
      position: relative;
      z-index: 1;
    }

    .cross-task-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      gap: 12px;
    }

    .cross-task-title {
      font-size: 16px;
      font-weight: 700;
      color: white;
      line-height: 1.4;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      flex: 1;
    }

    .cross-task-status {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      background: rgba(255, 255, 255, 0.9);
      color: #1e293b;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .cross-task-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .cross-task-handler,
    .cross-task-time,
    .cross-task-duration {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      color: white;
      background: rgba(255, 255, 255, 0.15);
      padding: 6px 12px;
      border-radius: 10px;
      font-weight: 500;
      backdrop-filter: blur(10px);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

      i {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }

  .daily-content {
    background: white;
    border-radius: 20px;
    overflow: visible;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);

    .timeline-container {
      position: relative;
      z-index: 1;
      overflow: visible;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 20px;

      .timeline-hours {
        position: relative;
        z-index: 2;
        overflow: visible;

        .hour-slot {
          position: relative;
          min-height: 120px; // 增加最小高度，为自适应工单提供更多空间
          height: auto; // 改为auto，允许高度自适应
          border-bottom: 2px solid #e2e8f0;
          display: block;
          overflow: visible; // 确保工单可以溢出时间槽
          z-index: auto;
          transition: all 0.2s ease;

          &:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
          }

          &:last-child {
            border-bottom: 2px solid #e2e8f0;
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
          }

          &:first-child {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
          }

          .hour-label {
            width: 90px;
            height: 120px; // 增加高度
            min-height: 120px;
            padding: 0;
            font-size: 14px;
            font-weight: 700;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-right: 3px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 10;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);

            .hour-text {
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
              letter-spacing: 0.5px;
            }
          }

          .hour-line {
            margin-left: 90px;
            min-height: 120px; // 增加高度
            height: auto; // 改为auto
            position: relative;
            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
            z-index: 1;

            .half-hour-line {
              position: absolute;
              top: 50%;
              left: 0;
              right: 0;
              height: 1px;
              background: linear-gradient(90deg, #d1d5db 0%, #e5e7eb 100%);
              z-index: 2;

              &::before {
                content: '';
                position: absolute;
                left: -90px;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, rgba(209, 213, 219, 0.5) 0%, #d1d5db 100%);
              }
            }

            .quarter-hour-line {
              position: absolute;
              left: 0;
              right: 0;
              height: 1px;
              background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);
              z-index: 2;
              opacity: 0.6;
            }
          }
        }

        .task-slots {
          position: absolute;
          left: 90px;
          right: 0;
          top: 0;
          height: auto; // 改为auto，允许高度自适应
          min-height: 120px; // 更新最小高度
          padding: 0; // 移除padding，让工单从最左侧开始
          overflow: visible; // 确保内容可见
          z-index: 100;
          pointer-events: none;

          .task-card {
            border-radius: 12px;
            padding: 16px; // 增加内边距确保内容完整显示
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
            border: none;
            font-size: 12px;
            line-height: 1.4;
            overflow: visible; // 改为visible，允许内容自适应
            position: absolute;
            z-index: 1000;
            pointer-events: auto;
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            backdrop-filter: blur(10px);
            border-left: 4px solid;
            min-height: 120px; // 增加最小高度确保内容显示
            display: flex;
            flex-direction: column;
            justify-content: flex-start; // 改为flex-start，让内容从顶部开始
            height: auto; // 允许高度自适应

            &:hover {
              transform: translateY(-2px) scale(1.02);
              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 8px 15px rgba(0, 0, 0, 0.15);
              z-index: 1001;
            }

            // 多彩工单颜色方案
            &.task-color-blue {
              background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 1) 100%);
              border-left-color: #1d4ed8;
              color: white;
              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            }

            &.task-color-purple {
              background: linear-gradient(135deg, rgba(147, 51, 234, 0.95) 0%, rgba(126, 34, 206, 1) 100%);
              border-left-color: #6b21a8;
              color: white;
              box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
            }

            &.task-color-green {
              background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(21, 128, 61, 1) 100%);
              border-left-color: #14532d;
              color: white;
              box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
            }

            &.task-color-orange {
              background: linear-gradient(135deg, rgba(249, 115, 22, 0.95) 0%, rgba(234, 88, 12, 1) 100%);
              border-left-color: #9a3412;
              color: white;
              box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
            }

            &.task-color-pink {
              background: linear-gradient(135deg, rgba(236, 72, 153, 0.95) 0%, rgba(219, 39, 119, 1) 100%);
              border-left-color: #831843;
              color: white;
              box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);
            }

            &.task-color-cyan {
              background: linear-gradient(135deg, rgba(6, 182, 212, 0.95) 0%, rgba(8, 145, 178, 1) 100%);
              border-left-color: #164e63;
              color: white;
              box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
            }

            &.task-color-red {
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 1) 100%);
              border-left-color: #7f1d1d;
              color: white;
              box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
            }

            &.task-color-indigo {
              background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(79, 70, 229, 1) 100%);
              border-left-color: #312e81;
              color: white;
              box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
            }
          }
        }

      // 工单卡片内容样式 - 自适应高度显示
      .task-card-content {
        position: relative;
        z-index: 1;
        width: 100%;
        height: auto; // 改为auto，允许高度自适应
        min-height: 100%; // 确保至少填满容器
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 从顶部开始排列
        gap: 8px; // 统一间距
        padding: 0; // 移除额外padding，使用卡片的padding

        .task-title {
          font-size: 14px; // 增大字体
          font-weight: 700;
          margin-bottom: 0; // 移除margin，使用gap
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.3; // 增加行高
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.2px;
          flex-shrink: 0; // 防止标题被压缩
          min-height: 20px; // 确保最小高度
        }

        .task-meta {
          display: flex;
          flex-direction: column;
          gap: 6px; // 增加间距
          margin-bottom: 0; // 移除margin，使用gap
          font-size: 11px; // 增大字体
          flex-grow: 1; // 占据剩余空间

          .task-handler,
          .task-time {
            display: flex;
            align-items: center;
            gap: 6px; // 增加间距
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 10px; // 增加内边距
            border-radius: 8px; // 增大圆角
            backdrop-filter: blur(5px);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            line-height: 1.3; // 增加行高
            min-height: 18px; // 确保最小高度

            i {
              font-size: 12px; // 增大图标
              opacity: 0.9;
              flex-shrink: 0; // 防止图标被压缩
            }
          }
        }

        .task-status {
          font-size: 10px; // 增大字体
          font-weight: 600;
          padding: 6px 12px; // 增加内边距
          border-radius: 12px; // 增大圆角
          display: inline-block;
          line-height: 1.2; // 增加行高
          text-transform: uppercase;
          letter-spacing: 0.3px;
          background: rgba(255, 255, 255, 0.9);
          color: #1e293b;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(10px);
          align-self: flex-start; // 左对齐
          flex-shrink: 0; // 防止状态标签被压缩
          margin-top: auto; // 推到底部
          min-height: 16px; // 确保最小高度
        }
      }
      }
    }
  }
}

  // 跨日期工单颜色样式
  .cross-date-task {
    &.task-color-blue {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    &.task-color-purple {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }

    &.task-color-green {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    &.task-color-orange {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    &.task-color-pink {
      background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
    }

    &.task-color-cyan {
      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    }

    &.task-color-red {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    &.task-color-indigo {
      background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    }
  }

// 确保工单卡片始终在最上层的全局样式
.daily-container .task-card {
  z-index: 9999 !important;
  position: absolute !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;
}

// 日期详情弹窗样式
.day-tasks-modal {
  /deep/ .ant-modal-content {
    border-radius: 20px;
    overflow: hidden;
  }

  /deep/ .ant-modal-header {
    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
    border-bottom: none;

    .ant-modal-title {
      color: white;
      font-weight: 700;
    }
  }

  /deep/ .ant-modal-close {
    .ant-modal-close-x {
      color: white;
    }
  }

  .day-tasks-list {
    max-height: 400px;
    overflow-y: auto;

    .day-task-item {
      padding: 16px;
      border: 1px solid #f1f5f9;
      border-radius: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
        border-color: rgba(76, 81, 191, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }

      .task-main {
        .task-title {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #374151;
        }

        .task-info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .task-handler {
            color: #718096;
            font-size: 14px;
          }

          .task-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;

            &.status-pending {
              background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
              color: white;
            }

            &.status-working {
              background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
              color: white;
            }

            &.status-completed {
              background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
              color: white;
            }

            &.status-approved {
              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
              color: white;
            }

            &.status-rejected {
              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
              color: white;
            }
          }
        }

        .task-time {
          color: #4c51bf;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }
  }
}

// 其他样式保持不变...
.enhanced-table {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);

  /deep/ .ant-table {
    border-radius: 16px;
    overflow: hidden;

    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 2px solid #e2e8f0;
      font-weight: 700;
      font-size: 14px;
      color: #374151;
      padding: 20px 16px;
      text-align: center;
      letter-spacing: 0.025em;

      &:first-child {
        border-top-left-radius: 16px;
      }

      &:last-child {
        border-top-right-radius: 16px;
      }
    }

    .ant-table-tbody > tr {
      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);

      &:hover {
        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
        transform: scale(1.001);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
      }

      > td {
        padding: 18px 16px;
        border-bottom: 1px solid #f1f5f9;
        font-size: 14px;
        color: #374151;
        text-align: center;
      }
    }

    .ant-table-pagination {
      margin: 28px 0 0;
      text-align: center;

      .ant-pagination-item {
        border-radius: 10px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
        font-weight: 500;

        &:hover {
          border-color: #4c51bf;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(76, 81, 191, 0.15);
        }

        &.ant-pagination-item-active {
          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
          border-color: transparent;
          box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);

          a {
            color: white;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 优先级标签
.priority-tag {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.priority-0 {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);
  }

  &.priority-1 {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);
  }

  &.priority-2 {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);
  }
}

// 状态标签
.status-tag {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.status-pending {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);
  }

  &.status-working {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);
  }

  &.status-completed {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(159, 122, 234, 0.3);
  }

  &.status-approved {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);
  }

  &.status-rejected {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);
  }
}

// 工时显示
.work-hour {
  font-weight: 600;
  color: #4c51bf;
  background: rgba(76, 81, 191, 0.1);
  padding: 6px 10px;
  border-radius: 10px;
  font-size: 13px;
  letter-spacing: 0.025em;
}

// 时间范围显示
.time-range {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;

  .time-start, .time-end {
    font-weight: 600;
    color: #374151;
  }

  .time-divider {
    color: #9ca3af;
    font-size: 10px;
    text-align: center;
    font-weight: 500;
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;

  .action-link {
    padding: 6px 12px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &.claim {
      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(66, 153, 225, 0.3);
    }

    &.start, &.restart {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);
    }

    &.complete {
      background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(159, 122, 234, 0.3);
    }

    &.approve {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);
    }

    &.reject {
      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(245, 101, 101, 0.3);
    }

    &.edit {
      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(237, 137, 54, 0.3);
    }

    &.detail {
      background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
      color: white;
      box-shadow: 0 3px 12px rgba(113, 128, 150, 0.3);
    }

    &:hover {
      transform: translateY(-2px);
      filter: brightness(1.05);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .action-divider {
    height: 16px;
    margin: 0 4px;
    border-color: #e2e8f0;
  }
}

// Ant Design 组件样式覆盖
/deep/ .ant-select {
  .ant-select-selection {
    height: 44px;
    border: 1.5px solid #e2e8f0;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);

    &:hover {
      border-color: #4c51bf;
      box-shadow: 0 4px 20px rgba(76, 81, 191, 0.1);
    }

    &.ant-select-selection--focused {
      border-color: #4c51bf;
      box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);
    }

    .ant-select-selection__rendered {
      line-height: 40px;
      margin-left: 14px;
      margin-right: 14px;
      color: #374151;
      font-weight: 500;
    }

    .ant-select-arrow {
      right: 14px;
      color: #4c51bf;
    }
  }
}

/deep/ .ant-modal {
  .ant-modal-content {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .ant-modal-header {
    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
    border-bottom: none;
    padding: 24px 32px;

    .ant-modal-title {
      color: white;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 0.025em;
    }
  }

  .ant-modal-close {
    top: 24px;
    right: 32px;

    .ant-modal-close-x {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 32px;
    background: rgba(255, 255, 255, 0.98);
  }

  .ant-modal-footer {
    padding: 20px 32px 28px;
    text-align: center;
    border-top: 1px solid #f1f5f9;
    background: rgba(255, 255, 255, 0.98);

    .ant-btn {
      border-radius: 12px;
      font-weight: 600;
      padding: 10px 24px;
      height: auto;
      letter-spacing: 0.025em;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
        border: none;
        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .view-switcher {
    .view-btn {
      padding: 8px 10px;
      font-size: 14px;
    }
  }

  .gantt-container {
    .task-info {
      width: 250px;
    }
  }

  .calendar-container {
    .calendar-day {
      min-height: 100px;
    }
  }
}

@media (max-width: 768px) {
  .project-detail-app {
    padding: 16px;
  }

  .tabs-header {
    flex-direction: column;
    gap: 16px;
    padding: 20px;

    .tabs-left {
      order: 2;
    }

    .view-switcher {
      order: 1;
      justify-content: center;
    }
  }

  .calendar-container {
    .calendar-header {
      .calendar-title {
        font-size: 20px;
        min-width: 150px;
      }
    }

    .calendar-day {
      min-height: 80px;
      padding: 8px;
    }
  }

  .gantt-container {
    .gantt-content {
      overflow-x: auto;
    }

    .task-info {
      width: 200px;
    }
  }


}

@media (max-width: 480px) {
  .tabs-left {
    flex-direction: column;

    .tab-btn {
      padding: 12px 16px;
    }
  }

  .view-switcher {
    .view-btn {
      padding: 8px;
      font-size: 12px;
    }
  }

  .calendar-day {
    min-height: 60px;
    padding: 4px;

    .task-item {
      font-size: 10px;
      padding: 2px 4px;
    }
  }


}

// 完成工单模态框样式
.finish-modal {
  /deep/ .ant-modal-content {
    border-radius: 20px;
    overflow: hidden;
  }

  /deep/ .ant-modal-header {
    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
    border-bottom: none;

    .ant-modal-title {
      color: white;
      font-weight: 700;
    }
  }

  /deep/ .ant-modal-close {
    .ant-modal-close-x {
      color: white;
    }
  }
  
  .upload-container {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    transition: all 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
  }
  
  .upload-tips {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
  }
  
  .modal-footer {
    text-align: right;
    margin-top: 24px;
    
    .ant-btn {
      margin-left: 8px;
    }
    
    .ant-btn-primary {
      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);
      border: none;
      box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);
      }
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .cross-date-timeline {
      flex-direction: column;
    }

    .cross-date-task {
      width: 100% !important;
      margin-right: 0 !important;
    }

    .timeline-hours .hour-label {
      width: 70px;
    }

    .timeline-hours .task-slots {
      left: 70px;
    }

    .timeline-hours .hour-line {
      margin-left: 70px;
    }
  }

  @media (max-width: 480px) {
    .cross-date-task {
      padding: 16px;
    }

    .cross-task-title {
      font-size: 14px;
    }

    .timeline-hours .hour-label {
      width: 60px;
      font-size: 12px;
    }

    .timeline-hours .task-slots {
      left: 60px;
    }

    .timeline-hours .hour-line {
      margin-left: 60px;
    }

    .task-card {
      padding: 8px 12px;
    }

    .task-card-content .task-title {
      font-size: 12px;
    }
  }
}
</style>