{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue?vue&type=style&index=0&id=f28d94aa&lang=less&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\WorkOrderTask\\modules\\ProjectDetail.vue", "mtime": 1753772831823}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\css-loader\\index.js", "mtime": 1753423166706}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753423171026}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753423169084}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1753423168554}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n// 基础样式保持不变\r\n.project-detail-app {\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: 100vh;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',\r\n  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n}\r\n\r\n// 多选框换行显示样式\r\n:deep(.multi-select-wrap) {\r\n  .ant-select-selection--multiple {\r\n    .ant-select-selection__rendered {\r\n      height: auto;\r\n      max-height: none;\r\n      overflow-y: auto;\r\n\r\n      // 使标签能够换行显示\r\n      ul {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n      }\r\n\r\n      .ant-select-selection__choice {\r\n        margin-top: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 项目头部样式保持不变...\r\n.project-header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  padding: 32px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    gap: 24px;\r\n  }\r\n\r\n  .project-info {\r\n    flex: 1;\r\n\r\n    .back-btn {\r\n      background: none;\r\n      border: none;\r\n      color: #5a67d8;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      cursor: pointer;\r\n      margin-bottom: 16px;\r\n      padding: 8px 16px;\r\n      border-radius: 10px;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, rgba(90, 103, 216, 0.1), rgba(79, 172, 254, 0.1));\r\n        opacity: 0;\r\n        transition: opacity 0.3s ease;\r\n      }\r\n\r\n      &:hover {\r\n        color: #4c51bf;\r\n        transform: translateX(-3px);\r\n\r\n        &::before {\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .project-title {\r\n      margin: 0 0 16px 0;\r\n      font-size: 32px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      line-height: 1.2;\r\n      letter-spacing: -0.025em;\r\n    }\r\n\r\n    .project-meta {\r\n      display: flex;\r\n      gap: 12px;\r\n      align-items: center;\r\n\r\n      .project-mode {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        color: white;\r\n        padding: 8px 16px;\r\n        border-radius: 20px;\r\n        font-size: 13px;\r\n        font-weight: 600;\r\n        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n  }\r\n\r\n  .header-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n\r\n    .btn-primary, .btn-secondary {\r\n      padding: 12px 24px;\r\n      border: none;\r\n      border-radius: 14px;\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n      overflow: hidden;\r\n      letter-spacing: 0.025em;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        width: 0;\r\n        height: 0;\r\n        background: rgba(255, 255, 255, 0.25);\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        transition: width 0.6s, height 0.6s;\r\n      }\r\n\r\n      &:active::before {\r\n        width: 300px;\r\n        height: 300px;\r\n      }\r\n    }\r\n\r\n    .btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 15px 35px rgba(76, 81, 191, 0.5);\r\n      }\r\n    }\r\n\r\n    .btn-secondary {\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #4a5568;\r\n      border: 1px solid rgba(74, 85, 104, 0.15);\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        background: white;\r\n        border-color: rgba(74, 85, 104, 0.25);\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 筛选区域样式保持不变...\r\n.filter-section {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  overflow: hidden;\r\n\r\n  .filter-content {\r\n    padding: 28px 32px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: flex-end;\r\n      gap: 24px;\r\n    }\r\n\r\n    .filter-group {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 10px;\r\n      min-width: 200px;\r\n\r\n      label {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #4a5568;\r\n        margin-bottom: 4px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .filter-actions {\r\n      display: flex;\r\n      gap: 12px;\r\n      margin-top: 32px;\r\n\r\n      .btn-primary, .btn-ghost {\r\n        padding: 12px 24px;\r\n        border-radius: 12px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        letter-spacing: 0.025em;\r\n      }\r\n\r\n      .btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .btn-ghost {\r\n        background: transparent;\r\n        color: #718096;\r\n        border: 1.5px solid #e2e8f0;\r\n\r\n        &:hover {\r\n          background: #f7fafc;\r\n          border-color: #cbd5e0;\r\n          color: #4a5568;\r\n          transform: translateY(-1px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优化标签页容器\r\n.tabs-container {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  overflow: hidden;\r\n\r\n  .tabs-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n    border-bottom: 1px solid rgba(226, 232, 240, 0.6);\r\n    padding: 0 20px 0 0;\r\n    position: relative;\r\n\r\n    .tabs-left {\r\n      display: flex;\r\n      flex: 1;\r\n\r\n      .tab-btn {\r\n        flex: 1;\r\n        padding: 20px 24px;\r\n        border: none;\r\n        background: none;\r\n        cursor: pointer;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n        color: #64748b;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 10px;\r\n        position: relative;\r\n        letter-spacing: 0.025em;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          inset: 0;\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n          opacity: 0;\r\n          transition: opacity 0.3s ease;\r\n        }\r\n\r\n        &:hover::before {\r\n          opacity: 1;\r\n        }\r\n\r\n        &.active {\r\n          color: #4c51bf;\r\n          background: rgba(255, 255, 255, 0.95);\r\n          font-weight: 700;\r\n\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            width: 50px;\r\n            height: 3px;\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            border-radius: 2px;\r\n            box-shadow: 0 2px 8px rgba(76, 81, 191, 0.3);\r\n          }\r\n        }\r\n\r\n        .tab-count {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          padding: 4px 10px;\r\n          border-radius: 12px;\r\n          font-size: 12px;\r\n          font-weight: 700;\r\n          min-width: 22px;\r\n          text-align: center;\r\n          box-shadow: 0 3px 10px rgba(76, 81, 191, 0.3);\r\n          letter-spacing: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 新增视图切换器\r\n    .view-switcher {\r\n      display: flex;\r\n      gap: 4px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      padding: 6px;\r\n      border-radius: 12px;\r\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n\r\n      .view-btn {\r\n        padding: 10px 12px;\r\n        border: none;\r\n        background: transparent;\r\n        border-radius: 8px;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #64748b;\r\n        font-size: 16px;\r\n\r\n        &:hover {\r\n          background: rgba(76, 81, 191, 0.1);\r\n          color: #4c51bf;\r\n          transform: scale(1.05);\r\n        }\r\n\r\n        &.active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n          transform: scale(1.05);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 表格容器保持原样...\r\n.table-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .table-loading {\r\n    .ant-spin-container {\r\n      transition: all 0.4s ease;\r\n    }\r\n\r\n    &.ant-spin-spinning .ant-spin-container {\r\n      opacity: 0.5;\r\n      filter: blur(2px);\r\n    }\r\n  }\r\n}\r\n\r\n// 月历视图样式\r\n.calendar-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .calendar-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 24px;\r\n    margin-bottom: 28px;\r\n\r\n    .calendar-nav-btn {\r\n      width: 44px;\r\n      height: 44px;\r\n      border: none;\r\n      border-radius: 12px;\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n\r\n    .calendar-title {\r\n      font-size: 24px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      margin: 0;\r\n      min-width: 180px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .calendar-grid {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .calendar-weekdays {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n\r\n      .weekday {\r\n        padding: 16px;\r\n        text-align: center;\r\n        font-weight: 700;\r\n        color: #374151;\r\n        font-size: 14px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .calendar-days {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n\r\n      .calendar-day {\r\n        min-height: 120px;\r\n        border-right: 1px solid #f1f5f9;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        padding: 12px;\r\n        transition: all 0.3s ease;\r\n        position: relative;\r\n\r\n        &:nth-child(7n) {\r\n          border-right: none;\r\n        }\r\n\r\n        &:hover {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        }\r\n\r\n        &.other-month {\r\n          background: #fafafa;\r\n          color: #cbd5e0;\r\n        }\r\n\r\n        &.today {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n\r\n          .day-number {\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            color: white;\r\n            border-radius: 50%;\r\n            width: 28px;\r\n            height: 28px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            font-weight: 700;\r\n          }\r\n        }\r\n\r\n        &.has-tasks {\r\n          .day-number {\r\n            font-weight: 700;\r\n            color: #4c51bf;\r\n          }\r\n        }\r\n\r\n        .day-number {\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .day-tasks {\r\n          .task-item {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);\r\n            border-left: 3px solid #4c51bf;\r\n            padding: 4px 8px;\r\n            margin-bottom: 4px;\r\n            border-radius: 4px;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            font-size: 11px;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(76, 81, 191, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);\r\n              transform: translateX(2px);\r\n            }\r\n\r\n            &.status-pending {\r\n              border-left-color: #ed8936;\r\n              background: rgba(237, 137, 54, 0.1);\r\n            }\r\n\r\n            &.status-working {\r\n              border-left-color: #4299e1;\r\n              background: rgba(66, 153, 225, 0.1);\r\n            }\r\n\r\n            &.status-completed {\r\n              border-left-color: #9f7aea;\r\n              background: rgba(159, 122, 234, 0.1);\r\n            }\r\n\r\n            &.status-approved {\r\n              border-left-color: #48bb78;\r\n              background: rgba(72, 187, 120, 0.1);\r\n            }\r\n\r\n            &.status-rejected {\r\n              border-left-color: #f56565;\r\n              background: rgba(245, 101, 101, 0.1);\r\n            }\r\n\r\n            .task-name {\r\n              display: block;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-handler {\r\n              display: block;\r\n              color: #718096;\r\n              font-size: 10px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n\r\n          .more-tasks {\r\n            color: #4c51bf;\r\n            font-size: 11px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            padding: 2px 4px;\r\n            border-radius: 4px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              background: rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 甘特图视图样式\r\n.gantt-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .gantt-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .gantt-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n\r\n      .gantt-nav-btn {\r\n        width: 40px;\r\n        height: 40px;\r\n        border: none;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .gantt-period {\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        color: #2d3748;\r\n        min-width: 200px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .gantt-content {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .gantt-timeline {\r\n      .timeline-header {\r\n        display: flex;\r\n        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n        border-bottom: 2px solid #e2e8f0;\r\n\r\n        .task-header {\r\n          width: 300px;\r\n          padding: 20px 24px;\r\n          font-weight: 700;\r\n          color: #374151;\r\n          font-size: 14px;\r\n          letter-spacing: 0.025em;\r\n          border-right: 2px solid #e2e8f0;\r\n          display: flex;\r\n          align-items: center;\r\n        }\r\n\r\n        .dates-header {\r\n          flex: 1;\r\n          display: flex;\r\n\r\n          .date-cell {\r\n            flex: 1;\r\n            padding: 12px 8px;\r\n            text-align: center;\r\n            border-right: 1px solid #f1f5f9;\r\n            transition: all 0.3s ease;\r\n\r\n            &.today {\r\n              background: rgba(76, 81, 191, 0.1);\r\n\r\n              .date-day {\r\n                color: #4c51bf;\r\n                font-weight: 700;\r\n              }\r\n            }\r\n\r\n            .date-day {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n            }\r\n\r\n            .date-weekday {\r\n              font-size: 10px;\r\n              color: #718096;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.5px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .timeline-body {\r\n        .gantt-row {\r\n          display: flex;\r\n          border-bottom: 1px solid #f1f5f9;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n          }\r\n\r\n          .task-info {\r\n            width: 300px;\r\n            padding: 20px 24px;\r\n            border-right: 1px solid #f1f5f9;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n\r\n            .task-name {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              margin-bottom: 6px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-meta {\r\n              display: flex;\r\n              gap: 12px;\r\n              align-items: center;\r\n\r\n              .task-handler {\r\n                font-size: 12px;\r\n                color: #718096;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 4px 8px;\r\n                border-radius: 8px;\r\n                font-size: 10px;\r\n                font-weight: 600;\r\n                text-transform: uppercase;\r\n                letter-spacing: 0.5px;\r\n\r\n                &.status-pending {\r\n                  background: rgba(237, 137, 54, 0.1);\r\n                  color: #dd6b20;\r\n                }\r\n\r\n                &.status-working {\r\n                  background: rgba(66, 153, 225, 0.1);\r\n                  color: #3182ce;\r\n                }\r\n\r\n                &.status-completed {\r\n                  background: rgba(159, 122, 234, 0.1);\r\n                  color: #805ad5;\r\n                }\r\n\r\n                &.status-approved {\r\n                  background: rgba(72, 187, 120, 0.1);\r\n                  color: #38a169;\r\n                }\r\n\r\n                &.status-rejected {\r\n                  background: rgba(245, 101, 101, 0.1);\r\n                  color: #e53e3e;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .gantt-bars {\r\n            flex: 1;\r\n            padding: 16px 8px;\r\n            position: relative;\r\n            min-height: 80px;\r\n\r\n            .gantt-bar {\r\n              position: absolute;\r\n              height: 28px;\r\n              top: 50%;\r\n              transform: translateY(-50%);\r\n              border-radius: 14px;\r\n              cursor: pointer;\r\n              transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n              overflow: hidden;\r\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n              &:hover {\r\n                transform: translateY(-50%) scale(1.02);\r\n                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\r\n                z-index: 10;\r\n              }\r\n\r\n              &.status-pending {\r\n                background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              }\r\n\r\n              &.status-working {\r\n                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              }\r\n\r\n              &.status-completed {\r\n                background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              }\r\n\r\n              &.status-approved {\r\n                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              }\r\n\r\n              &.status-rejected {\r\n                background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              }\r\n\r\n              .bar-content {\r\n                height: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 0 12px;\r\n\r\n                .bar-text {\r\n                  color: white;\r\n                  font-size: 12px;\r\n                  font-weight: 600;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 按天视图样式\r\n.daily-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .daily-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .daily-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 24px;\r\n\r\n      .daily-nav-btn {\r\n        width: 44px;\r\n        height: 44px;\r\n        border: none;\r\n        border-radius: 12px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .date-picker-wrapper {\r\n        .daily-date-picker {\r\n          /deep/ .ant-calendar-picker-input {\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            text-align: center;\r\n            border: 2px solid #e2e8f0;\r\n            border-radius: 12px;\r\n            padding: 12px 20px;\r\n            min-width: 200px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover, &:focus {\r\n              border-color: #4c51bf;\r\n              box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 跨日期工单展示区域\r\n  .cross-date-section {\r\n    margin-bottom: 32px;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    border-radius: 20px;\r\n    padding: 24px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    backdrop-filter: blur(20px);\r\n\r\n    .cross-date-header {\r\n      margin-bottom: 20px;\r\n\r\n      .cross-date-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 700;\r\n        color: #1e293b;\r\n\r\n        .title-icon {\r\n          font-size: 24px;\r\n        }\r\n\r\n        .task-count {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: white;\r\n          padding: 6px 12px;\r\n          border-radius: 20px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n          margin-left: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .cross-date-timeline {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 12px;\r\n      align-items: flex-start;\r\n    }\r\n\r\n    .cross-date-task {\r\n      border-radius: 16px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      border: none;\r\n      position: relative;\r\n      overflow: hidden;\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        transform: translateY(-4px);\r\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: inherit;\r\n        filter: brightness(1.1);\r\n        z-index: -1;\r\n      }\r\n    }\r\n\r\n    .cross-task-content {\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .cross-task-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 12px;\r\n      gap: 12px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      color: white;\r\n      line-height: 1.4;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n      flex: 1;\r\n    }\r\n\r\n    .cross-task-status {\r\n      padding: 6px 12px;\r\n      border-radius: 20px;\r\n      font-size: 11px;\r\n      font-weight: 600;\r\n      text-transform: uppercase;\r\n      letter-spacing: 0.5px;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #1e293b;\r\n      white-space: nowrap;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    .cross-task-details {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n    }\r\n\r\n    .cross-task-handler,\r\n    .cross-task-time,\r\n    .cross-task-duration {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      font-size: 13px;\r\n      color: white;\r\n      background: rgba(255, 255, 255, 0.15);\r\n      padding: 6px 12px;\r\n      border-radius: 10px;\r\n      font-weight: 500;\r\n      backdrop-filter: blur(10px);\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n\r\n      i {\r\n        font-size: 14px;\r\n        opacity: 0.9;\r\n      }\r\n    }\r\n  }\r\n\r\n  .daily-content {\r\n    background: white;\r\n    border-radius: 20px;\r\n    overflow: visible;\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n\r\n    .timeline-container {\r\n      position: relative;\r\n      z-index: 1;\r\n      overflow: visible;\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n\r\n      .timeline-hours {\r\n        position: relative;\r\n        z-index: 2;\r\n        overflow: visible;\r\n\r\n        .hour-slot {\r\n          position: relative;\r\n          min-height: 120px; // 增加最小高度，为自适应工单提供更多空间\r\n          height: auto; // 改为auto，允许高度自适应\r\n          border-bottom: 2px solid #e2e8f0;\r\n          display: block;\r\n          overflow: visible; // 确保工单可以溢出时间槽\r\n          z-index: auto;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);\r\n          }\r\n\r\n          &:last-child {\r\n            border-bottom: 2px solid #e2e8f0;\r\n            border-bottom-left-radius: 20px;\r\n            border-bottom-right-radius: 20px;\r\n          }\r\n\r\n          &:first-child {\r\n            border-top-left-radius: 20px;\r\n            border-top-right-radius: 20px;\r\n          }\r\n\r\n          .hour-label {\r\n            width: 90px;\r\n            height: 120px; // 增加高度\r\n            min-height: 120px;\r\n            padding: 0;\r\n            font-size: 14px;\r\n            font-weight: 700;\r\n            color: white;\r\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n            border-right: 3px solid #e2e8f0;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: absolute;\r\n            left: 0;\r\n            top: 0;\r\n            z-index: 10;\r\n            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\r\n\r\n            .hour-text {\r\n              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n              letter-spacing: 0.5px;\r\n            }\r\n          }\r\n\r\n          .hour-line {\r\n            margin-left: 90px;\r\n            min-height: 120px; // 增加高度\r\n            height: auto; // 改为auto\r\n            position: relative;\r\n            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);\r\n            z-index: 1;\r\n\r\n            .half-hour-line {\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #d1d5db 0%, #e5e7eb 100%);\r\n              z-index: 2;\r\n\r\n              &::before {\r\n                content: '';\r\n                position: absolute;\r\n                left: -90px;\r\n                right: 0;\r\n                height: 1px;\r\n                background: linear-gradient(90deg, rgba(209, 213, 219, 0.5) 0%, #d1d5db 100%);\r\n              }\r\n            }\r\n\r\n            .quarter-hour-line {\r\n              position: absolute;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);\r\n              z-index: 2;\r\n              opacity: 0.6;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-slots {\r\n          position: absolute;\r\n          left: 90px;\r\n          right: 0;\r\n          top: 0;\r\n          height: auto; // 改为auto，允许高度自适应\r\n          min-height: 120px; // 更新最小高度\r\n          padding: 0; // 移除padding，让工单从最左侧开始\r\n          overflow: visible; // 确保内容可见\r\n          z-index: 100;\r\n          pointer-events: none;\r\n\r\n          .task-card {\r\n            border-radius: 12px;\r\n            padding: 16px; // 增加内边距确保内容完整显示\r\n            cursor: pointer;\r\n            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);\r\n            border: none;\r\n            font-size: 12px;\r\n            line-height: 1.4;\r\n            overflow: visible; // 改为visible，允许内容自适应\r\n            position: absolute;\r\n            z-index: 1000;\r\n            pointer-events: auto;\r\n            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n            backdrop-filter: blur(10px);\r\n            border-left: 4px solid;\r\n            min-height: 120px; // 增加最小高度确保内容显示\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: flex-start; // 改为flex-start，让内容从顶部开始\r\n            height: auto; // 允许高度自适应\r\n\r\n            &:hover {\r\n              transform: translateY(-2px) scale(1.02);\r\n              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 8px 15px rgba(0, 0, 0, 0.15);\r\n              z-index: 1001;\r\n            }\r\n\r\n            // 右上角状态标签\r\n            .task-status-corner {\r\n              position: absolute;\r\n              top: 8px;\r\n              right: 8px;\r\n              font-size: 9px;\r\n              font-weight: 600;\r\n              padding: 4px 8px;\r\n              border-radius: 10px;\r\n              line-height: 1.1;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.3px;\r\n              background: rgba(255, 255, 255, 0.95);\r\n              color: #1e293b;\r\n              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\r\n              backdrop-filter: blur(10px);\r\n              z-index: 10;\r\n              white-space: nowrap;\r\n              border: 1px solid rgba(255, 255, 255, 0.3);\r\n            }\r\n\r\n            // 多彩工单颜色方案\r\n            &.task-color-blue {\r\n              background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 1) 100%);\r\n              border-left-color: #1d4ed8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\r\n            }\r\n\r\n            &.task-color-purple {\r\n              background: linear-gradient(135deg, rgba(147, 51, 234, 0.95) 0%, rgba(126, 34, 206, 1) 100%);\r\n              border-left-color: #6b21a8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);\r\n            }\r\n\r\n            &.task-color-green {\r\n              background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(21, 128, 61, 1) 100%);\r\n              border-left-color: #14532d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);\r\n            }\r\n\r\n            &.task-color-orange {\r\n              background: linear-gradient(135deg, rgba(249, 115, 22, 0.95) 0%, rgba(234, 88, 12, 1) 100%);\r\n              border-left-color: #9a3412;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);\r\n            }\r\n\r\n            &.task-color-pink {\r\n              background: linear-gradient(135deg, rgba(236, 72, 153, 0.95) 0%, rgba(219, 39, 119, 1) 100%);\r\n              border-left-color: #831843;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);\r\n            }\r\n\r\n            &.task-color-cyan {\r\n              background: linear-gradient(135deg, rgba(6, 182, 212, 0.95) 0%, rgba(8, 145, 178, 1) 100%);\r\n              border-left-color: #164e63;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);\r\n            }\r\n\r\n            &.task-color-red {\r\n              background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 1) 100%);\r\n              border-left-color: #7f1d1d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);\r\n            }\r\n\r\n            &.task-color-indigo {\r\n              background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(79, 70, 229, 1) 100%);\r\n              border-left-color: #312e81;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);\r\n            }\r\n          }\r\n        }\r\n\r\n      // 工单卡片内容样式 - 自适应高度显示，状态移到右上角\r\n      .task-card-content {\r\n        position: relative;\r\n        z-index: 1;\r\n        width: 100%;\r\n        height: auto; // 改为auto，允许高度自适应\r\n        min-height: 100%; // 确保至少填满容器\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: flex-start; // 从顶部开始排列\r\n        gap: 10px; // 增加间距，因为移除了状态标签\r\n        padding: 0; // 移除额外padding，使用卡片的padding\r\n        padding-right: 60px; // 为右上角状态标签留出空间\r\n\r\n        .task-title {\r\n          font-size: 14px; // 增大字体\r\n          font-weight: 700;\r\n          margin-bottom: 0; // 移除margin，使用gap\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          line-height: 1.3; // 增加行高\r\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n          letter-spacing: 0.2px;\r\n          flex-shrink: 0; // 防止标题被压缩\r\n          min-height: 20px; // 确保最小高度\r\n        }\r\n\r\n        .task-meta {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 8px; // 增加间距\r\n          margin-bottom: 0; // 移除margin，使用gap\r\n          font-size: 11px; // 增大字体\r\n          flex-grow: 1; // 占据剩余空间\r\n\r\n          .task-handler,\r\n          .task-time {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px; // 增加间距\r\n            font-weight: 500;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            padding: 8px 12px; // 增加内边距\r\n            border-radius: 8px; // 增大圆角\r\n            backdrop-filter: blur(5px);\r\n            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n            line-height: 1.3; // 增加行高\r\n            min-height: 20px; // 增加最小高度\r\n\r\n            i {\r\n              font-size: 12px; // 增大图标\r\n              opacity: 0.9;\r\n              flex-shrink: 0; // 防止图标被压缩\r\n            }\r\n          }\r\n        }\r\n      }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n  // 跨日期工单颜色样式\r\n  .cross-date-task {\r\n    &.task-color-blue {\r\n      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n    }\r\n\r\n    &.task-color-purple {\r\n      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\r\n    }\r\n\r\n    &.task-color-green {\r\n      background: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n    }\r\n\r\n    &.task-color-orange {\r\n      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\r\n    }\r\n\r\n    &.task-color-pink {\r\n      background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);\r\n    }\r\n\r\n    &.task-color-cyan {\r\n      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);\r\n    }\r\n\r\n    &.task-color-red {\r\n      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\r\n    }\r\n\r\n    &.task-color-indigo {\r\n      background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\r\n    }\r\n  }\r\n\r\n// 确保工单卡片始终在最上层的全局样式\r\n.daily-container .task-card {\r\n  z-index: 9999 !important;\r\n  position: absolute !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;\r\n}\r\n\r\n// 日期详情弹窗样式\r\n.day-tasks-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n\r\n  .day-tasks-list {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n\r\n    .day-task-item {\r\n      padding: 16px;\r\n      border: 1px solid #f1f5f9;\r\n      border-radius: 12px;\r\n      margin-bottom: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        border-color: rgba(76, 81, 191, 0.2);\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n      }\r\n\r\n      .task-main {\r\n        .task-title {\r\n          margin: 0 0 8px 0;\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n        }\r\n\r\n        .task-info-row {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .task-handler {\r\n            color: #718096;\r\n            font-size: 14px;\r\n          }\r\n\r\n          .task-status {\r\n            padding: 4px 12px;\r\n            border-radius: 12px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n\r\n            &.status-pending {\r\n              background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-working {\r\n              background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-completed {\r\n              background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-approved {\r\n              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-rejected {\r\n              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              color: white;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-time {\r\n          color: #4c51bf;\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 其他样式保持不变...\r\n.enhanced-table {\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n  /deep/ .ant-table {\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n\r\n    .ant-table-thead > tr > th {\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n      font-weight: 700;\r\n      font-size: 14px;\r\n      color: #374151;\r\n      padding: 20px 16px;\r\n      text-align: center;\r\n      letter-spacing: 0.025em;\r\n\r\n      &:first-child {\r\n        border-top-left-radius: 16px;\r\n      }\r\n\r\n      &:last-child {\r\n        border-top-right-radius: 16px;\r\n      }\r\n    }\r\n\r\n    .ant-table-tbody > tr {\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        transform: scale(1.001);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);\r\n      }\r\n\r\n      > td {\r\n        padding: 18px 16px;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        font-size: 14px;\r\n        color: #374151;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    .ant-table-pagination {\r\n      margin: 28px 0 0;\r\n      text-align: center;\r\n\r\n      .ant-pagination-item {\r\n        border-radius: 10px;\r\n        border: 1px solid #e2e8f0;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        font-weight: 500;\r\n\r\n        &:hover {\r\n          border-color: #4c51bf;\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 4px 12px rgba(76, 81, 191, 0.15);\r\n        }\r\n\r\n        &.ant-pagination-item-active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          border-color: transparent;\r\n          box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n          a {\r\n            color: white;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优先级标签\r\n.priority-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.priority-0 {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.priority-1 {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.priority-2 {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 状态标签\r\n.status-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.status-pending {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.status-working {\r\n    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);\r\n  }\r\n\r\n  &.status-completed {\r\n    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(159, 122, 234, 0.3);\r\n  }\r\n\r\n  &.status-approved {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.status-rejected {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 工时显示\r\n.work-hour {\r\n  font-weight: 600;\r\n  color: #4c51bf;\r\n  background: rgba(76, 81, 191, 0.1);\r\n  padding: 6px 10px;\r\n  border-radius: 10px;\r\n  font-size: 13px;\r\n  letter-spacing: 0.025em;\r\n}\r\n\r\n// 时间范围显示\r\n.time-range {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n\r\n  .time-start, .time-end {\r\n    font-weight: 600;\r\n    color: #374151;\r\n  }\r\n\r\n  .time-divider {\r\n    color: #9ca3af;\r\n    font-size: 10px;\r\n    text-align: center;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n// 操作按钮\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n\r\n  .action-link {\r\n    padding: 6px 12px;\r\n    border-radius: 10px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    text-decoration: none;\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    letter-spacing: 0.025em;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n      transition: left 0.5s;\r\n    }\r\n\r\n    &:hover::before {\r\n      left: 100%;\r\n    }\r\n\r\n    &.claim {\r\n      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(66, 153, 225, 0.3);\r\n    }\r\n\r\n    &.start, &.restart {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.complete {\r\n      background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(159, 122, 234, 0.3);\r\n    }\r\n\r\n    &.approve {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.reject {\r\n      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(245, 101, 101, 0.3);\r\n    }\r\n\r\n    &.edit {\r\n      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(237, 137, 54, 0.3);\r\n    }\r\n\r\n    &.detail {\r\n      background: linear-gradient(135deg, #718096 0%, #4a5568 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(113, 128, 150, 0.3);\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      filter: brightness(1.05);\r\n      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .action-divider {\r\n    height: 16px;\r\n    margin: 0 4px;\r\n    border-color: #e2e8f0;\r\n  }\r\n}\r\n\r\n// Ant Design 组件样式覆盖\r\n/deep/ .ant-select {\r\n  .ant-select-selection {\r\n    height: 44px;\r\n    border: 1.5px solid #e2e8f0;\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    backdrop-filter: blur(10px);\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n    &:hover {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 4px 20px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    &.ant-select-selection--focused {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    .ant-select-selection__rendered {\r\n      line-height: 40px;\r\n      margin-left: 14px;\r\n      margin-right: 14px;\r\n      color: #374151;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .ant-select-arrow {\r\n      right: 14px;\r\n      color: #4c51bf;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ .ant-modal {\r\n  .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n    padding: 24px 32px;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 700;\r\n      letter-spacing: 0.025em;\r\n    }\r\n  }\r\n\r\n  .ant-modal-close {\r\n    top: 24px;\r\n    right: 32px;\r\n\r\n    .ant-modal-close-x {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .ant-modal-body {\r\n    padding: 32px;\r\n    background: rgba(255, 255, 255, 0.98);\r\n  }\r\n\r\n  .ant-modal-footer {\r\n    padding: 20px 32px 28px;\r\n    text-align: center;\r\n    border-top: 1px solid #f1f5f9;\r\n    background: rgba(255, 255, 255, 0.98);\r\n\r\n    .ant-btn {\r\n      border-radius: 12px;\r\n      font-weight: 600;\r\n      padding: 10px 24px;\r\n      height: auto;\r\n      letter-spacing: 0.025em;\r\n\r\n      &.ant-btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px 10px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .task-info {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-day {\r\n      min-height: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .project-detail-app {\r\n    padding: 16px;\r\n  }\r\n\r\n  .tabs-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    padding: 20px;\r\n\r\n    .tabs-left {\r\n      order: 2;\r\n    }\r\n\r\n    .view-switcher {\r\n      order: 1;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-header {\r\n      .calendar-title {\r\n        font-size: 20px;\r\n        min-width: 150px;\r\n      }\r\n    }\r\n\r\n    .calendar-day {\r\n      min-height: 80px;\r\n      padding: 8px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .gantt-content {\r\n      overflow-x: auto;\r\n    }\r\n\r\n    .task-info {\r\n      width: 200px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .tabs-left {\r\n    flex-direction: column;\r\n\r\n    .tab-btn {\r\n      padding: 12px 16px;\r\n    }\r\n  }\r\n\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .calendar-day {\r\n    min-height: 60px;\r\n    padding: 4px;\r\n\r\n    .task-item {\r\n      font-size: 10px;\r\n      padding: 2px 4px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 完成工单模态框样式\r\n.finish-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n  \r\n  .upload-container {\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    transition: all 0.3s;\r\n    \r\n    &:hover {\r\n      border-color: #1890ff;\r\n    }\r\n  }\r\n  \r\n  .upload-tips {\r\n    margin-top: 8px;\r\n    color: #999;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    text-align: right;\r\n    margin-top: 24px;\r\n    \r\n    .ant-btn {\r\n      margin-left: 8px;\r\n    }\r\n    \r\n    .ant-btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      border: none;\r\n      box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n      \r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式优化\r\n  @media (max-width: 768px) {\r\n    .cross-date-timeline {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .cross-date-task {\r\n      width: 100% !important;\r\n      margin-right: 0 !important;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 70px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 70px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 70px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .cross-date-task {\r\n      padding: 16px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 14px;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 60px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 60px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 60px;\r\n    }\r\n\r\n    .task-card {\r\n      padding: 8px 12px;\r\n    }\r\n\r\n    .task-card-content .task-title {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["ProjectDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq9DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ProjectDetail.vue", "sourceRoot": "src/views/admin/WorkOrderTask/modules", "sourcesContent": ["<template>\r\n  <div class=\"project-detail-app\">\r\n    <!-- 项目信息头部 -->\r\n    <div class=\"project-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"project-info\">\r\n          <button class=\"back-btn\" @click=\"goBack\">\r\n            ← 返回项目列表\r\n          </button>\r\n          <h1 class=\"project-title\">{{ projectInfo.projectName }}</h1>\r\n          <div class=\"project-meta\">\r\n            <span class=\"project-mode\">{{ getModeText(projectInfo.mode) }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <button class=\"btn-secondary\" v-if=\"projectInfo.mode==='hours'\" @click=\"viewProjectStats\">工时统计</button>\r\n          <button class=\"btn-primary\" v-if=\"userRole==='admin'\" @click=\"handleAddTask\">新建工单</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索筛选 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-content\">\r\n        <div class=\"filter-row\">\r\n          <div class=\"filter-group\" v-if=\"userRole === 'admin' && activeTab === 'all'\">\r\n            <label>处理人</label>\r\n            <a-select v-model=\"queryParam.handling\" placeholder=\"请选择处理人\">\r\n              <a-select-option v-for=\"d in projectMemberList\" :key=\"d.id\" :value=\"d.user_id\">\r\n                {{ d.realname }}\r\n              </a-select-option>\r\n            </a-select>\r\n          </div>\r\n\r\n          <div class=\"filter-group\">\r\n            <label>状态</label>\r\n            <a-select \r\n              v-model=\"queryParam.workStatus\" \r\n              placeholder=\"请选择状态\" \r\n              mode=\"multiple\"\r\n              class=\"multi-select-wrap\">\r\n              <a-select-option value=\"pending\">待处理</a-select-option>\r\n              <a-select-option value=\"working\">进行中</a-select-option>\r\n              <a-select-option value=\"completed\">待审核</a-select-option>\r\n              <a-select-option value=\"approved\">已审核</a-select-option>\r\n              <a-select-option value=\"rejected\">已驳回</a-select-option>\r\n            </a-select>\r\n          </div>\r\n\r\n          <div class=\"filter-actions\">\r\n            <button class=\"btn-primary\" @click=\"searchQuery\">搜索</button>\r\n            <button class=\"btn-ghost\" @click=\"searchReset\">重置</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 标签页切换 -->\r\n    <div class=\"tabs-container\">\r\n      <div class=\"tabs-header\">\r\n        <div class=\"tabs-left\">\r\n          <button\r\n            v-for=\"tab in tabs[this.userRole]\"\r\n            :key=\"tab.value\"\r\n            :class=\"['tab-btn', { active: activeTab === tab.value }]\"\r\n            @click=\"switchTab(tab.value)\"\r\n          >\r\n            {{ tab.label }}\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'pool'\">{{ poolTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'my'\">{{ myTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'all'\">{{ allTotal }}</span>\r\n            <span class=\"tab-count\" v-if=\"tab.value === 'audit'\">{{ auditTotal }}</span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 视图切换按钮 - 仅在排期模式和全部工单标签页显示 -->\r\n        <div class=\"view-switcher\" v-if=\"projectInfo.mode === 'timespan' && (activeTab === 'all' || activeTab === 'my')\">\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'table' }]\"\r\n            @click=\"switchView('table')\"\r\n            title=\"表格视图\"\r\n          >\r\n            <i class=\"icon-table\">表格</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'calendar' }]\"\r\n            @click=\"switchView('calendar')\"\r\n            title=\"月历视图\"\r\n          >\r\n            <i class=\"icon-calendar\">月历</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'daily' }]\"\r\n            @click=\"switchView('daily')\"\r\n            title=\"按天视图\"\r\n          >\r\n            <i class=\"icon-daily\">按天</i>\r\n          </button>\r\n          <button\r\n            :class=\"['view-btn', { active: currentView === 'gantt' }]\"\r\n            @click=\"switchView('gantt')\"\r\n            title=\"甘特图\"\r\n          >\r\n            <i class=\"icon-gantt\">甘特图</i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div class=\"table-container\" v-show=\"currentView === 'table'\">\r\n        <a-spin :spinning=\"loading\" class=\"table-loading\">\r\n          <a-table\r\n            ref=\"table\"\r\n            size=\"middle\"\r\n            bordered\r\n            :rowKey=\"record => record.id\"\r\n            :columns=\"dataColumns[projectInfo.mode]\"\r\n            :dataSource=\"dataSource\"\r\n            :pagination=\"ipagination\"\r\n            class=\"enhanced-table\"\r\n            @change=\"handleTableChange\"\r\n          >\r\n            <template slot=\"prioritySlot\" slot-scope=\"text\">\r\n              <span class=\"priority-tag\" :class=\"'priority-' + text\">{{ getPriorityText(text) }}</span>\r\n            </template>\r\n\r\n            <template slot=\"workHourSlot\" slot-scope=\"text\">\r\n              <span class=\"work-hour\">{{ text }}h</span>\r\n            </template>\r\n\r\n            <template slot=\"workStatus\" slot-scope=\"text\">\r\n              <span class=\"status-tag\" :class=\"'status-' + text\">{{ getWorkStatusText(text) }}</span>\r\n            </template>\r\n\r\n            <template slot=\"plannedTimeSlot\" slot-scope=\"text, record\">\r\n              <div class=\"time-range\">\r\n                <div class=\"time-start\">{{ record.startTime }}</div>\r\n                <div class=\"time-divider\">至</div>\r\n                <div class=\"time-end\">{{ record.endTime }}</div>\r\n              </div>\r\n            </template>\r\n\r\n            <template slot=\"reviewTime\" slot-scope=\"text, record\">\r\n              <span v-if=\"record.workStatus === 'approved'\" class=\"status-tag\" :class=\"'status-' + text\">{{ text }}</span>\r\n            </template>\r\n\r\n            <!-- 操作列 -->\r\n            <template slot=\"action\" slot-scope=\"text, record\">\r\n              <div class=\"action-buttons\">\r\n                <a v-if=\"activeTab === 'pool'\" @click=\"claimTask(record)\" class=\"action-link claim\">领取</a>\r\n\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'rejected'\"\r\n                   @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link restart\">重新开始</a>\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'pending'\"\r\n                   @click=\"updateWorkStatus(record.id, 'working')\" class=\"action-link start\">开始</a>\r\n                <a v-if=\"activeTab === 'my' && record.workStatus === 'working'\"\r\n                   @click=\"showFinishModal(record)\" class=\"action-link complete\">结束</a>\r\n\r\n                <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '1')\" class=\"action-link approve\">通过</a>\r\n                <a-divider v-if=\"activeTab === 'audit'\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a v-if=\"activeTab === 'audit'\" @click=\"audit(record.id, '2')\" class=\"action-link reject\">拒绝</a>\r\n\r\n                <a-divider v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a v-if=\"record.initiatorId === currentUserId && (activeTab === 'my' || activeTab === 'pool') && (record.workStatus === 'pending' || record.workStatus === 'rejected')\" @click=\"taskEdit(record)\" class=\"action-link edit\">编辑</a>\r\n\r\n                <a v-if=\"userRole === 'admin' && (activeTab === 'all' && record.workStatus === 'pending')\" @click=\"taskEdit2(record)\" class=\"action-link edit\">转交负责人</a>\r\n\r\n                <a-divider v-if=\"(activeTab !== 'all' && record.workStatus !== 'approved' && (record.workStatus !== 'completed' || activeTab === 'audit'))\" type=\"vertical\" class=\"action-divider\"/>\r\n                <a @click=\"viewTaskDetail(record)\" class=\"action-link detail\">详情</a>\r\n              </div>\r\n            </template>\r\n          </a-table>\r\n        </a-spin>\r\n      </div>\r\n\r\n      <!-- 月历视图 -->\r\n      <div class=\"calendar-container\" v-show=\"currentView === 'calendar'\">\r\n        <div class=\"calendar-header\">\r\n          <button class=\"calendar-nav-btn\" @click=\"previousMonth\">\r\n            <i class=\"icon-prev\">‹</i>\r\n          </button>\r\n          <h3 class=\"calendar-title\">{{ currentMonth.format('YYYY年 MM月') }}</h3>\r\n          <button class=\"calendar-nav-btn\" @click=\"nextMonth\">\r\n            <i class=\"icon-next\">›</i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"calendar-grid\">\r\n          <div class=\"calendar-weekdays\">\r\n            <div class=\"weekday\" v-for=\"day in weekdays\" :key=\"day\">{{ day }}</div>\r\n          </div>\r\n\r\n          <div class=\"calendar-days\">\r\n            <div\r\n              v-for=\"day in calendarDays\"\r\n              :key=\"day.date\"\r\n              :class=\"['calendar-day', {\r\n                'other-month': !day.isCurrentMonth,\r\n                'today': day.isToday,\r\n                'has-tasks': day.tasks.length > 0\r\n              }]\"\r\n            >\r\n              <div class=\"day-number\">{{ day.dayNumber }}</div>\r\n              <div class=\"day-tasks\">\r\n                <div\r\n                  v-for=\"task in day.tasks.slice(0, 3)\"\r\n                  :key=\"task.id\"\r\n                  :class=\"['task-item', 'status-' + task.workStatus]\"\r\n                  @click=\"viewTaskDetail(task)\"\r\n                  :title=\"task.ordername\"\r\n                >\r\n                  <span class=\"task-name\">{{ task.ordername }}</span>\r\n                  <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5) }}</span>\r\n                </div>\r\n                <div v-if=\"day.tasks.length > 3\" class=\"more-tasks\" @click=\"showDayTasks(day)\">\r\n                  +{{ day.tasks.length - 3 }} 更多\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 甘特图视图 -->\r\n      <div class=\"gantt-container\" v-show=\"currentView === 'gantt'\">\r\n        <div class=\"gantt-header\">\r\n          <div class=\"gantt-controls\">\r\n            <button class=\"gantt-nav-btn\" @click=\"previousGanttPeriod\">\r\n              <i class=\"icon-prev\">‹</i>\r\n            </button>\r\n            <span class=\"gantt-period\">{{ ganttPeriod.start.format('MM/DD') }} - {{ ganttPeriod.end.format('MM/DD') }}</span>\r\n            <button class=\"gantt-nav-btn\" @click=\"nextGanttPeriod\">\r\n              <i class=\"icon-next\">›</i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"gantt-content\">\r\n          <div class=\"gantt-timeline\">\r\n            <div class=\"timeline-header\">\r\n              <div class=\"task-header\">工单</div>\r\n              <div class=\"dates-header\">\r\n                <div\r\n                  v-for=\"date in ganttDates\"\r\n                  :key=\"date.format('YYYY-MM-DD')\"\r\n                  :class=\"['date-cell', { 'today': date.isSame(moment(), 'day') }]\"\r\n                >\r\n                  <div class=\"date-day\">{{ date.format('DD') }}</div>\r\n                  <div class=\"date-weekday\">{{ date.format('ddd') }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"timeline-body\">\r\n              <div\r\n                v-for=\"task in ganttTasks\"\r\n                :key=\"task.id\"\r\n                class=\"gantt-row\"\r\n              >\r\n                <div class=\"task-info\">\r\n                  <div class=\"task-name\" :title=\"task.ordername\">{{ task.ordername }}</div>\r\n                  <div class=\"task-meta\">\r\n                    <span class=\"task-handler\">{{ task.handling + ' ' + task.startTime.substr(11, 5) + '-' + task.endTime.substr(11, 5)}}</span>\r\n                    <span :class=\"['task-status', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"gantt-bars\">\r\n                  <div\r\n                    :class=\"['gantt-bar', 'status-' + task.workStatus]\"\r\n                    :style=\"getGanttBarStyle(task)\"\r\n                    @click=\"viewTaskDetail(task)\"\r\n                    :title=\"`${task.ordername} (${task.startTime} - ${task.endTime})`\"\r\n                  >\r\n                    <div class=\"bar-content\">\r\n                      <span class=\"bar-text\">{{ task.ordername }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 按天视图 -->\r\n      <div class=\"daily-container\" v-show=\"currentView === 'daily'\">\r\n        <div class=\"daily-header\">\r\n          <div class=\"daily-controls\">\r\n            <button class=\"daily-nav-btn\" @click=\"previousDay\">\r\n              <i class=\"icon-prev\">‹</i>\r\n            </button>\r\n            <div class=\"date-picker-wrapper\">\r\n              <a-date-picker\r\n                v-model=\"currentDay\"\r\n                :format=\"'YYYY年MM月DD日'\"\r\n                :allowClear=\"false\"\r\n                @change=\"onDayChange\"\r\n                class=\"daily-date-picker\"\r\n              />\r\n            </div>\r\n            <button class=\"daily-nav-btn\" @click=\"nextDay\">\r\n              <i class=\"icon-next\">›</i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"daily-content\">\r\n          <!-- 跨日期工单展示区域 -->\r\n          <div class=\"cross-date-section\" v-if=\"getCrossDayTasksForCurrentDay().length > 0\">\r\n            <div class=\"cross-date-header\">\r\n              <h3 class=\"cross-date-title\">\r\n                <span class=\"title-icon\">📅</span>\r\n                跨日期工单\r\n                <span class=\"task-count\">{{ getCrossDayTasksForCurrentDay().length }}</span>\r\n              </h3>\r\n            </div>\r\n            <div class=\"cross-date-timeline\">\r\n              <div\r\n                v-for=\"(task, index) in getCrossDayTasksForCurrentDay()\"\r\n                :key=\"task.id\"\r\n                :class=\"['cross-date-task', getTaskColorClass(task, index)]\"\r\n                :style=\"getCrossDayTaskStyle(task, index)\"\r\n                @click=\"viewTaskDetail(task)\"\r\n              >\r\n                <div class=\"cross-task-content\">\r\n                  <div class=\"cross-task-header\">\r\n                    <div class=\"cross-task-title\">{{ task.ordername }}</div>\r\n                    <div :class=\"['cross-task-status', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"cross-task-details\">\r\n                    <div class=\"cross-task-handler\">\r\n                      <i class=\"icon-user\">👤</i>\r\n                      {{ task.handling }}\r\n                    </div>\r\n                    <div class=\"cross-task-time\">\r\n                      <i class=\"icon-time\">⏰</i>\r\n                      {{ formatCrossDayTaskTime(task) }}\r\n                    </div>\r\n                    <div class=\"cross-task-duration\">\r\n                      <i class=\"icon-duration\">⏱️</i>\r\n                      {{ getCrossDayTaskDuration(task) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 时间轴容器 -->\r\n          <div class=\"timeline-container\">\r\n            <div class=\"timeline-hours\">\r\n              <div\r\n                v-for=\"hour in timelineHours\"\r\n                :key=\"hour\"\r\n                class=\"hour-slot\"\r\n              >\r\n                <!-- 美观的小时标签 -->\r\n                <div class=\"hour-label\">\r\n                  <div class=\"hour-text\">{{ hour }}:00</div>\r\n                </div>\r\n\r\n                <!-- 时间线区域 -->\r\n                <div class=\"hour-line\">\r\n                  <!-- 半小时刻度线 -->\r\n                  <div class=\"half-hour-line\"></div>\r\n\r\n                  <!-- 15分钟刻度线 -->\r\n                  <div class=\"quarter-hour-line\" style=\"top: 25%\"></div>\r\n                  <div class=\"quarter-hour-line\" style=\"top: 75%\"></div>\r\n                </div>\r\n\r\n                <!-- 工单展示区域 -->\r\n                <div class=\"task-slots\">\r\n                  <div\r\n                    v-for=\"task in getTasksForHour(hour)\"\r\n                    :key=\"task.id\"\r\n                    :class=\"['task-card', getTaskColorClass(task)]\"\r\n                    :style=\"getTaskCardStyle(task)\"\r\n                    @click=\"viewTaskDetail(task)\"\r\n                  >\r\n                    <!-- 状态标签移到右上角 -->\r\n                    <div :class=\"['task-status-corner', 'status-' + task.workStatus]\">\r\n                      {{ getWorkStatusText(task.workStatus) }}\r\n                    </div>\r\n\r\n                    <div class=\"task-card-content\">\r\n                      <div class=\"task-title\">{{ task.ordername }}</div>\r\n                      <div class=\"task-meta\">\r\n                        <div class=\"task-handler\">\r\n                          <i class=\"icon-user\">👤</i>\r\n                          {{ task.handling }}\r\n                        </div>\r\n                        <div class=\"task-time\">\r\n                          <i class=\"icon-time\">⏰</i>\r\n                          {{ formatTaskTime(task) }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 日期详情弹窗 -->\r\n    <a-modal\r\n      v-model=\"dayTasksVisible\"\r\n      :title=\"`${selectedDay ? selectedDay.date : ''} 的工单`\"\r\n      :footer=\"null\"\r\n      width=\"800px\"\r\n      class=\"day-tasks-modal\"\r\n    >\r\n      <div class=\"day-tasks-list\">\r\n        <div\r\n          v-for=\"task in selectedDayTasks\"\r\n          :key=\"task.id\"\r\n          class=\"day-task-item\"\r\n          @click=\"viewTaskDetail(task)\"\r\n        >\r\n          <div class=\"task-main\">\r\n            <h4 class=\"task-title\">{{ task.ordername }}</h4>\r\n            <div class=\"task-info-row\">\r\n              <span class=\"task-handler\">处理人: {{ task.handling }}</span>\r\n              <span :class=\"['task-status', 'status-' + task.workStatus]\">\r\n                {{ getWorkStatusText(task.workStatus) }}\r\n              </span>\r\n            </div>\r\n            <div class=\"task-time\">\r\n              {{ task.startTime }} - {{ task.endTime }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n\r\n    <!-- 其他弹窗 -->\r\n    <div>\r\n      <a-modal\r\n        title=\"工单审核\"\r\n        :visible=\"isShowAudit\"\r\n        @ok=\"audit(null,'3')\"\r\n        @cancel=\"isShowAudit = false\"\r\n        class=\"audit-modal\"\r\n      >\r\n        <a-form-model>\r\n          <a-form-model-item label=\"审核意见\" :labelCol=\"{ span: 5 }\" :wrapperCol=\"{ span: 19 }\">\r\n            <a-textarea :rows=\"3\" v-model=\"auditOpinion\" placeholder=\"请输入审核意见\"></a-textarea>\r\n          </a-form-model-item>\r\n        </a-form-model>\r\n      </a-modal>\r\n    </div>\r\n\r\n    <!-- 工单弹窗 -->\r\n    <work-order-task-modal\r\n      ref=\"taskModal\"\r\n      :project-info=\"projectInfo\"\r\n      @ok=\"taskModalOk\"\r\n    ></work-order-task-modal>\r\n\r\n          <!-- 工单详情弹窗 -->\r\n    <task-detail-modal ref=\"taskDetailModal\"></task-detail-modal>\r\n\r\n    <!-- 工时统计弹窗 -->\r\n    <project-stats-modal ref=\"projectStatsModal\"></project-stats-modal>\r\n    \r\n    <!-- 工单完成弹窗 -->\r\n    <a-modal\r\n      title=\"完成工单\"\r\n      :visible=\"finishModalVisible\"\r\n      :confirmLoading=\"finishConfirmLoading\"\r\n      @cancel=\"finishModalVisible = false\"\r\n      :footer=\"null\"\r\n      width=\"30%\"\r\n      class=\"finish-modal\"\r\n    >\r\n      <a-spin :spinning=\"finishConfirmLoading\">\r\n        <a-form-model :model=\"finishForm\" :label-col=\"{ span: 4 }\" :wrapper-col=\"{ span: 20 }\">\r\n          <a-form-model-item label=\"上传附件\">\r\n            <div class=\"upload-container\" @dragover.prevent @drop.prevent=\"handleDrop\">\r\n              <a-upload\r\n                name=\"file\"\r\n                :action=\"uploadAction\"\r\n                :headers=\"uploadHeaders\"\r\n                :file-list=\"finishForm.fileList\"\r\n                @change=\"handleFileChange\"\r\n                :multiple=\"true\"\r\n                :show-upload-list=\"true\"\r\n                :before-upload=\"beforeUpload\"\r\n              >\r\n                <a-button type=\"dashed\">\r\n                  <a-icon type=\"upload\" /> 点击上传\r\n                </a-button>\r\n                <div class=\"upload-tips\">\r\n                  <span>支持：点击上传、拖拽上传（附件非必选）</span>\r\n                </div>\r\n              </a-upload>\r\n            </div>\r\n          </a-form-model-item>\r\n          \r\n          <a-form-model-item label=\"完成说明\">\r\n            <a-textarea \r\n              :rows=\"4\" \r\n              v-model=\"finishForm.finishDescribe\" \r\n              placeholder=\"请输入完成说明（非必填）\"\r\n            />\r\n          </a-form-model-item>\r\n        </a-form-model>\r\n        \r\n        <div class=\"modal-footer\">\r\n          <a-button @click=\"finishModalVisible = false\">取消</a-button>\r\n          <a-button type=\"primary\" @click=\"handleFinishOk\" :loading=\"finishConfirmLoading\">立即结束</a-button>\r\n        </div>\r\n      </a-spin>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mixinDevice } from '@/utils/mixin'\r\nimport WorkOrderTaskModal from './WorkOrderTaskModal'\r\nimport TaskDetailModal from './TaskDetailModal'\r\nimport ProjectStatsModal from './ProjectStatsModal'\r\nimport moment from 'moment'\r\nimport { getAction, putAction, httpAction } from '@api/manage'\r\nimport { Modal } from 'ant-design-vue';\r\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\r\nimport Vue from 'vue';\r\n\r\nexport default {\r\n  name: 'ProjectDetail',\r\n  mixins: [mixinDevice],\r\n  components: {\r\n    WorkOrderTaskModal,\r\n    TaskDetailModal,\r\n    ProjectStatsModal\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'pool',\r\n      currentView: 'table', // 新增：当前视图类型\r\n      currentUserId: '',\r\n      projectInfo: {},\r\n      projectMemberList: [],\r\n      queryParam: {\r\n        workStatus: []\r\n      },\r\n      uploadAction: `${window._CONFIG['domianURL']}/sys/common/upload`,\r\n      uploadHeaders: {},\r\n      finishModalVisible: false,\r\n      finishConfirmLoading: false,\r\n      currentTask: null,\r\n      finishForm: {\r\n        fileList: [],\r\n        finishDescribe: '',\r\n        finishAnnex: ''\r\n      },\r\n      poolTasks: [],\r\n      myTasks: [],\r\n      workingTimers: new Map(),\r\n\r\n      // 月历相关\r\n      currentMonth: moment(),\r\n      weekdays: ['一', '二', '三', '四', '五', '六','日'],\r\n      dayTasksVisible: false,\r\n      selectedDay: null,\r\n      selectedDayTasks: [],\r\n\r\n      // 甘特图相关\r\n      ganttPeriod: {\r\n        start: moment().startOf('week'),\r\n        end: moment().endOf('week').add(6, 'days')\r\n      },\r\n\r\n      // 按天视图相关\r\n      currentDay: moment(),\r\n      dailyColumnAssignment: null, // 当天工单列分配缓存\r\n      timelineHours: ['00', ...Array.from({ length: 23 }, (_, i) => (i + 1).toString().padStart(2, '0'))], // 0-23小时，完整24小时\r\n\r\n      tabs: {\r\n        'admin' : [\r\n          { value: 'pool', label: '公海池' },\r\n          { value: 'my', label: '我的工单' },\r\n          { value: 'all', label: '全部工单' },\r\n          { value: 'audit', label: '待审核' }\r\n        ],\r\n        'member': [\r\n          { value: 'pool', label: '公海池' },\r\n          { value: 'my', label: '我的工单' }\r\n        ]\r\n      },\r\n      pageNo: 1,\r\n      pageSize: 3,\r\n      total: 0,\r\n      poolTotal: 0,\r\n      myTotal: 0,\r\n      auditTotal: 0,\r\n      allTotal: 0,\r\n      userRole: 'member',\r\n      dataSource: [],\r\n      dataColumns: {\r\n        'hours': [\r\n          {\r\n            title: '工单名称',\r\n            align: 'center',\r\n            dataIndex: 'ordername',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '优先级',\r\n            align: 'center',\r\n            dataIndex: 'priority',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'prioritySlot' }\r\n          },\r\n          {\r\n            title: '处理人',\r\n            align: 'center',\r\n            dataIndex: 'handling',\r\n            width: 120\r\n          },\r\n          {\r\n            title: '工时',\r\n            align: 'center',\r\n            dataIndex: 'estimatedHours',\r\n            width: 120,\r\n            scopedSlots: { customRender: 'workHourSlot' }\r\n          },\r\n          {\r\n            title: '状态',\r\n            align: 'center',\r\n            dataIndex: 'workStatus',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'workStatus' }\r\n          },\r\n          {\r\n            title: '创建时间',\r\n            align: 'center',\r\n            dataIndex: 'createTime',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'createTime' }\r\n          },\r\n          {\r\n            title: '审核意见',\r\n            align: 'center',\r\n            dataIndex: 'reviewComment',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewComment' }\r\n          },\r\n          {\r\n            title: '完成时间',\r\n            align: 'center',\r\n            dataIndex: 'reviewTime',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewTime' }\r\n          },\r\n          {\r\n            title: '操作',\r\n            dataIndex: 'action',\r\n            align: 'center',\r\n            width: 180,\r\n            scopedSlots: { customRender: 'action' }\r\n          }\r\n        ],\r\n        'timespan': [\r\n          {\r\n            title: '工单名称',\r\n            align: 'center',\r\n            dataIndex: 'ordername',\r\n            width: 200\r\n          },\r\n          {\r\n            title: '优先级',\r\n            align: 'center',\r\n            dataIndex: 'priority',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'prioritySlot' }\r\n          },\r\n          {\r\n            title: '处理人',\r\n            align: 'center',\r\n            dataIndex: 'handling',\r\n            width: 120\r\n          },\r\n          {\r\n            title: '计划时间',\r\n            align: 'center',\r\n            width: 120,\r\n            scopedSlots: { customRender: 'plannedTimeSlot' }\r\n          },\r\n          {\r\n            title: '状态',\r\n            align: 'center',\r\n            dataIndex: 'workStatus',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'workStatus' }\r\n          },\r\n          {\r\n            title: '审核意见',\r\n            align: 'center',\r\n            dataIndex: 'reviewComment',\r\n            width: 100,\r\n            scopedSlots: { customRender: 'reviewComment' }\r\n          },\r\n          {\r\n            title: '操作',\r\n            dataIndex: 'action',\r\n            align: 'center',\r\n            width: 180,\r\n            scopedSlots: { customRender: 'action' }\r\n          }\r\n        ]\r\n      },\r\n      loading: false,\r\n      selectedRowKeys: [],\r\n      selectionRows: [],\r\n      ipagination: {\r\n        current: 1,\r\n        pageSize: 15,\r\n        total: 0,\r\n      },\r\n      statusMap: {\r\n        'pending': '待处理',\r\n        'working': '进行中',\r\n        'completed': '待审核',\r\n        'approved': '已审核',\r\n        'rejected': '已驳回'\r\n      },\r\n      progressStageMap: {\r\n        '0': '准备阶段',\r\n        '1': '实施阶段',\r\n        '2': '完成阶段'\r\n      },\r\n      isShowAudit: false,\r\n      auditOpinion: '',\r\n      currentId: '',\r\n      allTasksForView: [],\r\n    }\r\n  },\r\n  computed: {\r\n    projectId() {\r\n      return this.$route.query.id\r\n    },\r\n\r\n    moment() {\r\n      return moment\r\n    },\r\n\r\n    // 月历计算属性\r\n    calendarDays() {\r\n      const startOfMonth = this.currentMonth.clone().startOf('month')\r\n      const endOfMonth = this.currentMonth.clone().endOf('month')\r\n      const startDate = startOfMonth.clone().startOf('week')\r\n      const endDate = endOfMonth.clone().endOf('week')\r\n\r\n      const days = []\r\n      const current = startDate.clone()\r\n\r\n      while (current.isSameOrBefore(endDate)) {\r\n        const dayTasks = this.getTasksForDate(current)\r\n        days.push({\r\n          date: current.format('YYYY-MM-DD'),\r\n          dayNumber: current.date(),\r\n          isCurrentMonth: current.isSame(this.currentMonth, 'month'),\r\n          isToday: current.isSame(moment(), 'day'),\r\n          tasks: dayTasks\r\n        })\r\n        current.add(1, 'day')\r\n      }\r\n\r\n      return days\r\n    },\r\n\r\n    // 甘特图计算属性\r\n    ganttDates() {\r\n      const dates = []\r\n      const current = this.ganttPeriod.start.clone()\r\n\r\n      while (current.isSameOrBefore(this.ganttPeriod.end)) {\r\n        dates.push(current.clone())\r\n        current.add(1, 'day')\r\n      }\r\n\r\n      return dates\r\n    },\r\n\r\n    ganttTasks() {\r\n      // 优先使用当前视图的数据，如果没有则使用表格数据\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n\r\n        return taskStart.isSameOrBefore(this.ganttPeriod.end) &&\r\n          taskEnd.isSameOrAfter(this.ganttPeriod.start)\r\n      })\r\n    }\r\n  },\r\n  created(options) {\r\n    console.log(\"222333444\")\r\n    console.log(options)\r\n    console.log(\"进入人33333333\")\r\n  },\r\n\r\n  mounted() {\r\n    console.log(\"进入人222222222\")\r\n    this.currentUserId = this.$store.getters.userInfo.id;\r\n    this.loadProjectInfo()\r\n    this.loadProjectMember();\r\n    this.loadTasks()\r\n    this.startWorkingTimers()\r\n    \r\n    // 设置上传头部\r\n    const token = Vue.ls.get(ACCESS_TOKEN)\r\n    this.uploadHeaders = { 'X-Access-Token': token }\r\n  },\r\n\r\n  beforeDestroy() {\r\n    this.clearWorkingTimers()\r\n  },\r\n\r\n  methods: {\r\n    // 视图切换\r\n    switchView(view) {\r\n      this.currentView = view\r\n\r\n      // 如果切换到日历或甘特图视图，重新加载当前标签页的数据\r\n      if (view !== 'table') {\r\n        this.loadAllTasksForView()\r\n      }\r\n    },\r\n\r\n    loadAllTasksForView() {\r\n      this.loading = true;\r\n      // 先清空旧数据\r\n      this.allTasksForView = []\r\n\r\n      let params = {...this.queryParam};\r\n      \r\n      // 将状态数组转换为逗号分隔的字符串\r\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\r\n        params.workStatus = params.workStatus.join(',');\r\n      }\r\n      \r\n      params.projectId = this.projectId;\r\n      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));\r\n      params.pageNo = 1;\r\n      params.pageSize = 1000;\r\n\r\n      this.$http.get('/WorkOrderTask/list', { params }).then(res => {\r\n        if (res.success) {\r\n          this.allTasksForView = res.result.data\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    // 月历相关方法\r\n    previousMonth() {\r\n      this.currentMonth = this.currentMonth.clone().subtract(1, 'month')\r\n    },\r\n\r\n    nextMonth() {\r\n      this.currentMonth = this.currentMonth.clone().add(1, 'month')\r\n    },\r\n\r\n    getTasksForDate(date) {\r\n      const dateStr = date.format('YYYY-MM-DD')\r\n      // 优先使用当前视图的数据，如果没有则使用表格数据\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const startDate = moment(task.startTime).format('YYYY-MM-DD')\r\n        const endDate = moment(task.endTime).format('YYYY-MM-DD')\r\n\r\n        return dateStr >= startDate && dateStr <= endDate\r\n      })\r\n    },\r\n\r\n    showDayTasks(day) {\r\n      this.selectedDay = day\r\n      this.selectedDayTasks = day.tasks\r\n      this.dayTasksVisible = true\r\n    },\r\n\r\n    // 甘特图相关方法\r\n    previousGanttPeriod() {\r\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().subtract(7, 'days')\r\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().subtract(7, 'days')\r\n    },\r\n\r\n    nextGanttPeriod() {\r\n      this.ganttPeriod.start = this.ganttPeriod.start.clone().add(7, 'days')\r\n      this.ganttPeriod.end = this.ganttPeriod.end.clone().add(7, 'days')\r\n    },\r\n\r\n    getGanttBarStyle(task) {\r\n      if (!task.startTime || !task.endTime) return { display: 'none' }\r\n\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const periodStart = this.ganttPeriod.start\r\n      const periodEnd = this.ganttPeriod.end\r\n\r\n      // 计算任务在甘特图中的位置\r\n      const totalDays = periodEnd.diff(periodStart, 'days') + 1\r\n      const dayWidth = 100 / totalDays\r\n\r\n      // 计算开始位置\r\n      let startOffset = 0\r\n      if (taskStart.isSameOrAfter(periodStart)) {\r\n        startOffset = taskStart.diff(periodStart, 'days') * dayWidth\r\n      }\r\n\r\n      // 计算宽度\r\n      let width = dayWidth\r\n      if (taskEnd.isAfter(taskStart)) {\r\n        const visibleStart = moment.max(taskStart, periodStart)\r\n        const visibleEnd = moment.min(taskEnd, periodEnd)\r\n        const visibleDays = visibleEnd.diff(visibleStart, 'days') + 1\r\n        width = visibleDays * dayWidth\r\n      }\r\n\r\n      return {\r\n        left: `${startOffset}%`,\r\n        width: `${width}%`\r\n      }\r\n    },\r\n\r\n    // 按天视图相关方法\r\n    previousDay() {\r\n      this.currentDay = this.currentDay.clone().subtract(1, 'day')\r\n      this.clearDailyColumnAssignment()\r\n    },\r\n\r\n    nextDay() {\r\n      this.currentDay = this.currentDay.clone().add(1, 'day')\r\n      this.clearDailyColumnAssignment()\r\n    },\r\n\r\n    onDayChange(date) {\r\n      if (date) {\r\n        this.currentDay = moment(date)\r\n        this.clearDailyColumnAssignment()\r\n      }\r\n    },\r\n\r\n    clearDailyColumnAssignment() {\r\n      // 清除当天的列分配缓存，强制重新计算\r\n      this.dailyColumnAssignment = null\r\n    },\r\n\r\n    getTasksForHour(hour) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      const tasks = tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 关键修改：排除跨日期工单，只显示当天的工单\r\n        if (taskStartDate !== taskEndDate) {\r\n          return false // 跨日期工单不在时间轴内显示\r\n        }\r\n\r\n        // 只处理当天的工单\r\n        if (currentDateStr !== taskStartDate) {\r\n          return false\r\n        }\r\n\r\n        const startHour = taskStart.hour()\r\n        const hourNum = parseInt(hour)\r\n\r\n        // 只在开始小时显示工单\r\n        return hourNum === startHour\r\n      })\r\n\r\n      // 为重叠的工单分配列位置，从最左侧开始\r\n      return this.assignTaskColumnsFromLeft(tasks, hour)\r\n    },\r\n\r\n    // 新的列分配方法：从最左侧开始排列\r\n    assignTaskColumnsFromLeft(tasks, hour) {\r\n      if (tasks.length <= 1) {\r\n        return tasks.map(task => ({ ...task, columnIndex: 0, totalColumns: 1 }))\r\n      }\r\n\r\n      // 简化的列分配：按开始时间排序，从左到右分配\r\n      const sortedTasks = tasks.sort((a, b) => {\r\n        const timeA = moment(a.startTime)\r\n        const timeB = moment(b.startTime)\r\n        return timeA.valueOf() - timeB.valueOf()\r\n      })\r\n\r\n      // 检查时间重叠，分配到不同列\r\n      const columns = []\r\n\r\n      sortedTasks.forEach(task => {\r\n        let assignedColumn = -1\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n\r\n        // 寻找不重叠的列\r\n        for (let colIndex = 0; colIndex < columns.length; colIndex++) {\r\n          let canUseColumn = true\r\n\r\n          for (let existingTask of columns[colIndex]) {\r\n            const existingStart = moment(existingTask.startTime)\r\n            const existingEnd = moment(existingTask.endTime)\r\n\r\n            // 检查时间重叠\r\n            if (taskStart.isBefore(existingEnd) && taskEnd.isAfter(existingStart)) {\r\n              canUseColumn = false\r\n              break\r\n            }\r\n          }\r\n\r\n          if (canUseColumn) {\r\n            assignedColumn = colIndex\r\n            break\r\n          }\r\n        }\r\n\r\n        // 如果没有找到合适的列，创建新列\r\n        if (assignedColumn === -1) {\r\n          assignedColumn = columns.length\r\n          columns.push([])\r\n        }\r\n\r\n        columns[assignedColumn].push(task)\r\n        task.columnIndex = assignedColumn\r\n        task.totalColumns = Math.min(4, columns.length) // 最多4列\r\n      })\r\n\r\n      // 确保所有工单都有相同的总列数\r\n      const totalColumns = Math.min(4, columns.length)\r\n      sortedTasks.forEach(task => {\r\n        task.totalColumns = totalColumns\r\n      })\r\n\r\n      return sortedTasks\r\n    },\r\n\r\n    assignTaskColumns(tasks, hour) {\r\n      // 保留原方法以兼容其他调用\r\n      return this.assignTaskColumnsFromLeft(tasks, hour)\r\n    },\r\n\r\n    performDailyColumnAssignment(allTasks) {\r\n      console.log('开始全局列分配，工单数量:', allTasks.length)\r\n\r\n      // 按开始时间排序所有工单\r\n      const sortedTasks = allTasks.sort((a, b) => {\r\n        const startA = moment(a.startTime)\r\n        const startB = moment(b.startTime)\r\n        return startA.valueOf() - startB.valueOf()\r\n      })\r\n\r\n      const columns = []\r\n\r\n      // 为每个工单分配列，使用更严格的重叠检测\r\n      sortedTasks.forEach((task, index) => {\r\n        let assignedColumn = -1\r\n\r\n        console.log(`处理工单 ${index + 1}: ${task.ordername}`)\r\n        console.log(`工单时间: ${task.startTime} - ${task.endTime}`)\r\n\r\n        // 尝试找到一个不重叠的列\r\n        for (let colIndex = 0; colIndex < columns.length; colIndex++) {\r\n          let canUseColumn = true\r\n\r\n          for (let existingTask of columns[colIndex]) {\r\n            const overlaps = this.tasksOverlapInTime(task, existingTask)\r\n            console.log(`检查与工单 ${existingTask.ordername} 的重叠: ${overlaps}`)\r\n\r\n            if (overlaps) {\r\n              canUseColumn = false\r\n              break\r\n            }\r\n          }\r\n\r\n          if (canUseColumn) {\r\n            assignedColumn = colIndex\r\n            console.log(`分配到列 ${colIndex}`)\r\n            break\r\n          }\r\n        }\r\n\r\n        // 如果没有找到合适的列，创建新列\r\n        if (assignedColumn === -1) {\r\n          assignedColumn = columns.length\r\n          columns.push([])\r\n          console.log(`创建新列 ${assignedColumn}`)\r\n        }\r\n\r\n        columns[assignedColumn].push(task)\r\n        task.columnIndex = assignedColumn\r\n        task.totalColumns = Math.min(4, columns.length) // 最多4列\r\n      })\r\n\r\n      // 确保所有工单都有相同的总列数\r\n      const totalColumns = Math.min(4, columns.length)\r\n      sortedTasks.forEach(task => {\r\n        task.totalColumns = totalColumns\r\n      })\r\n\r\n      console.log(`列分配完成，总列数: ${totalColumns}`)\r\n      console.log('各列工单分布:', columns.map((col, index) => ({\r\n        column: index,\r\n        tasks: col.map(t => t.ordername)\r\n      })))\r\n\r\n      // 缓存当天的列分配结果\r\n      this.dailyColumnAssignment = {\r\n        date: this.currentDay.format('YYYY-MM-DD'),\r\n        tasks: sortedTasks\r\n      }\r\n    },\r\n\r\n    tasksOverlapInTime(task1, task2) {\r\n      // 获取两个工单在当前日期的可见时间范围\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const range1 = this.getTaskVisibleRangeForDate(task1, currentDate)\r\n      const range2 = this.getTaskVisibleRangeForDate(task2, currentDate)\r\n\r\n      if (!range1 || !range2) {\r\n        console.log(`重叠检测失败: range1=${!!range1}, range2=${!!range2}`)\r\n        return false\r\n      }\r\n\r\n      // 改进的重叠检测：增加时间缓冲区，使相近的工单也被认为重叠\r\n      // 这样可以避免视觉上相近的工单在同一列显示\r\n      const bufferMinutes = 30 // 30分钟缓冲区\r\n      const range1StartWithBuffer = range1.start.clone().subtract(bufferMinutes, 'minutes')\r\n      const range1EndWithBuffer = range1.end.clone().add(bufferMinutes, 'minutes')\r\n      const range2StartWithBuffer = range2.start.clone().subtract(bufferMinutes, 'minutes')\r\n      const range2EndWithBuffer = range2.end.clone().add(bufferMinutes, 'minutes')\r\n\r\n      const overlaps = range1StartWithBuffer.isBefore(range2EndWithBuffer) && range2StartWithBuffer.isBefore(range1EndWithBuffer)\r\n\r\n      console.log(`重叠检测详情:`)\r\n      console.log(`  工单1 ${task1.ordername}: ${range1.start.format('HH:mm')} - ${range1.end.format('HH:mm')}`)\r\n      console.log(`  工单2 ${task2.ordername}: ${range2.start.format('HH:mm')} - ${range2.end.format('HH:mm')}`)\r\n      console.log(`  重叠结果: ${overlaps}`)\r\n\r\n      return overlaps\r\n    },\r\n\r\n    getTaskVisibleRangeForDate(task, date) {\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      // 如果工单不在当前日期范围内，返回null\r\n      if (date < taskStartDate || date > taskEndDate) {\r\n        return null\r\n      }\r\n\r\n      // 计算在当前日期的可见时间范围\r\n      const dayStart = moment(`${date} 00:00:00`)\r\n      const dayEnd = moment(`${date} 23:59:59`)\r\n\r\n      let visibleStart, visibleEnd\r\n\r\n      if (date === taskStartDate && date === taskEndDate) {\r\n        // 工单在同一天内\r\n        visibleStart = taskStart\r\n        visibleEnd = taskEnd\r\n      } else if (date === taskStartDate) {\r\n        // 当前日期是工单开始日期，延伸到当天结束\r\n        visibleStart = taskStart\r\n        visibleEnd = dayEnd\r\n      } else if (date === taskEndDate) {\r\n        // 修复：当前日期是工单结束日期，从当天开始到工单结束时间\r\n        // 但是对于按天视图，我们需要考虑工单在结束日期的实际显示需求\r\n        visibleStart = dayStart\r\n        visibleEnd = taskEnd\r\n      } else {\r\n        // 当前日期在工单开始和结束之间，显示全天\r\n        visibleStart = dayStart\r\n        visibleEnd = dayEnd\r\n      }\r\n\r\n      // 调试输出\r\n      console.log(`工单 ${task.ordername} 在 ${date} 的可见范围:`)\r\n      console.log(`  原始时间: ${taskStart.format('YYYY-MM-DD HH:mm')} - ${taskEnd.format('YYYY-MM-DD HH:mm')}`)\r\n      console.log(`  日期分类: 开始=${taskStartDate}, 当前=${date}, 结束=${taskEndDate}`)\r\n      console.log(`  可见范围: ${visibleStart.format('HH:mm')} - ${visibleEnd.format('HH:mm')}`)\r\n\r\n      return {\r\n        start: visibleStart,\r\n        end: visibleEnd\r\n      }\r\n    },\r\n\r\n    getAllTasksForCurrentDay() {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStartDate = moment(task.startTime).format('YYYY-MM-DD')\r\n        const taskEndDate = moment(task.endTime).format('YYYY-MM-DD')\r\n\r\n        // 检查任务是否在当前日期\r\n        return currentDateStr >= taskStartDate && currentDateStr <= taskEndDate\r\n      })\r\n    },\r\n\r\n    findOverlappingTasks(currentTask, allTasks) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const overlapping = []\r\n\r\n      allTasks.forEach(task => {\r\n        if (this.tasksOverlapOnDate(currentTask, task, currentDateStr)) {\r\n          overlapping.push(task.id)\r\n        }\r\n      })\r\n\r\n      return overlapping.sort() // 排序确保一致的列分配\r\n    },\r\n\r\n    getTaskVisibleRange(task) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n      const dayStart = moment(`${currentDateStr} 01:00:00`) // 从1:00开始\r\n      const dayEnd = moment(`${currentDateStr} 23:59:59`)   // 到23:59结束\r\n\r\n      let visibleStart, visibleEnd\r\n\r\n      if (currentDateStr === taskStartDate && currentDateStr === taskEndDate) {\r\n        visibleStart = moment.max(taskStart, dayStart)\r\n        visibleEnd = moment.min(taskEnd, dayEnd)\r\n      } else if (currentDateStr === taskStartDate) {\r\n        visibleStart = moment.max(taskStart, dayStart)\r\n        visibleEnd = dayEnd\r\n      } else if (currentDateStr === taskEndDate) {\r\n        visibleStart = dayStart\r\n        visibleEnd = moment.min(taskEnd, dayEnd)\r\n      } else if (currentDateStr > taskStartDate && currentDateStr < taskEndDate) {\r\n        visibleStart = dayStart\r\n        visibleEnd = dayEnd\r\n      } else {\r\n        return null\r\n      }\r\n\r\n      // 如果可见时间范围无效，返回null\r\n      if (visibleStart.isAfter(visibleEnd)) {\r\n        return null\r\n      }\r\n\r\n      return { start: visibleStart, end: visibleEnd }\r\n    },\r\n\r\n    timeRangesOverlap(range1, range2) {\r\n      // 检查两个时间范围是否重叠\r\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)\r\n    },\r\n\r\n    tasksOverlapOnDate(task1, task2, dateStr) {\r\n      // 计算两个工单在指定日期的时间范围\r\n      const getVisibleRange = (task) => {\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        if (dateStr === taskStartDate && dateStr === taskEndDate) {\r\n          return { start: taskStart, end: taskEnd }\r\n        } else if (dateStr === taskStartDate) {\r\n          return { start: taskStart, end: moment(`${dateStr} 23:59:59`) }\r\n        } else if (dateStr === taskEndDate) {\r\n          return { start: moment(`${dateStr} 00:00:00`), end: taskEnd }\r\n        } else if (dateStr > taskStartDate && dateStr < taskEndDate) {\r\n          return { start: moment(`${dateStr} 00:00:00`), end: moment(`${dateStr} 23:59:59`) }\r\n        }\r\n        return null\r\n      }\r\n\r\n      const range1 = getVisibleRange(task1)\r\n      const range2 = getVisibleRange(task2)\r\n\r\n      if (!range1 || !range2) return false\r\n\r\n      // 检查时间范围是否重叠\r\n      return range1.start.isBefore(range2.end) && range2.start.isBefore(range1.end)\r\n    },\r\n\r\n    getTaskCardStyle(task) {\r\n      if (!task.startTime || !task.endTime) return {}\r\n\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      // 只处理当天的工单（跨日期工单已在上层过滤）\r\n      if (taskStartDate !== taskEndDate || currentDate !== taskStartDate) {\r\n        return { display: 'none' }\r\n      }\r\n\r\n      const startHour = taskStart.hour()\r\n      const startMinute = taskStart.minute()\r\n      const endHour = taskEnd.hour()\r\n      const endMinute = taskEnd.minute()\r\n\r\n      // 计算在120px高度时间槽中的精确位置\r\n      const topOffsetPx = (startMinute / 60) * 120 // 使用120px高度\r\n\r\n      // 注释：不再需要durationMinutes变量，直接计算精确高度\r\n\r\n      // 精确计算高度，确保底部对齐结束时间刻度线\r\n      let heightPx\r\n      if (startHour === endHour) {\r\n        // 同一小时内的工单 - 精确计算到分钟\r\n        const endOffsetPx = (endMinute / 60) * 120\r\n        heightPx = endOffsetPx - topOffsetPx\r\n\r\n        // 确保最小高度能显示内容\r\n        const contentHeight = this.calculateContentHeight()\r\n        heightPx = Math.max(heightPx, contentHeight)\r\n      } else {\r\n        // 跨小时的工单 - 计算到结束时间的精确位置\r\n        const totalHours = endHour - startHour\r\n        const endMinuteOffset = (endMinute / 60) * 120\r\n        heightPx = totalHours * 120 + endMinuteOffset - topOffsetPx\r\n\r\n        // 跨小时工单通常有足够空间显示内容，但仍需检查\r\n        const contentHeight = this.calculateContentHeight()\r\n        if (heightPx < contentHeight) {\r\n          // 如果计算的时间高度不足以显示内容，优先保证内容显示\r\n          heightPx = contentHeight\r\n        }\r\n      }\r\n\r\n      // 计算多列布局，从最左侧开始\r\n      const totalColumns = task.totalColumns || 1\r\n      const columnIndex = task.columnIndex || 0\r\n\r\n      // 优化列宽计算，确保内容完整显示\r\n      const containerWidth = 100 // 容器总宽度百分比\r\n      const columnGap = 2 // 列间距百分比\r\n      const totalGapWidth = (totalColumns - 1) * columnGap\r\n      const availableWidth = containerWidth - totalGapWidth\r\n      const columnWidth = availableWidth / totalColumns\r\n\r\n      // 从最左侧开始排列（0%开始）\r\n      const leftOffset = columnIndex * (columnWidth + columnGap)\r\n\r\n      const finalStyle = {\r\n        top: `${topOffsetPx}px`,\r\n        height: `${heightPx}px`,\r\n        position: 'absolute',\r\n        width: `${Math.max(columnWidth, 20)}%`, // 确保最小宽度\r\n        left: `${leftOffset}%`, // 从最左侧开始\r\n        zIndex: 10001 + columnIndex,\r\n        overflow: 'visible', // 改为visible，允许内容自适应\r\n        minHeight: `${heightPx}px` // 使用计算出的高度作为最小高度\r\n      }\r\n\r\n      return finalStyle\r\n    },\r\n\r\n    // 新增：计算内容所需高度（状态移到右上角后）\r\n    calculateContentHeight() {\r\n      // 基础内容高度计算（根据实际CSS样式）\r\n      const titleHeight = 22 // 标题行高度（14px字体 * 1.3行高）\r\n      const metaItemHeight = 28 // 每个meta项高度（11px字体 + 8px padding * 2 + line-height）\r\n      const cardPadding = 32 // 卡片上下内边距总和（16px * 2）\r\n      const gaps = 20 // 元素间隙总和（10px * 2，因为增加了间距）\r\n\r\n      // 计算meta项数量（处理人 + 时间）\r\n      const metaItems = 2\r\n      const metaHeight = metaItems * metaItemHeight + (metaItems - 1) * 8 // 8px gap between items\r\n\r\n      // 总内容高度（不包括状态标签，因为移到了右上角）\r\n      const totalContentHeight = titleHeight + metaHeight + cardPadding + gaps\r\n\r\n      // 确保最小高度能显示完整内容，并且不小于100px\r\n      return Math.max(totalContentHeight, 100)\r\n    },\r\n\r\n    // 获取当前日期的跨日期工单\r\n    getCrossDayTasksForCurrentDay() {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      return tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 检查是否为跨日期工单且在当前日期范围内\r\n        const isCrossDay = taskStartDate !== taskEndDate\r\n        const isInCurrentDate = currentDateStr >= taskStartDate && currentDateStr <= taskEndDate\r\n\r\n        return isCrossDay && isInCurrentDate\r\n      })\r\n    },\r\n\r\n    // 获取当前日期的当天工单（非跨日期）\r\n    getCurrentDayTasksForHour(hour) {\r\n      const currentDateStr = this.currentDay.format('YYYY-MM-DD')\r\n      const tasksForView = this.allTasksForView || this.dataSource\r\n\r\n      const tasks = tasksForView.filter(task => {\r\n        if (!task.startTime || !task.endTime) return false\r\n\r\n        const taskStart = moment(task.startTime)\r\n        const taskEnd = moment(task.endTime)\r\n        const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n        const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n        // 只显示当天工单（非跨日期）\r\n        const isSameDay = taskStartDate === taskEndDate && taskStartDate === currentDateStr\r\n        if (!isSameDay) return false\r\n\r\n        // 修复：只在工单开始的小时槽显示，避免重复\r\n        const startHour = taskStart.hour()\r\n        const hourNum = parseInt(hour)\r\n\r\n        return hourNum === startHour\r\n      })\r\n\r\n      return this.assignTaskColumns(tasks, hour)\r\n    },\r\n\r\n    // 格式化跨日期工单的时间显示\r\n    formatCrossDayTaskTime(task) {\r\n      const currentDate = this.currentDay.format('YYYY-MM-DD')\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const taskStartDate = taskStart.format('YYYY-MM-DD')\r\n      const taskEndDate = taskEnd.format('YYYY-MM-DD')\r\n\r\n      if (currentDate === taskStartDate && currentDate === taskEndDate) {\r\n        // 同一天（不应该出现在这里）\r\n        return `${taskStart.format('HH:mm')} - ${taskEnd.format('HH:mm')}`\r\n      } else if (currentDate === taskStartDate) {\r\n        // 开始日期\r\n        return `${taskStart.format('HH:mm')} - 次日`\r\n      } else if (currentDate === taskEndDate) {\r\n        // 结束日期\r\n        return `前日 - ${taskEnd.format('HH:mm')}`\r\n      } else {\r\n        // 中间日期\r\n        return `全天`\r\n      }\r\n    },\r\n\r\n    // 计算跨日期工单的持续时间\r\n    getCrossDayTaskDuration(task) {\r\n      const taskStart = moment(task.startTime)\r\n      const taskEnd = moment(task.endTime)\r\n      const duration = moment.duration(taskEnd.diff(taskStart))\r\n\r\n      const days = Math.floor(duration.asDays())\r\n      const hours = duration.hours()\r\n      const minutes = duration.minutes()\r\n\r\n      if (days > 0) {\r\n        return `${days}天${hours}小时`\r\n      } else if (hours > 0) {\r\n        return `${hours}小时${minutes}分钟`\r\n      } else {\r\n        return `${minutes}分钟`\r\n      }\r\n    },\r\n\r\n    // 计算跨日期工单在时间轴上的样式\r\n    getCrossDayTaskStyle(task, index) {\r\n      const totalTasks = this.getCrossDayTasksForCurrentDay().length\r\n      const taskWidth = Math.min(95 / totalTasks, 25) // 最大宽度25%，根据工单数量调整\r\n      const leftOffset = index * (taskWidth + 1.5) // 1.5%间距，增加间距\r\n\r\n      return {\r\n        position: 'relative',\r\n        width: `${taskWidth}%`,\r\n        minWidth: '200px',\r\n        marginRight: '12px',\r\n        marginBottom: '8px',\r\n        display: 'inline-block',\r\n        verticalAlign: 'top',\r\n        zIndex: 100 + index\r\n      }\r\n    },\r\n\r\n    // 获取工单颜色类名\r\n    getTaskColorClass(task, index = 0) {\r\n      // 基于工单状态和索引生成颜色类\r\n      const colorClasses = [\r\n        'task-color-blue',\r\n        'task-color-purple',\r\n        'task-color-green',\r\n        'task-color-orange',\r\n        'task-color-pink',\r\n        'task-color-cyan',\r\n        'task-color-red',\r\n        'task-color-indigo'\r\n      ]\r\n\r\n      // 优先根据状态分配颜色\r\n      const statusColors = {\r\n        'pending': 'task-color-orange',\r\n        'working': 'task-color-blue',\r\n        'completed': 'task-color-purple',\r\n        'approved': 'task-color-green',\r\n        'rejected': 'task-color-red'\r\n      }\r\n\r\n      // 如果有状态对应的颜色，使用状态颜色，否则使用循环颜色\r\n      return statusColors[task.workStatus] || colorClasses[index % colorClasses.length]\r\n    },\r\n\r\n\r\n\r\n    formatTaskTime(task) {\r\n      if (!task.startTime || !task.endTime) return ''\r\n      const start = moment(task.startTime).format('HH:mm')\r\n      const end = moment(task.endTime).format('HH:mm')\r\n      const startDate = moment(task.startTime).format('MM-DD')\r\n      const endDate = moment(task.endTime).format('MM-DD')\r\n\r\n      // 如果跨天，显示日期\r\n      if (startDate !== endDate) {\r\n        return `${startDate} ${start} - ${endDate} ${end}`\r\n      }\r\n      return `${start}-${end}`\r\n    },\r\n\r\n\r\n\r\n    // 原有方法保持不变\r\n    searchQuery(){\r\n      this.loadTasks();\r\n    },\r\n\r\n    searchReset(){\r\n      this.queryParam = {\r\n        workStatus: []\r\n      }\r\n      this.ipagination.current = 1;\r\n    },\r\n\r\n    audit(id, type){\r\n      if(type === '1'){\r\n        Modal.confirm({\r\n          title: '审核确认',\r\n          icon: '',\r\n          content: '确定要通过此条工单吗？',\r\n          onOk: () => {\r\n            this.updateWorkStatus(id, 'approved');\r\n          }\r\n        })\r\n      }else if(type === '2'){\r\n        this.isShowAudit = true;\r\n        this.currentId = id;\r\n      }else if(type === '3'){\r\n        if(this.auditOpinion.length === 0){\r\n          this.$message.error(\"请输入审核意见！\");\r\n          return;\r\n        }\r\n        this.updateWorkStatus(this.currentId, 'rejected');\r\n      }\r\n    },\r\n\r\n    updateWorkStatus(id, status) {\r\n      let params = {}\r\n      params.id = id\r\n      params.workStatus = status\r\n      if(status === 'rejected'){\r\n        params.reviewComment = this.auditOpinion;\r\n      }\r\n      putAction('/WorkOrderTask/edit', params)\r\n        .then(res => {\r\n          if (res.success) {\r\n            this.$message.success('操作成功！')\r\n            this.loadTasks()\r\n            // 如果当前不是表格视图，也要重新加载数据\r\n            if (this.currentView !== 'table') {\r\n              this.loadAllTasksForView()\r\n            }\r\n            this.isShowAudit = false;\r\n          } else {\r\n            this.$message.error('操作失败！')\r\n          }\r\n        })\r\n    },\r\n\r\n    handleTableChange(current, pageSize) {\r\n      this.ipagination = current\r\n      this.loadTasks()\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys, selectionRows) {\r\n      this.selectedRowKeys = selectedRowKeys\r\n      this.selectionRows = selectionRows\r\n    },\r\n\r\n    getWorkStatusText(status) {\r\n      return this.statusMap[status] || '待处理'\r\n    },\r\n\r\n    getProgressStageText(stage) {\r\n      return this.progressStageMap[stage] || '未开始'\r\n    },\r\n\r\n    switchTab(e) {\r\n      this.dataSource = []\r\n      this.loading = true\r\n      this.activeTab = e\r\n\r\n      // 如果切换到不支持多视图的标签页，强制切换回表格视图\r\n      if (e === 'pool' || e === 'audit') {\r\n        this.currentView = 'table'\r\n      }\r\n\r\n      this.searchReset();\r\n      this.loadTasks()\r\n\r\n      // 如果当前不是表格视图，也要重新加载数据\r\n      if (this.currentView !== 'table') {\r\n        // 延迟加载，确保标签切换完成\r\n        this.$nextTick(() => {\r\n          this.loadAllTasksForView()\r\n        })\r\n      }\r\n    },\r\n    loadProjectInfo() {\r\n      getAction(`/project/queryById`, { id: this.projectId }).then(res => {\r\n        if (res.success) {\r\n          this.projectInfo = res.result\r\n        }\r\n      })\r\n    },\r\n\r\n    loadProjectMember() {\r\n      getAction(`/projectMember/list`, { projectId: this.projectId }).then(res => {\r\n        if (res.success) {\r\n          this.projectMemberList = res.result;\r\n          this.projectMemberList.forEach(item => {\r\n            if(item.user_id === this.currentUserId){\r\n              this.userRole = item.role;\r\n              return;\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    loadTasks() {\r\n      this.loading = true;\r\n      let params = {...this.queryParam};\r\n      \r\n      // 将状态数组转换为逗号分隔的字符串\r\n      if (params.workStatus && Array.isArray(params.workStatus) && params.workStatus.length > 0) {\r\n        params.workStatus = params.workStatus.join(',');\r\n      }\r\n      \r\n      params.projectId = this.projectId;\r\n      params.backup1 = this.activeTab === 'pool' ? '' : (this.activeTab === 'my' ? '1' : (this.activeTab === 'all' ? '3' : '2'));\r\n      params.pageNo = this.ipagination.current;\r\n      params.pageSize = this.ipagination.pageSize;\r\n      this.$http.get('/WorkOrderTask/list', {\r\n        params: params\r\n      }).then(res => {\r\n        if (res.success) {\r\n          this.dataSource = res.result.data\r\n          let numJSON = res.result.data2\r\n          this.poolTotal = numJSON.poolTotal\r\n          this.myTotal = numJSON.myTotal\r\n          this.allTotal = numJSON.allTotal\r\n          this.auditTotal = numJSON.auditTotal\r\n          this.ipagination.total = res.result.total\r\n        } else {\r\n          this.dataSource = []\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    handleAddTask() {\r\n      this.$refs.taskModal.add()\r\n    },\r\n\r\n    taskModalOk() {\r\n      this.loadTasks()\r\n      // 如果当前不是表格视图，也要重新加载数据\r\n      if (this.currentView !== 'table') {\r\n        this.loadAllTasksForView()\r\n      }\r\n    },\r\n\r\n    claimTask(task) {\r\n      this.$confirm({\r\n        title: '确认领取',\r\n        content: `确定要领取工单\"${task.ordername}\"吗？`,\r\n        onOk: () => {\r\n          this.$http.post('/WorkOrderTask/claim', {\r\n            id: task.id\r\n          }).then(res => {\r\n            if (res.success) {\r\n              this.$message.success('领取成功')\r\n              this.loadTasks()\r\n              if (this.currentView !== 'table') {\r\n                this.loadAllTasksForView()\r\n              }\r\n            } else {\r\n              this.$message.error('领取失败')\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    startWork(task) {\r\n      this.$http.post('/WorkOrderTask/startWork', {\r\n        id: task.id,\r\n        startTime: new Date()\r\n      }).then(res => {\r\n        if (res.success) {\r\n          this.$message.success('工单已开始')\r\n          task.workStatus = 'working'\r\n          task.actualStartTime = new Date()\r\n          this.startWorkingTimer(task)\r\n        } else {\r\n          this.$message.error('开始失败')\r\n        }\r\n      })\r\n    },\r\n\r\n    showFinishModal(task) {\r\n      this.currentTask = task;\r\n      this.finishForm = {\r\n        fileList: [],\r\n        finishDescribe: '',\r\n        finishAnnex: ''\r\n      };\r\n      this.finishModalVisible = true;\r\n    },\r\n    \r\n    // 处理拖拽上传\r\n    handleDrop(e) {\r\n      e.preventDefault();\r\n      const files = Array.from(e.dataTransfer.files);\r\n      files.forEach(file => {\r\n        this.uploadFile(file);\r\n      });\r\n    },\r\n    \r\n    // 上传前验证\r\n    beforeUpload(file) {\r\n      const isLt100M = file.size / 1024 / 1024 < 100;\r\n      if (!isLt100M) {\r\n        this.$message.error('文件必须小于 100MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    \r\n    // 处理上传变更\r\n    handleFileChange(info) {\r\n      let fileList = [...info.fileList];\r\n      \r\n      // 限制最多5个文件\r\n      fileList = fileList.slice(-5);\r\n      \r\n      // 更新文件状态\r\n      fileList = fileList.map(file => {\r\n        if (file.response) {\r\n          file.url = file.response.message;\r\n        }\r\n        return file;\r\n      });\r\n      \r\n      this.finishForm.fileList = fileList;\r\n      \r\n      // 将文件URL拼接成字符串保存到finishAnnex\r\n      if (fileList.length > 0) {\r\n        this.finishForm.finishAnnex = fileList\r\n          .filter(file => file.status === 'done' && file.response)\r\n          .map(file => file.response.message)\r\n          .join(',');\r\n      } else {\r\n        this.finishForm.finishAnnex = '';\r\n      }\r\n    },\r\n    \r\n    // 完成工单提交\r\n    handleFinishOk() {\r\n      if (!this.currentTask) return;\r\n      \r\n      this.finishConfirmLoading = true;\r\n      const params = {\r\n        id: this.currentTask.id,\r\n        workStatus: 'completed'\r\n      };\r\n      \r\n      // 添加附件和说明字段\r\n      if (this.finishForm.finishAnnex) {\r\n        params.finishAnnex = this.finishForm.finishAnnex;\r\n      }\r\n      \r\n      if (this.finishForm.finishDescribe) {\r\n        params.finishDescribe = this.finishForm.finishDescribe;\r\n      }\r\n\r\n      putAction('/WorkOrderTask/edit', params)\r\n        .then(res => {\r\n          if (res.success) {\r\n            this.$message.success('工单已结束，等待审核！')\r\n            this.finishModalVisible = false;\r\n            this.loadTasks()\r\n            // 如果当前不是表格视图，也要重新加载数据\r\n            if (this.currentView !== 'table') {\r\n              this.loadAllTasksForView()\r\n            }\r\n            this.isShowAudit = false;\r\n          } else {\r\n            this.$message.error(res.message || '结束失败')\r\n          }\r\n        }).finally(() => {\r\n            this.finishConfirmLoading = false;\r\n      });\r\n      \r\n      // httpAction('/WorkOrderTask/endWork', params, 'post').then(res => {\r\n      //   if (res.success) {\r\n      //     this.$message.success('工单已结束，等待审核');\r\n      //     this.currentTask.workStatus = 'completed';\r\n      //     this.currentTask.actualEndTime = endTime;\r\n      //     this.currentTask.actualHours = workingHours;\r\n      //     this.clearWorkingTimer(this.currentTask.id);\r\n      //     this.finishModalVisible = false;\r\n      //     this.loadTasks();\r\n      //   } else {\r\n      //     this.$message.error(res.message || '结束失败');\r\n      //   }\r\n      // }).finally(() => {\r\n      //   this.finishConfirmLoading = false;\r\n      // });\r\n    },\r\n\r\n    viewTaskDetail(task) {\r\n      this.$refs.taskDetailModal.show(task)\r\n    },\r\n\r\n    taskEdit(task) {\r\n      let data = JSON.parse(JSON.stringify(task));\r\n      data.handling = data.handlingId;\r\n      this.$refs.taskModal.show(data);\r\n    },\r\n\r\n    taskEdit2(task) {\r\n      let data = JSON.parse(JSON.stringify(task));\r\n      data.handling = data.handlingId;\r\n      this.$refs.taskModal.show2(data);\r\n    },\r\n\r\n    viewProjectStats() {\r\n      this.$refs.projectStatsModal.show(this.projectInfo)\r\n    },\r\n\r\n    goBack() {\r\n      this.$router.go(-1)\r\n    },\r\n\r\n    startWorkingTimers() {\r\n      this.myTasks.forEach(task => {\r\n        if (task.workStatus === 'working' && task.actualStartTime) {\r\n          this.startWorkingTimer(task)\r\n        }\r\n      })\r\n    },\r\n\r\n    startWorkingTimer(task) {\r\n      const timer = setInterval(() => {\r\n        this.$forceUpdate()\r\n      }, 1000)\r\n      this.workingTimers.set(task.id, timer)\r\n    },\r\n\r\n    clearWorkingTimer(taskId) {\r\n      const timer = this.workingTimers.get(taskId)\r\n      if (timer) {\r\n        clearInterval(timer)\r\n        this.workingTimers.delete(taskId)\r\n      }\r\n    },\r\n\r\n    clearWorkingTimers() {\r\n      this.workingTimers.forEach(timer => clearInterval(timer))\r\n      this.workingTimers.clear()\r\n    },\r\n\r\n    calculateWorkingHours(startTime, endTime) {\r\n      const start = moment(startTime)\r\n      const end = moment(endTime)\r\n      return Math.round(end.diff(start, 'hours', true) * 100) / 100\r\n    },\r\n\r\n    getWorkingDuration(startTime) {\r\n      const start = moment(startTime)\r\n      const now = moment()\r\n      const duration = moment.duration(now.diff(start))\r\n      const hours = Math.floor(duration.asHours())\r\n      const minutes = duration.minutes()\r\n      return `${hours}h ${minutes}m`\r\n    },\r\n\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '未开始',\r\n        '1': '进行中',\r\n        '2': '已完成',\r\n        '3': '已暂停'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    getStatusClass(status) {\r\n      const classMap = {\r\n        '0': 'status-pending',\r\n        '1': 'status-active',\r\n        '2': 'status-completed',\r\n        '3': 'status-paused'\r\n      }\r\n      return classMap[status] || 'status-pending'\r\n    },\r\n\r\n    getModeText(mode) {\r\n      return mode === 'hours' ? '工时模式' : '排期模式'\r\n    },\r\n\r\n    getPriorityText(priority) {\r\n      const priorityMap = {\r\n        '2': '高',\r\n        '1': '中',\r\n        '0': '低'\r\n      }\r\n      return priorityMap[priority] || '中'\r\n    },\r\n\r\n    getPriorityClass(priority) {\r\n      const classMap = {\r\n        '2': 'priority-high',\r\n        '1': 'priority-medium',\r\n        '0': 'priority-low'\r\n      }\r\n      return classMap[priority] || 'priority-medium'\r\n    },\r\n\r\n    getTaskStatusText(task) {\r\n      const statusMap = {\r\n        'pending': '待开始',\r\n        'working': '进行中',\r\n        'completed': '待审核',\r\n        'approved': '已完成',\r\n        'rejected': '已驳回'\r\n      }\r\n      return statusMap[task.workStatus] || '未知'\r\n    },\r\n\r\n    getTaskStatusClass(task) {\r\n      const classMap = {\r\n        'pending': 'task-pending',\r\n        'working': 'task-working',\r\n        'completed': 'task-completed',\r\n        'approved': 'task-approved',\r\n        'rejected': 'task-rejected'\r\n      }\r\n      return classMap[task.workStatus] || 'task-pending'\r\n    },\r\n\r\n    formatDate(date) {\r\n      if (!date) return '未设置'\r\n      return moment(date).format('MM-DD HH:mm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n// 基础样式保持不变\r\n.project-detail-app {\r\n  padding: 24px;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  min-height: 100vh;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',\r\n  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n}\r\n\r\n// 多选框换行显示样式\r\n:deep(.multi-select-wrap) {\r\n  .ant-select-selection--multiple {\r\n    .ant-select-selection__rendered {\r\n      height: auto;\r\n      max-height: none;\r\n      overflow-y: auto;\r\n\r\n      // 使标签能够换行显示\r\n      ul {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n      }\r\n\r\n      .ant-select-selection__choice {\r\n        margin-top: 4px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 项目头部样式保持不变...\r\n.project-header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  padding: 32px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  .header-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    gap: 24px;\r\n  }\r\n\r\n  .project-info {\r\n    flex: 1;\r\n\r\n    .back-btn {\r\n      background: none;\r\n      border: none;\r\n      color: #5a67d8;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      cursor: pointer;\r\n      margin-bottom: 16px;\r\n      padding: 8px 16px;\r\n      border-radius: 10px;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, rgba(90, 103, 216, 0.1), rgba(79, 172, 254, 0.1));\r\n        opacity: 0;\r\n        transition: opacity 0.3s ease;\r\n      }\r\n\r\n      &:hover {\r\n        color: #4c51bf;\r\n        transform: translateX(-3px);\r\n\r\n        &::before {\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .project-title {\r\n      margin: 0 0 16px 0;\r\n      font-size: 32px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      line-height: 1.2;\r\n      letter-spacing: -0.025em;\r\n    }\r\n\r\n    .project-meta {\r\n      display: flex;\r\n      gap: 12px;\r\n      align-items: center;\r\n\r\n      .project-mode {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        color: white;\r\n        padding: 8px 16px;\r\n        border-radius: 20px;\r\n        font-size: 13px;\r\n        font-weight: 600;\r\n        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n  }\r\n\r\n  .header-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: flex-start;\r\n\r\n    .btn-primary, .btn-secondary {\r\n      padding: 12px 24px;\r\n      border: none;\r\n      border-radius: 14px;\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      position: relative;\r\n      overflow: hidden;\r\n      letter-spacing: 0.025em;\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        width: 0;\r\n        height: 0;\r\n        background: rgba(255, 255, 255, 0.25);\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        transition: width 0.6s, height 0.6s;\r\n      }\r\n\r\n      &:active::before {\r\n        width: 300px;\r\n        height: 300px;\r\n      }\r\n    }\r\n\r\n    .btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 15px 35px rgba(76, 81, 191, 0.5);\r\n      }\r\n    }\r\n\r\n    .btn-secondary {\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #4a5568;\r\n      border: 1px solid rgba(74, 85, 104, 0.15);\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        background: white;\r\n        border-color: rgba(74, 85, 104, 0.25);\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 筛选区域样式保持不变...\r\n.filter-section {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  overflow: hidden;\r\n\r\n  .filter-content {\r\n    padding: 28px 32px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: flex-end;\r\n      gap: 24px;\r\n    }\r\n\r\n    .filter-group {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 10px;\r\n      min-width: 200px;\r\n\r\n      label {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        color: #4a5568;\r\n        margin-bottom: 4px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .filter-actions {\r\n      display: flex;\r\n      gap: 12px;\r\n      margin-top: 32px;\r\n\r\n      .btn-primary, .btn-ghost {\r\n        padding: 12px 24px;\r\n        border-radius: 12px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        letter-spacing: 0.025em;\r\n      }\r\n\r\n      .btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .btn-ghost {\r\n        background: transparent;\r\n        color: #718096;\r\n        border: 1.5px solid #e2e8f0;\r\n\r\n        &:hover {\r\n          background: #f7fafc;\r\n          border-color: #cbd5e0;\r\n          color: #4a5568;\r\n          transform: translateY(-1px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优化标签页容器\r\n.tabs-container {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  overflow: hidden;\r\n\r\n  .tabs-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n    border-bottom: 1px solid rgba(226, 232, 240, 0.6);\r\n    padding: 0 20px 0 0;\r\n    position: relative;\r\n\r\n    .tabs-left {\r\n      display: flex;\r\n      flex: 1;\r\n\r\n      .tab-btn {\r\n        flex: 1;\r\n        padding: 20px 24px;\r\n        border: none;\r\n        background: none;\r\n        cursor: pointer;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n        color: #64748b;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 10px;\r\n        position: relative;\r\n        letter-spacing: 0.025em;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          inset: 0;\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n          opacity: 0;\r\n          transition: opacity 0.3s ease;\r\n        }\r\n\r\n        &:hover::before {\r\n          opacity: 1;\r\n        }\r\n\r\n        &.active {\r\n          color: #4c51bf;\r\n          background: rgba(255, 255, 255, 0.95);\r\n          font-weight: 700;\r\n\r\n          &::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: 0;\r\n            left: 50%;\r\n            transform: translateX(-50%);\r\n            width: 50px;\r\n            height: 3px;\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            border-radius: 2px;\r\n            box-shadow: 0 2px 8px rgba(76, 81, 191, 0.3);\r\n          }\r\n        }\r\n\r\n        .tab-count {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          padding: 4px 10px;\r\n          border-radius: 12px;\r\n          font-size: 12px;\r\n          font-weight: 700;\r\n          min-width: 22px;\r\n          text-align: center;\r\n          box-shadow: 0 3px 10px rgba(76, 81, 191, 0.3);\r\n          letter-spacing: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 新增视图切换器\r\n    .view-switcher {\r\n      display: flex;\r\n      gap: 4px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      padding: 6px;\r\n      border-radius: 12px;\r\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n\r\n      .view-btn {\r\n        padding: 10px 12px;\r\n        border: none;\r\n        background: transparent;\r\n        border-radius: 8px;\r\n        cursor: pointer;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #64748b;\r\n        font-size: 16px;\r\n\r\n        &:hover {\r\n          background: rgba(76, 81, 191, 0.1);\r\n          color: #4c51bf;\r\n          transform: scale(1.05);\r\n        }\r\n\r\n        &.active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          color: white;\r\n          box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n          transform: scale(1.05);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 表格容器保持原样...\r\n.table-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .table-loading {\r\n    .ant-spin-container {\r\n      transition: all 0.4s ease;\r\n    }\r\n\r\n    &.ant-spin-spinning .ant-spin-container {\r\n      opacity: 0.5;\r\n      filter: blur(2px);\r\n    }\r\n  }\r\n}\r\n\r\n// 月历视图样式\r\n.calendar-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .calendar-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 24px;\r\n    margin-bottom: 28px;\r\n\r\n    .calendar-nav-btn {\r\n      width: 44px;\r\n      height: 44px;\r\n      border: none;\r\n      border-radius: 12px;\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      color: white;\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n      &:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n\r\n    .calendar-title {\r\n      font-size: 24px;\r\n      font-weight: 700;\r\n      color: #2d3748;\r\n      margin: 0;\r\n      min-width: 180px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .calendar-grid {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .calendar-weekdays {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n\r\n      .weekday {\r\n        padding: 16px;\r\n        text-align: center;\r\n        font-weight: 700;\r\n        color: #374151;\r\n        font-size: 14px;\r\n        letter-spacing: 0.025em;\r\n      }\r\n    }\r\n\r\n    .calendar-days {\r\n      display: grid;\r\n      grid-template-columns: repeat(7, 1fr);\r\n\r\n      .calendar-day {\r\n        min-height: 120px;\r\n        border-right: 1px solid #f1f5f9;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        padding: 12px;\r\n        transition: all 0.3s ease;\r\n        position: relative;\r\n\r\n        &:nth-child(7n) {\r\n          border-right: none;\r\n        }\r\n\r\n        &:hover {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        }\r\n\r\n        &.other-month {\r\n          background: #fafafa;\r\n          color: #cbd5e0;\r\n        }\r\n\r\n        &.today {\r\n          background: linear-gradient(135deg, rgba(76, 81, 191, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);\r\n\r\n          .day-number {\r\n            background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n            color: white;\r\n            border-radius: 50%;\r\n            width: 28px;\r\n            height: 28px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            font-weight: 700;\r\n          }\r\n        }\r\n\r\n        &.has-tasks {\r\n          .day-number {\r\n            font-weight: 700;\r\n            color: #4c51bf;\r\n          }\r\n        }\r\n\r\n        .day-number {\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .day-tasks {\r\n          .task-item {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);\r\n            border-left: 3px solid #4c51bf;\r\n            padding: 4px 8px;\r\n            margin-bottom: 4px;\r\n            border-radius: 4px;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            font-size: 11px;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(76, 81, 191, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);\r\n              transform: translateX(2px);\r\n            }\r\n\r\n            &.status-pending {\r\n              border-left-color: #ed8936;\r\n              background: rgba(237, 137, 54, 0.1);\r\n            }\r\n\r\n            &.status-working {\r\n              border-left-color: #4299e1;\r\n              background: rgba(66, 153, 225, 0.1);\r\n            }\r\n\r\n            &.status-completed {\r\n              border-left-color: #9f7aea;\r\n              background: rgba(159, 122, 234, 0.1);\r\n            }\r\n\r\n            &.status-approved {\r\n              border-left-color: #48bb78;\r\n              background: rgba(72, 187, 120, 0.1);\r\n            }\r\n\r\n            &.status-rejected {\r\n              border-left-color: #f56565;\r\n              background: rgba(245, 101, 101, 0.1);\r\n            }\r\n\r\n            .task-name {\r\n              display: block;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-handler {\r\n              display: block;\r\n              color: #718096;\r\n              font-size: 10px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n\r\n          .more-tasks {\r\n            color: #4c51bf;\r\n            font-size: 11px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            padding: 2px 4px;\r\n            border-radius: 4px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              background: rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 甘特图视图样式\r\n.gantt-container {\r\n  padding: 28px 32px 32px;\r\n\r\n  .gantt-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .gantt-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n\r\n      .gantt-nav-btn {\r\n        width: 40px;\r\n        height: 40px;\r\n        border: none;\r\n        border-radius: 10px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .gantt-period {\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        color: #2d3748;\r\n        min-width: 200px;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .gantt-content {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n    .gantt-timeline {\r\n      .timeline-header {\r\n        display: flex;\r\n        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n        border-bottom: 2px solid #e2e8f0;\r\n\r\n        .task-header {\r\n          width: 300px;\r\n          padding: 20px 24px;\r\n          font-weight: 700;\r\n          color: #374151;\r\n          font-size: 14px;\r\n          letter-spacing: 0.025em;\r\n          border-right: 2px solid #e2e8f0;\r\n          display: flex;\r\n          align-items: center;\r\n        }\r\n\r\n        .dates-header {\r\n          flex: 1;\r\n          display: flex;\r\n\r\n          .date-cell {\r\n            flex: 1;\r\n            padding: 12px 8px;\r\n            text-align: center;\r\n            border-right: 1px solid #f1f5f9;\r\n            transition: all 0.3s ease;\r\n\r\n            &.today {\r\n              background: rgba(76, 81, 191, 0.1);\r\n\r\n              .date-day {\r\n                color: #4c51bf;\r\n                font-weight: 700;\r\n              }\r\n            }\r\n\r\n            .date-day {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              line-height: 1.2;\r\n            }\r\n\r\n            .date-weekday {\r\n              font-size: 10px;\r\n              color: #718096;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.5px;\r\n              margin-top: 2px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .timeline-body {\r\n        .gantt-row {\r\n          display: flex;\r\n          border-bottom: 1px solid #f1f5f9;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n          }\r\n\r\n          .task-info {\r\n            width: 300px;\r\n            padding: 20px 24px;\r\n            border-right: 1px solid #f1f5f9;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n\r\n            .task-name {\r\n              font-size: 14px;\r\n              font-weight: 600;\r\n              color: #374151;\r\n              margin-bottom: 6px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n\r\n            .task-meta {\r\n              display: flex;\r\n              gap: 12px;\r\n              align-items: center;\r\n\r\n              .task-handler {\r\n                font-size: 12px;\r\n                color: #718096;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 4px 8px;\r\n                border-radius: 8px;\r\n                font-size: 10px;\r\n                font-weight: 600;\r\n                text-transform: uppercase;\r\n                letter-spacing: 0.5px;\r\n\r\n                &.status-pending {\r\n                  background: rgba(237, 137, 54, 0.1);\r\n                  color: #dd6b20;\r\n                }\r\n\r\n                &.status-working {\r\n                  background: rgba(66, 153, 225, 0.1);\r\n                  color: #3182ce;\r\n                }\r\n\r\n                &.status-completed {\r\n                  background: rgba(159, 122, 234, 0.1);\r\n                  color: #805ad5;\r\n                }\r\n\r\n                &.status-approved {\r\n                  background: rgba(72, 187, 120, 0.1);\r\n                  color: #38a169;\r\n                }\r\n\r\n                &.status-rejected {\r\n                  background: rgba(245, 101, 101, 0.1);\r\n                  color: #e53e3e;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .gantt-bars {\r\n            flex: 1;\r\n            padding: 16px 8px;\r\n            position: relative;\r\n            min-height: 80px;\r\n\r\n            .gantt-bar {\r\n              position: absolute;\r\n              height: 28px;\r\n              top: 50%;\r\n              transform: translateY(-50%);\r\n              border-radius: 14px;\r\n              cursor: pointer;\r\n              transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n              overflow: hidden;\r\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n              &:hover {\r\n                transform: translateY(-50%) scale(1.02);\r\n                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\r\n                z-index: 10;\r\n              }\r\n\r\n              &.status-pending {\r\n                background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              }\r\n\r\n              &.status-working {\r\n                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              }\r\n\r\n              &.status-completed {\r\n                background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              }\r\n\r\n              &.status-approved {\r\n                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              }\r\n\r\n              &.status-rejected {\r\n                background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              }\r\n\r\n              .bar-content {\r\n                height: 100%;\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 0 12px;\r\n\r\n                .bar-text {\r\n                  color: white;\r\n                  font-size: 12px;\r\n                  font-weight: 600;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 按天视图样式\r\n.daily-container {\r\n  padding: 28px 32px 32px;\r\n  min-height: 600px;\r\n\r\n  .daily-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 28px;\r\n\r\n    .daily-controls {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 24px;\r\n\r\n      .daily-nav-btn {\r\n        width: 44px;\r\n        height: 44px;\r\n        border: none;\r\n        border-radius: 12px;\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        color: white;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        box-shadow: 0 4px 15px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 8px 25px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n\r\n      .date-picker-wrapper {\r\n        .daily-date-picker {\r\n          /deep/ .ant-calendar-picker-input {\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            text-align: center;\r\n            border: 2px solid #e2e8f0;\r\n            border-radius: 12px;\r\n            padding: 12px 20px;\r\n            min-width: 200px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover, &:focus {\r\n              border-color: #4c51bf;\r\n              box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 跨日期工单展示区域\r\n  .cross-date-section {\r\n    margin-bottom: 32px;\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    border-radius: 20px;\r\n    padding: 24px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    backdrop-filter: blur(20px);\r\n\r\n    .cross-date-header {\r\n      margin-bottom: 20px;\r\n\r\n      .cross-date-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 700;\r\n        color: #1e293b;\r\n\r\n        .title-icon {\r\n          font-size: 24px;\r\n        }\r\n\r\n        .task-count {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: white;\r\n          padding: 6px 12px;\r\n          border-radius: 20px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n          margin-left: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .cross-date-timeline {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 12px;\r\n      align-items: flex-start;\r\n    }\r\n\r\n    .cross-date-task {\r\n      border-radius: 16px;\r\n      padding: 20px;\r\n      cursor: pointer;\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n      border: none;\r\n      position: relative;\r\n      overflow: hidden;\r\n      backdrop-filter: blur(10px);\r\n\r\n      &:hover {\r\n        transform: translateY(-4px);\r\n        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: inherit;\r\n        filter: brightness(1.1);\r\n        z-index: -1;\r\n      }\r\n    }\r\n\r\n    .cross-task-content {\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n\r\n    .cross-task-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 12px;\r\n      gap: 12px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      color: white;\r\n      line-height: 1.4;\r\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n      flex: 1;\r\n    }\r\n\r\n    .cross-task-status {\r\n      padding: 6px 12px;\r\n      border-radius: 20px;\r\n      font-size: 11px;\r\n      font-weight: 600;\r\n      text-transform: uppercase;\r\n      letter-spacing: 0.5px;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      color: #1e293b;\r\n      white-space: nowrap;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    .cross-task-details {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n    }\r\n\r\n    .cross-task-handler,\r\n    .cross-task-time,\r\n    .cross-task-duration {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      font-size: 13px;\r\n      color: white;\r\n      background: rgba(255, 255, 255, 0.15);\r\n      padding: 6px 12px;\r\n      border-radius: 10px;\r\n      font-weight: 500;\r\n      backdrop-filter: blur(10px);\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n\r\n      i {\r\n        font-size: 14px;\r\n        opacity: 0.9;\r\n      }\r\n    }\r\n  }\r\n\r\n  .daily-content {\r\n    background: white;\r\n    border-radius: 20px;\r\n    overflow: visible;\r\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n\r\n    .timeline-container {\r\n      position: relative;\r\n      z-index: 1;\r\n      overflow: visible;\r\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n      border-radius: 20px;\r\n\r\n      .timeline-hours {\r\n        position: relative;\r\n        z-index: 2;\r\n        overflow: visible;\r\n\r\n        .hour-slot {\r\n          position: relative;\r\n          min-height: 120px; // 增加最小高度，为自适应工单提供更多空间\r\n          height: auto; // 改为auto，允许高度自适应\r\n          border-bottom: 2px solid #e2e8f0;\r\n          display: block;\r\n          overflow: visible; // 确保工单可以溢出时间槽\r\n          z-index: auto;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);\r\n          }\r\n\r\n          &:last-child {\r\n            border-bottom: 2px solid #e2e8f0;\r\n            border-bottom-left-radius: 20px;\r\n            border-bottom-right-radius: 20px;\r\n          }\r\n\r\n          &:first-child {\r\n            border-top-left-radius: 20px;\r\n            border-top-right-radius: 20px;\r\n          }\r\n\r\n          .hour-label {\r\n            width: 90px;\r\n            height: 120px; // 增加高度\r\n            min-height: 120px;\r\n            padding: 0;\r\n            font-size: 14px;\r\n            font-weight: 700;\r\n            color: white;\r\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n            border-right: 3px solid #e2e8f0;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            position: absolute;\r\n            left: 0;\r\n            top: 0;\r\n            z-index: 10;\r\n            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\r\n\r\n            .hour-text {\r\n              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n              letter-spacing: 0.5px;\r\n            }\r\n          }\r\n\r\n          .hour-line {\r\n            margin-left: 90px;\r\n            min-height: 120px; // 增加高度\r\n            height: auto; // 改为auto\r\n            position: relative;\r\n            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);\r\n            z-index: 1;\r\n\r\n            .half-hour-line {\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #d1d5db 0%, #e5e7eb 100%);\r\n              z-index: 2;\r\n\r\n              &::before {\r\n                content: '';\r\n                position: absolute;\r\n                left: -90px;\r\n                right: 0;\r\n                height: 1px;\r\n                background: linear-gradient(90deg, rgba(209, 213, 219, 0.5) 0%, #d1d5db 100%);\r\n              }\r\n            }\r\n\r\n            .quarter-hour-line {\r\n              position: absolute;\r\n              left: 0;\r\n              right: 0;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);\r\n              z-index: 2;\r\n              opacity: 0.6;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-slots {\r\n          position: absolute;\r\n          left: 90px;\r\n          right: 0;\r\n          top: 0;\r\n          height: auto; // 改为auto，允许高度自适应\r\n          min-height: 120px; // 更新最小高度\r\n          padding: 0; // 移除padding，让工单从最左侧开始\r\n          overflow: visible; // 确保内容可见\r\n          z-index: 100;\r\n          pointer-events: none;\r\n\r\n          .task-card {\r\n            border-radius: 12px;\r\n            padding: 16px; // 增加内边距确保内容完整显示\r\n            cursor: pointer;\r\n            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);\r\n            border: none;\r\n            font-size: 12px;\r\n            line-height: 1.4;\r\n            overflow: visible; // 改为visible，允许内容自适应\r\n            position: absolute;\r\n            z-index: 1000;\r\n            pointer-events: auto;\r\n            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n            backdrop-filter: blur(10px);\r\n            border-left: 4px solid;\r\n            min-height: 120px; // 增加最小高度确保内容显示\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: flex-start; // 改为flex-start，让内容从顶部开始\r\n            height: auto; // 允许高度自适应\r\n\r\n            &:hover {\r\n              transform: translateY(-2px) scale(1.02);\r\n              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25), 0 8px 15px rgba(0, 0, 0, 0.15);\r\n              z-index: 1001;\r\n            }\r\n\r\n            // 右上角状态标签\r\n            .task-status-corner {\r\n              position: absolute;\r\n              top: 8px;\r\n              right: 8px;\r\n              font-size: 9px;\r\n              font-weight: 600;\r\n              padding: 4px 8px;\r\n              border-radius: 10px;\r\n              line-height: 1.1;\r\n              text-transform: uppercase;\r\n              letter-spacing: 0.3px;\r\n              background: rgba(255, 255, 255, 0.95);\r\n              color: #1e293b;\r\n              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\r\n              backdrop-filter: blur(10px);\r\n              z-index: 10;\r\n              white-space: nowrap;\r\n              border: 1px solid rgba(255, 255, 255, 0.3);\r\n            }\r\n\r\n            // 多彩工单颜色方案\r\n            &.task-color-blue {\r\n              background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 1) 100%);\r\n              border-left-color: #1d4ed8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\r\n            }\r\n\r\n            &.task-color-purple {\r\n              background: linear-gradient(135deg, rgba(147, 51, 234, 0.95) 0%, rgba(126, 34, 206, 1) 100%);\r\n              border-left-color: #6b21a8;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);\r\n            }\r\n\r\n            &.task-color-green {\r\n              background: linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(21, 128, 61, 1) 100%);\r\n              border-left-color: #14532d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);\r\n            }\r\n\r\n            &.task-color-orange {\r\n              background: linear-gradient(135deg, rgba(249, 115, 22, 0.95) 0%, rgba(234, 88, 12, 1) 100%);\r\n              border-left-color: #9a3412;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);\r\n            }\r\n\r\n            &.task-color-pink {\r\n              background: linear-gradient(135deg, rgba(236, 72, 153, 0.95) 0%, rgba(219, 39, 119, 1) 100%);\r\n              border-left-color: #831843;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);\r\n            }\r\n\r\n            &.task-color-cyan {\r\n              background: linear-gradient(135deg, rgba(6, 182, 212, 0.95) 0%, rgba(8, 145, 178, 1) 100%);\r\n              border-left-color: #164e63;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);\r\n            }\r\n\r\n            &.task-color-red {\r\n              background: linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 1) 100%);\r\n              border-left-color: #7f1d1d;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);\r\n            }\r\n\r\n            &.task-color-indigo {\r\n              background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(79, 70, 229, 1) 100%);\r\n              border-left-color: #312e81;\r\n              color: white;\r\n              box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);\r\n            }\r\n          }\r\n        }\r\n\r\n      // 工单卡片内容样式 - 自适应高度显示，状态移到右上角\r\n      .task-card-content {\r\n        position: relative;\r\n        z-index: 1;\r\n        width: 100%;\r\n        height: auto; // 改为auto，允许高度自适应\r\n        min-height: 100%; // 确保至少填满容器\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: flex-start; // 从顶部开始排列\r\n        gap: 10px; // 增加间距，因为移除了状态标签\r\n        padding: 0; // 移除额外padding，使用卡片的padding\r\n        padding-right: 60px; // 为右上角状态标签留出空间\r\n\r\n        .task-title {\r\n          font-size: 14px; // 增大字体\r\n          font-weight: 700;\r\n          margin-bottom: 0; // 移除margin，使用gap\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          line-height: 1.3; // 增加行高\r\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n          letter-spacing: 0.2px;\r\n          flex-shrink: 0; // 防止标题被压缩\r\n          min-height: 20px; // 确保最小高度\r\n        }\r\n\r\n        .task-meta {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 8px; // 增加间距\r\n          margin-bottom: 0; // 移除margin，使用gap\r\n          font-size: 11px; // 增大字体\r\n          flex-grow: 1; // 占据剩余空间\r\n\r\n          .task-handler,\r\n          .task-time {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px; // 增加间距\r\n            font-weight: 500;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            padding: 8px 12px; // 增加内边距\r\n            border-radius: 8px; // 增大圆角\r\n            backdrop-filter: blur(5px);\r\n            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n            line-height: 1.3; // 增加行高\r\n            min-height: 20px; // 增加最小高度\r\n\r\n            i {\r\n              font-size: 12px; // 增大图标\r\n              opacity: 0.9;\r\n              flex-shrink: 0; // 防止图标被压缩\r\n            }\r\n          }\r\n        }\r\n      }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n  // 跨日期工单颜色样式\r\n  .cross-date-task {\r\n    &.task-color-blue {\r\n      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n    }\r\n\r\n    &.task-color-purple {\r\n      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\r\n    }\r\n\r\n    &.task-color-green {\r\n      background: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n    }\r\n\r\n    &.task-color-orange {\r\n      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\r\n    }\r\n\r\n    &.task-color-pink {\r\n      background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);\r\n    }\r\n\r\n    &.task-color-cyan {\r\n      background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);\r\n    }\r\n\r\n    &.task-color-red {\r\n      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\r\n    }\r\n\r\n    &.task-color-indigo {\r\n      background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\r\n    }\r\n  }\r\n\r\n// 确保工单卡片始终在最上层的全局样式\r\n.daily-container .task-card {\r\n  z-index: 9999 !important;\r\n  position: absolute !important;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1) !important;\r\n}\r\n\r\n// 日期详情弹窗样式\r\n.day-tasks-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n\r\n  .day-tasks-list {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n\r\n    .day-task-item {\r\n      padding: 16px;\r\n      border: 1px solid #f1f5f9;\r\n      border-radius: 12px;\r\n      margin-bottom: 12px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        border-color: rgba(76, 81, 191, 0.2);\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n      }\r\n\r\n      .task-main {\r\n        .task-title {\r\n          margin: 0 0 8px 0;\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          color: #374151;\r\n        }\r\n\r\n        .task-info-row {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .task-handler {\r\n            color: #718096;\r\n            font-size: 14px;\r\n          }\r\n\r\n          .task-status {\r\n            padding: 4px 12px;\r\n            border-radius: 12px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n\r\n            &.status-pending {\r\n              background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-working {\r\n              background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-completed {\r\n              background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-approved {\r\n              background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n              color: white;\r\n            }\r\n\r\n            &.status-rejected {\r\n              background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n              color: white;\r\n            }\r\n          }\r\n        }\r\n\r\n        .task-time {\r\n          color: #4c51bf;\r\n          font-size: 13px;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 其他样式保持不变...\r\n.enhanced-table {\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\r\n\r\n  /deep/ .ant-table {\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n\r\n    .ant-table-thead > tr > th {\r\n      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n      border-bottom: 2px solid #e2e8f0;\r\n      font-weight: 700;\r\n      font-size: 14px;\r\n      color: #374151;\r\n      padding: 20px 16px;\r\n      text-align: center;\r\n      letter-spacing: 0.025em;\r\n\r\n      &:first-child {\r\n        border-top-left-radius: 16px;\r\n      }\r\n\r\n      &:last-child {\r\n        border-top-right-radius: 16px;\r\n      }\r\n    }\r\n\r\n    .ant-table-tbody > tr {\r\n      transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(76, 81, 191, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);\r\n        transform: scale(1.001);\r\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);\r\n      }\r\n\r\n      > td {\r\n        padding: 18px 16px;\r\n        border-bottom: 1px solid #f1f5f9;\r\n        font-size: 14px;\r\n        color: #374151;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    .ant-table-pagination {\r\n      margin: 28px 0 0;\r\n      text-align: center;\r\n\r\n      .ant-pagination-item {\r\n        border-radius: 10px;\r\n        border: 1px solid #e2e8f0;\r\n        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n        font-weight: 500;\r\n\r\n        &:hover {\r\n          border-color: #4c51bf;\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 4px 12px rgba(76, 81, 191, 0.15);\r\n        }\r\n\r\n        &.ant-pagination-item-active {\r\n          background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n          border-color: transparent;\r\n          box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n          a {\r\n            color: white;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优先级标签\r\n.priority-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.priority-0 {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.priority-1 {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.priority-2 {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 状态标签\r\n.status-tag {\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n\r\n  &.status-pending {\r\n    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(237, 137, 54, 0.3);\r\n  }\r\n\r\n  &.status-working {\r\n    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(66, 153, 225, 0.3);\r\n  }\r\n\r\n  &.status-completed {\r\n    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(159, 122, 234, 0.3);\r\n  }\r\n\r\n  &.status-approved {\r\n    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);\r\n  }\r\n\r\n  &.status-rejected {\r\n    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n    color: white;\r\n    box-shadow: 0 3px 10px rgba(245, 101, 101, 0.3);\r\n  }\r\n}\r\n\r\n// 工时显示\r\n.work-hour {\r\n  font-weight: 600;\r\n  color: #4c51bf;\r\n  background: rgba(76, 81, 191, 0.1);\r\n  padding: 6px 10px;\r\n  border-radius: 10px;\r\n  font-size: 13px;\r\n  letter-spacing: 0.025em;\r\n}\r\n\r\n// 时间范围显示\r\n.time-range {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n\r\n  .time-start, .time-end {\r\n    font-weight: 600;\r\n    color: #374151;\r\n  }\r\n\r\n  .time-divider {\r\n    color: #9ca3af;\r\n    font-size: 10px;\r\n    text-align: center;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n// 操作按钮\r\n.action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n\r\n  .action-link {\r\n    padding: 6px 12px;\r\n    border-radius: 10px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    text-decoration: none;\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    letter-spacing: 0.025em;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n      transition: left 0.5s;\r\n    }\r\n\r\n    &:hover::before {\r\n      left: 100%;\r\n    }\r\n\r\n    &.claim {\r\n      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(66, 153, 225, 0.3);\r\n    }\r\n\r\n    &.start, &.restart {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.complete {\r\n      background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(159, 122, 234, 0.3);\r\n    }\r\n\r\n    &.approve {\r\n      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(72, 187, 120, 0.3);\r\n    }\r\n\r\n    &.reject {\r\n      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(245, 101, 101, 0.3);\r\n    }\r\n\r\n    &.edit {\r\n      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(237, 137, 54, 0.3);\r\n    }\r\n\r\n    &.detail {\r\n      background: linear-gradient(135deg, #718096 0%, #4a5568 100%);\r\n      color: white;\r\n      box-shadow: 0 3px 12px rgba(113, 128, 150, 0.3);\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      filter: brightness(1.05);\r\n      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .action-divider {\r\n    height: 16px;\r\n    margin: 0 4px;\r\n    border-color: #e2e8f0;\r\n  }\r\n}\r\n\r\n// Ant Design 组件样式覆盖\r\n/deep/ .ant-select {\r\n  .ant-select-selection {\r\n    height: 44px;\r\n    border: 1.5px solid #e2e8f0;\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    backdrop-filter: blur(10px);\r\n    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\r\n    &:hover {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 4px 20px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    &.ant-select-selection--focused {\r\n      border-color: #4c51bf;\r\n      box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.1);\r\n    }\r\n\r\n    .ant-select-selection__rendered {\r\n      line-height: 40px;\r\n      margin-left: 14px;\r\n      margin-right: 14px;\r\n      color: #374151;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .ant-select-arrow {\r\n      right: 14px;\r\n      color: #4c51bf;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ .ant-modal {\r\n  .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n    padding: 24px 32px;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 700;\r\n      letter-spacing: 0.025em;\r\n    }\r\n  }\r\n\r\n  .ant-modal-close {\r\n    top: 24px;\r\n    right: 32px;\r\n\r\n    .ant-modal-close-x {\r\n      color: white;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .ant-modal-body {\r\n    padding: 32px;\r\n    background: rgba(255, 255, 255, 0.98);\r\n  }\r\n\r\n  .ant-modal-footer {\r\n    padding: 20px 32px 28px;\r\n    text-align: center;\r\n    border-top: 1px solid #f1f5f9;\r\n    background: rgba(255, 255, 255, 0.98);\r\n\r\n    .ant-btn {\r\n      border-radius: 12px;\r\n      font-weight: 600;\r\n      padding: 10px 24px;\r\n      height: auto;\r\n      letter-spacing: 0.025em;\r\n\r\n      &.ant-btn-primary {\r\n        background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n        border: none;\r\n        box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n\r\n        &:hover {\r\n          transform: translateY(-1px);\r\n          box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px 10px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .task-info {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-day {\r\n      min-height: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .project-detail-app {\r\n    padding: 16px;\r\n  }\r\n\r\n  .tabs-header {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    padding: 20px;\r\n\r\n    .tabs-left {\r\n      order: 2;\r\n    }\r\n\r\n    .view-switcher {\r\n      order: 1;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  .calendar-container {\r\n    .calendar-header {\r\n      .calendar-title {\r\n        font-size: 20px;\r\n        min-width: 150px;\r\n      }\r\n    }\r\n\r\n    .calendar-day {\r\n      min-height: 80px;\r\n      padding: 8px;\r\n    }\r\n  }\r\n\r\n  .gantt-container {\r\n    .gantt-content {\r\n      overflow-x: auto;\r\n    }\r\n\r\n    .task-info {\r\n      width: 200px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .tabs-left {\r\n    flex-direction: column;\r\n\r\n    .tab-btn {\r\n      padding: 12px 16px;\r\n    }\r\n  }\r\n\r\n  .view-switcher {\r\n    .view-btn {\r\n      padding: 8px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .calendar-day {\r\n    min-height: 60px;\r\n    padding: 4px;\r\n\r\n    .task-item {\r\n      font-size: 10px;\r\n      padding: 2px 4px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 完成工单模态框样式\r\n.finish-modal {\r\n  /deep/ .ant-modal-content {\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  /deep/ .ant-modal-header {\r\n    background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n    border-bottom: none;\r\n\r\n    .ant-modal-title {\r\n      color: white;\r\n      font-weight: 700;\r\n    }\r\n  }\r\n\r\n  /deep/ .ant-modal-close {\r\n    .ant-modal-close-x {\r\n      color: white;\r\n    }\r\n  }\r\n  \r\n  .upload-container {\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    padding: 16px;\r\n    transition: all 0.3s;\r\n    \r\n    &:hover {\r\n      border-color: #1890ff;\r\n    }\r\n  }\r\n  \r\n  .upload-tips {\r\n    margin-top: 8px;\r\n    color: #999;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    text-align: right;\r\n    margin-top: 24px;\r\n    \r\n    .ant-btn {\r\n      margin-left: 8px;\r\n    }\r\n    \r\n    .ant-btn-primary {\r\n      background: linear-gradient(135deg, #4c51bf 0%, #667eea 100%);\r\n      border: none;\r\n      box-shadow: 0 6px 20px rgba(76, 81, 191, 0.3);\r\n      \r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 10px 30px rgba(76, 81, 191, 0.4);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 响应式优化\r\n  @media (max-width: 768px) {\r\n    .cross-date-timeline {\r\n      flex-direction: column;\r\n    }\r\n\r\n    .cross-date-task {\r\n      width: 100% !important;\r\n      margin-right: 0 !important;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 70px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 70px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 70px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .cross-date-task {\r\n      padding: 16px;\r\n    }\r\n\r\n    .cross-task-title {\r\n      font-size: 14px;\r\n    }\r\n\r\n    .timeline-hours .hour-label {\r\n      width: 60px;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .timeline-hours .task-slots {\r\n      left: 60px;\r\n    }\r\n\r\n    .timeline-hours .hour-line {\r\n      margin-left: 60px;\r\n    }\r\n\r\n    .task-card {\r\n      padding: 8px 12px;\r\n    }\r\n\r\n    .task-card-content .task-title {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}